# Dokumentasi Sistem Kalkulasi Hadiah Purchase Order (PO)

## 📋 Overview

Sistem ini menghitung dan mendistribusikan hadiah produk berdasarkan Purchase Order (PO) dengan menggunakan rasio pembelian yang telah ditentukan.

## 🎯 Konsep Dasar

### Rasio Program Hadiah
- **Rasio 5:1** = Setiap 500L pembelian mendapat 100L hadiah
- **Syarat Minimal**: 500L total pembelian
- **Hadiah per Syarat**: 100L

### Distribusi Proporsional
Hadiah dibagi berdasarkan kontribusi pembelian masing-masing produk terhadap total PO.

## ⚙️ Algoritma Sistem

### 1. Validasi Syarat Minimal
```
IF Total_Pembelian >= Syarat_Minimal THEN
    Lanjut ke step 2
ELSE
    Tidak dapat hadiah
END IF
```

### 2. Kalkulasi Total Hadiah
```
Kelipatan = Total_Pembelian ÷ Syarat_Minimal (dibulatkan ke bawah)
Total_Hadiah = Kelipatan × Hadiah_per_Syarat
```

### 3. Distribusi Proporsional
```
Untuk setiap produk:
    Proporsi = Pembelian_Produk ÷ Total_Pembelian
    Hadiah_Produk = Total_Hadiah × Proporsi
```

### 4. Konversi ke Box
```
Untuk setiap produk:
    Box_Hadiah = Hadiah_Produk ÷ Tonase_per_Box
    Box_Final = ROUND(Box_Hadiah)
```

### 5. Adjustment (jika diperlukan)
```
IF Total_Box_Final ≠ Target_Box THEN
    Sesuaikan berdasarkan selisih pembulatan terkecil
END IF
```

## 📊 Contoh Kasus

### Kasus 1: PO dengan 2 Produk

#### Input Data PO
| No | Nama Produk | Tonase per Box | Qty Box | Total Liter |
|----|-------------|----------------|---------|-------------|
| 1 | Triactive + Dinamec | 20 L | 20 box | 400 L |
| 2 | Triactive | 20 L | 15 box | 300 L |
| **Total** | | | **35 box** | **700 L** |

#### Proses Kalkulasi
| Step | Deskripsi | Kalkulasi | Hasil |
|------|-----------|-----------|-------|
| 1 | Cek Syarat Minimal | 700L ≥ 500L | ✅ Memenuhi |
| 2 | Hitung Kelipatan | 700L ÷ 500L = 1.4 | 1 kelipatan |
| 3 | Total Hadiah | 1 × 100L | **100L** |

#### Distribusi Proporsional
| Produk | Pembelian | Proporsi | Hadiah (L) | Box Kalkulasi | Box Final |
|--------|-----------|----------|------------|---------------|-----------|
| Triactive + Dinamec | 400 L | 400÷700 = 57.14% | 57.14 L | 57.14÷20 = 2.86 | **3 box (60L)** |
| Triactive | 300 L | 300÷700 = 42.86% | 42.86 L | 42.86÷20 = 2.14 | **2 box (40L)** |
| **Total** | **700 L** | **100%** | **100 L** | | **5 box (100L)** |

#### Hasil Akhir Hadiah
| No | Nama Produk | Qty Box Hadiah | Total Liter Hadiah |
|----|-------------|----------------|-------------------|
| 1 | Triactive + Dinamec | **3 box** | **60 L** |
| 2 | Triactive | **2 box** | **40 L** |
| **Total** | | **5 box** | **100 L** |

---

### Kasus 2: PO dengan 3 Produk

#### Input Data PO
| No | Nama Produk | Tonase per Box | Qty Box | Total Liter |
|----|-------------|----------------|---------|-------------|
| 1 | Triactive + Dinamec A | 20 L | 5 box | 100 L |
| 2 | Triactive | 20 L | 15 box | 300 L |
| 3 | Triactive + Dinamec B | 20 L | 5 box | 100 L |
| **Total** | | | **25 box** | **500 L** |

#### Proses Kalkulasi
| Step | Deskripsi | Kalkulasi | Hasil |
|------|-----------|-----------|-------|
| 1 | Cek Syarat Minimal | 500L ≥ 500L | ✅ Tepat memenuhi |
| 2 | Hitung Kelipatan | 500L ÷ 500L = 1.0 | 1 kelipatan |
| 3 | Total Hadiah | 1 × 100L | **100L** |

#### Distribusi Proporsional
| Produk | Pembelian | Proporsi | Hadiah (L) | Box Kalkulasi | Box Final |
|--------|-----------|----------|------------|---------------|-----------|
| Triactive + Dinamec A | 100 L | 100÷500 = 20% | 20 L | 20÷20 = 1.0 | **1 box (20L)** |
| Triactive | 300 L | 300÷500 = 60% | 60 L | 60÷20 = 3.0 | **3 box (60L)** |
| Triactive + Dinamec B | 100 L | 100÷500 = 20% | 20 L | 20÷20 = 1.0 | **1 box (20L)** |
| **Total** | **500 L** | **100%** | **100 L** | | **5 box (100L)** |

#### Hasil Akhir Hadiah
| No | Nama Produk | Qty Box Hadiah | Total Liter Hadiah |
|----|-------------|----------------|-------------------|
| 1 | Triactive + Dinamec A | **1 box** | **20 L** |
| 2 | Triactive | **3 box** | **60 L** |
| 3 | Triactive + Dinamec B | **1 box** | **20 L** |
| **Total** | | **5 box** | **100 L** |

## 🔧 Parameter Konfigurasi

### Parameter yang Dapat Diubah
- **Syarat Minimal**: 200L, 300L, 500L, 1000L, dll
- **Hadiah per Syarat**: 40L, 50L, 100L, 150L, dll
- **Rasio Program**: 4:1, 5:1, 6:1, dll
- **Tonase per Box**: Sesuai produk (20L, 25L, 30L, dll)

### Contoh Konfigurasi Program Berbeda
| Program | Syarat Minimal | Hadiah | Rasio |
|---------|----------------|--------|-------|
| Program A | 200L | 40L | 5:1 |
| Program B | 300L | 50L | 6:1 |
| Program C | 500L | 100L | 5:1 |
| Program D | 1000L | 150L | 6.67:1 |

## ⚠️ Aturan Adjustment

### Ketika Total Box Tidak Sesuai Target

**Jika Kelebihan Box:**
1. Hitung selisih pembulatan untuk setiap produk
2. Kurangi 1 box dari produk dengan selisih pembulatan **terkecil**
3. Ulangi sampai total sesuai target

**Jika Kekurangan Box:**
1. Hitung selisih pembulatan untuk setiap produk  
2. Tambah 1 box ke produk dengan selisih pembulatan **terbesar**
3. Ulangi sampai total sesuai target

### Contoh Adjustment
```
Sebelum: Produk A = 2.86 box → 3 box, Produk B = 2.14 box → 2 box
Total: 5 box (target: 4 box) - Kelebihan 1 box

Selisih pembulatan:
- Produk A: 3 - 2.86 = +0.14
- Produk B: 2 - 2.14 = -0.14 (terkecil)

Action: Kurangi 1 box dari Produk B
Hasil: Produk A = 3 box, Produk B = 1 box (Total: 4 box)
```

## 🎯 Keuntungan Sistem

### ✅ Fairness
- Distribusi berdasarkan kontribusi pembelian
- Semakin besar pembelian, semakin besar hadiah

### ✅ Fleksibilitas  
- Parameter dapat dikonfigurasi sesuai program
- Mendukung berbagai jenis produk dan tonase

### ✅ Transparansi
- Algoritma jelas dan dapat diverifikasi
- Customer dapat memahami cara perhitungan

### ✅ Scalability
- Dapat menangani PO dengan banyak produk
- Mudah diterapkan untuk program hadiah berbeda

## 🔍 Verifikasi Hasil

### Checklist Validasi
- [ ] Total pembelian memenuhi syarat minimal
- [ ] Rasio hadiah sesuai dengan program
- [ ] Distribusi proporsional benar (total proporsi = 100%)
- [ ] Konversi ke box menggunakan tonase per-item yang tepat
- [ ] Total hadiah final sesuai target
- [ ] Tidak ada produk yang mendapat hadiah negatif

---

*Dokumentasi ini dapat digunakan sebagai panduan implementasi sistem kalkulasi hadiah PO dengan distribusi proporsional.*
