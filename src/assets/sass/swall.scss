@import "components/variables.custom";

.container_swall_custom {
  * {
    padding: 0 !important;
    margin: 0 !important;
    height: fit-content !important;
  }

  &.with-deny-button {

    .swal2-deny {
      display: block !important;
      border: 1px solid $primary !important;
      background-color: white !important;
    }
  }

  .swal2-html-container {
    margin: 10px 20px !important;
  }

  .swal2-actions {
    margin-top: 30px !important;
    text-align: center !important;
    width: 100% !important;
    display: flex !important;
    flex-direction: row-reverse !important;
    gap: 1rem !important;
    align-items: center !important;
    justify-content: center !important;

    button.swal2-styled {
      text-align: center !important;
      padding: 8px 30px !important;
      align-items: center !important;
      justify-content: center !important;
      flex: 1;
      border-radius: 8px !important;
      outline: none !important;

      &:focus {
        box-shadow: none !important;
      }
    }

    .swal2-deny {
      border: 1px solid $primary !important;
      color: $primary;
    }

  }

  .swal2-popup.swal2-modal.swal2-show {
    padding: 2rem !important;
    display: flex !important;
    align-items: center !important;
    flex-direction: column !important;
    width: 570px !important;
  }

  .icon_swall_customs.swal2-icon {
    margin: 0 !important;
    border-color: transparent !important;
    height: auto !important;
    width: auto !important;
  }

  .html_container_swall_custom {
    padding: 0 !important;
    margin-top: 20px !important;
  }

  .swall_title_custom {
    background-color: red !important;
    padding: 0 !important;
  }

  .image_swall_customs {
    height: 25px !important;
  }
}

