import { IGenericResponseSuccess } from '@shared/interface/generic';

export interface ICreateSO {
  product_id: string;
  product_image: string;
  product_name: string;
  qty_order: number;
  qty_available: number;
  qty_fulfilled: number;
  qty_less: number;
  note: string | null;
  price: number;
  discount: number;
  percentage_ppnvalue: number;
}

export interface ItemResponse {
  product_id: string;
  product_name: string;
  product_image: string;
  qty_order: number;
  discount: number;
  price?: any;
  percentage_ppnvalue: number;
  price_discount?: any;
}

export interface ICreatedSO {
  purchase_id: string;
  code: string;
  item_responses: ItemResponse[];
  billing_type_enum: string;
  billing_type_string: string;
  cbd_discount: number;
}

export interface INote {
  available_enum: string;
  display: string;
}

export interface IGetStock {
  product_id: string;
  product_name: string;
  order_item: number;
  available_stock?: any;
}

export interface IResponseSO extends IGenericResponseSuccess {}

export interface IPostSO {
  product_id: string;
  qty: number;
  estimate_available: string | null;
}

export interface IPostSOFormat {
  warehouse_id: string;
  sales_order_item_requests: IPostSO[];
}

// enhancement endpoint response - related to promag
export interface IInitCreateSO {
  is_have_program_marketing: boolean;
  purchase_order_code: string;
}

export interface IInitCreateSO__ProductOrder
  extends Omit<ICreateSO, 'qty_order' | 'qty_fulfilled' | 'qty_available' | 'qty_less' | 'note' | 'price' | 'discount' | 'percentage_ppnvalue'> {
  qty_outstanding: number;
  sale_unit: string;
  available_stock: number | null;
  note?: string;
}

export interface IInitCreateSO__ProductQty {
  product_id: string;
  qty: number;
}

export interface IFormCreateSO__ProductField {
  product_id: string;
  product_name: string;
  product_image: string;
  qty_order: number;
  qty_fulfilled: number;
  qty_less: number;
  note: string;
}

export interface IFormCreateSOTag__ProductField extends IFormCreateSO__ProductField {
  packaging_type: string;
}

export interface IPayloadProductQty {
  products: IInitCreateSO__ProductQty[];
}

interface ISalesOrderItemRequests extends IInitCreateSO__ProductQty {
  estimate_available: string | null;
}

export interface IPayloadCreateSO {
  warehouse_id: string;
  sales_order_item_requests: ISalesOrderItemRequests[];
}
