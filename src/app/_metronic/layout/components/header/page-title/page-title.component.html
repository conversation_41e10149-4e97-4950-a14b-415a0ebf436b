<ng-container *ngIf="title$ | async as title">
  <!-- begin::Title -->
  <h1 *ngIf="title$ | async as title" class="page-heading d-flex text-dark my-0"
    [ngClass]="{'flex-column justify-content-center': appPageTitleDirection, 'align-items-center': !appPageTitleDirection}">
    {{ title }}
    <ng-container *ngIf="appPageTitleDescription">
      <ng-container *ngIf="description$ | async as description">


        <!--begin::Description-->
        <span class="page-desc text-muted fs-7 fw-semibold" [ngClass]="{'pt-2': appPageTitleDirection === 'column'}">
          <ng-container *ngIf="appPageTitleDirection === 'row'">
            <!--begin::Separator-->
            <span class="h-20px border-1 border-gray-300 border-start ms-3 mx-2"></span>
            <!--end::Separator-->
          </ng-container>

          {{description}}
        </span>
        <!--end::Description-->
      </ng-container>

    </ng-container>

  </h1>
  <!-- end::Title -->

  <ng-container *ngIf="appPageTitleBreadcrumb">
    <ng-container *ngIf="bc$ | async as _bc">
      <ng-container *ngIf="_bc.length > 0">

        <ng-container *ngIf="appPageTitleDirection === 'row'">
          <span class="h-20px border-gray-300 border-start mx-4"></span>
        </ng-container>

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0"
          [ngClass]="{'pt-2': appPageTitleDirection === 'column'}">

            <li
              *ngFor="let bc of _bc" class="breadcrumb-item"
              [ngClass]="!bc.isSeparator && !bc.isActive ? 'text-muted' : 'text-dark fw-bold'"
            >

              <ng-container *ngIf="bc.attributes; else default">
                <span class="fw-bold">{{ bc.attributes }}</span>
              </ng-container>

              <ng-template #default>
                <ng-container *ngIf="!bc.isSeparator && bc.path">
                  <ng-container *ngIf="bc?.queryParams; else defaultPathLink">
                    <a class="text-muted fw-normal {{ bc.path && bc.path.trim().length > 0 ? 'cursor-pointer' : 'cursor-default' }}"
                       [routerLink]='bc.path' [queryParams]="bc?.queryParams"> {{ bc.title }}
                    </a>
                  </ng-container>
                  <ng-template #defaultPathLink>
                    <a class="text-muted fw-normal {{ bc.path && bc.path.trim().length > 0 ? 'cursor-pointer' : 'cursor-default' }}" [routerLink]='bc.path'>
                      {{ bc.title }}
                    </a>
                  </ng-template>
                </ng-container>

                <ng-container *ngIf="!bc.isSeparator && !bc.path">
                  <span>
                    {{ bc.title }}
                  </span>
                </ng-container>

                <ng-container *ngIf="bc.isSeparator">
                  <span class="w-5px text-muted">/</span>
                </ng-container>
              </ng-template>
            </li>
        </ul>
        <!--end::Breadcrumb-->

      </ng-container>
    </ng-container>
  </ng-container>
</ng-container>
