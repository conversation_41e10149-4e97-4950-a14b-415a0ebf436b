import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class ObjectUtilsService {
  constructor() {}

  removeDuplicateByKey(arr: any[], key: string) {
    return [...new Map(arr.map((item) => [item[key], item])).values()];
  }

  objectMapToArray(obj: Record<string, any>) {
    return Object.entries(obj).map(([key, value]) => ({ [key]: value }));
  }

  isObjectEmpty(obj: Record<string, any>) {
    if (!obj || Object.keys(obj).length === 0) return true;
    return Object.values(obj).every((value) => value === null || value === '' || value === undefined || value === false || value === 0 || value === 0.0);
  }

  isArrayObjIdentical(_arr1: any[], _arr2: any[]) {
    if (_arr1.length !== _arr2.length) {
      return false;
    }

    return _arr1.every((item, index) => {
      return Object.keys(item).every((objItem) => {
        return _arr2[index][objItem] === item[objItem];
      });
    });
  }
}
