// src/app/core/utils/dom-utils.service.spec.ts
import { DomUtilsService } from '../dom-utils.service';
import { TestHelpers } from './test-helpers';
import { TEST_DATA } from './test-data';

describe('DomUtilsService', () => {
  let service: DomUtilsService;

  beforeEach(() => {
    service = new DomUtilsService();
  });

  describe('copyToClipboard', () => {
    it('should copy text to clipboard successfully', async () => {
      // Arrange
      const testText = 'sample text';
      const writeTextMock = jest.fn().mockResolvedValue(undefined);
      TestHelpers.mockClipboard(writeTextMock);

      // Act
      await service.copyToClipboard(testText);

      // Assert
      expect(writeTextMock).toHaveBeenCalledWith(testText);
    });

    it('should handle clipboard API errors gracefully', async () => {
      // Arrange
      const writeTextMock = jest.fn().mockRejectedValue(new Error('Clipboard API not supported'));
      TestHelpers.mockClipboard(writeTextMock);

      // Act & Assert
      await expect(service.copyToClipboard('test')).rejects.toThrow('Clipboard API not supported');
    });
  });

  describe('setDefaultImageProduct', () => {
    it('should set default image when error event is triggered', () => {
      // Arrange
      const imgElement = TestHelpers.createTestElement('img');
      const event = TestHelpers.createMockEvent('error', imgElement);

      // Act
      const result = service.setDefaultImageProduct(event);

      // Assert
      expect(result.src).toMatch(/default_image\.svg$/);
    });

    it('should handle event without target gracefully', () => {
      // Arrange
      const event = TestHelpers.createMockEvent('error');

      // Act & Assert
      expect(() => service.setDefaultImageProduct(event)).toThrow('Cannot set properties of null');
    });
  });

  describe('otherToggleSelection', () => {
    it('should return true and add "OTHER" if not selected yet', () => {
      // Arrange
      const list = ['A', 'B'];
      const event = TestHelpers.createMockEvent('change', { value: 'OTHER' });

      // Act
      const result = service.otherToggleSelection(list, event);

      // Assert
      expect(result).toBe(true);
      expect(list).toContain('OTHER');
    });

    it('should return false and remove "OTHER" if already selected', () => {
      // Arrange
      const list = ['OTHER', 'B'];
      const event = TestHelpers.createMockEvent('change', { value: 'OTHER' });

      // Act
      const result = service.otherToggleSelection(list, event);

      // Assert
      expect(result).toBe(false);
      expect(list).not.toContain('OTHER');
    });

    it('should handle event without target gracefully', () => {
      // Arrange
      const list = ['A', 'B'];
      const event = TestHelpers.createMockEvent('change');

      // Act & Assert
      expect(() => service.otherToggleSelection(list, event)).toThrow('Cannot read properties of null');
    });
  });

  describe('getBrowserName', () => {
    it.each(TEST_DATA.browsers)(
      'should return $name for userAgent: $userAgent',
      ({ name, userAgent }) => {
        expect(service.getBrowserName(userAgent)).toBe(name);
      },
    );

    it('should return "other" for empty user agent', () => {
      expect(service.getBrowserName('')).toBe('other');
    });

    it('should return "other" for null user agent', () => {
      expect(service.getBrowserName(null as any)).toBe('other');
    });
  });

  describe('checkInHasNewValue', () => {
    it('should return border class if hasValue is true', () => {
      expect(service.checkInHasNewValue(true)).toBe('border border-primary');
    });

    it('should return empty string if hasValue is false', () => {
      expect(service.checkInHasNewValue(false)).toBe('');
    });

    it('should handle undefined input gracefully', () => {
      expect(service.checkInHasNewValue(undefined as any)).toBe('');
    });
  });
});
