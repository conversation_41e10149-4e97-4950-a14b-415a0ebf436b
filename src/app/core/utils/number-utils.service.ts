import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class NumberUtilsService {
  constructor() {}

  toRupiah(value?: number, decimal?: boolean, symbol: boolean = true): string {
    let validateValue = value ?? 0;

    if (typeof decimal === 'boolean') {
      validateValue = value ? Math.trunc(value) : 0;
    }

    const options = symbol
      ? {
          style: 'currency',
          currency: 'IDR',
          minimumFractionDigits: 2,
        }
      : { minimumFractionDigits: 2 };

    return new Intl.NumberFormat('id-ID', options).format(validateValue).replace(/\s/g, '');
  }

  convertTwoDigits(value: number): string {
    return parseFloat(String(value)).toFixed(2);
  }

  toThousandConvert(value: number | string): string {
    let result: string;

    if (typeof value === 'string') {
      const num = parseFloat(value);
      const fixed = this.convertTwoDigits(num);
      result = new Intl.NumberFormat('id-ID').format(Number(fixed));
    } else {
      const fixed = this.convertTwoDigits(value);
      result = new Intl.NumberFormat('id-ID').format(Number(fixed));
    }

    return result;
  }

  toStringWithUnitType(input: string): string {
    const [numberPart, unitPart] = input.split(' ');
    const number = parseInt(numberPart);
    const formatted = new Intl.NumberFormat('id-ID', {
      maximumSignificantDigits: 3,
    }).format(number);
    return `${formatted} ${unitPart}`;
  }

  toNumberFormat(data: number): string {
    return new Intl.NumberFormat('id-ID', {
      maximumSignificantDigits: 3,
    }).format(data);
  }

  stringNumberToPayload(value: string): number {
    return !!value ? parseInt(value.replaceAll('.', '')) : 0;
  }

  convertStringToNumberWithComma(input: string): number {
    const normalized = input.replaceAll(',', '.');
    return parseFloat(normalized);
  }

  formatInternationalNumber(value: string) {
    if (!value) return 0;
    // Remove thousand separators (dot) and replace decimal separator (comma → dot)
    const normalizedValue = value.replace(/\./g, '').replace(',', '.');
    return Number(normalizedValue);
  }

  reverseFormatInternationalNumber(value: number, useFixedDecimal = false) {
    if (isNaN(value) || value === null) return '0';
    let formatted = value.toFixed(2).replace('.', ',');
    // remove ",00"
    if (!useFixedDecimal && formatted.endsWith(',00')) formatted = formatted.slice(0, -3);
    return formatted.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  }
}
