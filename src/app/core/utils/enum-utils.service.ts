import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class EnumUtilsService {
  constructor() {}

  getEnumKeyByValue = (_enum: any, _enumVal: string) => Object.keys(_enum)[Object.values(_enum).indexOf(_enumVal)];

  mapKeyToString(enumType: any, key: string): string {
    return enumType[key as keyof typeof enumType];
  }

  SubMenuBreadcrumbTitles(status: string | null, SubMenu: Record<string, string>): string | null {
    if (!status) return null;
    return SubMenu[status] ?? null;
  }
}
