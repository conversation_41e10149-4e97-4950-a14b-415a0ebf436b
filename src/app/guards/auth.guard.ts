import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';

import { AuthService } from '@services/auth.service';
import { Observable } from 'rxjs';
import { RolePrivilegeService } from '@services/role-privilege.service';

@Injectable({ providedIn: 'root' })
export class AuthGuard {
  permissions: boolean;

  constructor(private authService: AuthService, private rolePrivilegeService: RolePrivilegeService, private router: Router) {}

  canActivate(): Observable<boolean> | boolean | Promise<boolean> {
    const isLoggedIn = this.authService.isLoggedIn();
    // const isTokenExpired = isLoggedIn ? this.authService.isTokenExpired() : null;
    //
    // if (isTokenExpired) {
    //   this.authService.logout();
    //   return false;
    // }

    if (!isLoggedIn) {
      return this.router.navigate(['auth/login']).then(() => false);
    }

    return true;
  }

  canActivateChild(next: ActivatedRouteSnapshot) {
    return this.checkUserAccess(next);
  }

  checkUserAccess(route: ActivatedRouteSnapshot) {
    const _privilegeArray: any[] = [];
    // let _hasAccessPage: boolean = false;

    if (this.authService.isLoggedIn()) {
      const _enumAccess = !route.data.children.length ? route.data.enum : route.routeConfig?.data?.enum;
      const _routeHasChild = route.data.children.length > 0;
      // const _role = this.authService.getRoleFromToken()

      const _routeChildEnumArray: string[] = [];

      const _privileges = this.rolePrivilegeService.getDataUserPrivileges();

      _privileges.forEach((item: any, index: number) => {
        const { name } = item;
        _privilegeArray.push({ name });
        // has child access?
        if (item.child.length && item.name === _privilegeArray[index].name) {
          _privilegeArray[index].child = [];

          item.child.forEach((child: any) => {
            const { name } = child;
            _privilegeArray[index].child.push({ name });

            // has grandchild access?
            if (child.child.length) {
              _privilegeArray[index].grandChild = [];

              child.child.forEach((grandChild: { name: string }) => {
                const { name } = grandChild;
                _privilegeArray[index].grandChild.push({ name });
              });
            }
          });
        }
      });

      // _hasAccessPage = _privilegeArray.includes(_enumAccess);
      const _privilegeEnumArray = [];
      _privilegeArray.forEach((privilegeItem) => {
        if (privilegeItem.child && privilegeItem.child.length > 0) {
          let i = 0;
          while (i < privilegeItem.child.length) {
            _privilegeEnumArray.push(privilegeItem.child[i].name);
            i++;
          }
        }
      });

      const _hasParent = _privilegeArray.filter((parentItem) => parentItem.name === _enumAccess);
      const _hasAccessToParent = _hasParent.length > 0;

      if (!_hasAccessToParent) {
        return false;
      }

      if (_routeHasChild) {
        route.data.children.forEach((child: { enum: string }) => _routeChildEnumArray.push(child.enum));
      }

      // if (!_hasAccessPage) {
      //   // return this.router.navigate(['/error/404']).then(() => false);
      // }

      return true;
    }

    // return false;
    return this.router.navigate(['/error/404']).then(() => false);
  }
}
