import { Directive, Input, Renderer2, ViewContainerRef } from '@angular/core';
import { MatButton } from '@angular/material/button';
import { MatPaginator } from '@angular/material/paginator';

interface PageObject {
  length: number;
  pageIndex: number;
  pageSize: number;
  previousPageIndex: number;
}

@Directive({
  selector: '[appTablePaginator]',
})
export class TablePaginatorDirective {
  // @Input() length: number;
  // @Input() pageIndex: number;

  private _pageGapTxt = '...';
  private _rangeStart: number;
  private _rangeEnd: number;
  private _buttons: any[] = [];

  private _curPageObj: PageObject = {
    length: 0,
    pageIndex: 0,
    pageSize: 0,
    previousPageIndex: 0,
  };

  _showTotalPages = 5;
  _length = 0;

  @Input() get pageSize(): number {
    return this._showTotalPages;
  }

  set pageSize(value: number) {
    this._showTotalPages = value % 2 == 0 ? value + 1 : value;
  }

  get inc(): number {
    return this._showTotalPages % 2 == 0 ? this.pageSize / 2 : (this.pageSize - 1) / 2;
  }

  get numOfPages(): number {
    return this.matPaginator.getNumberOfPages();
  }

  get lastPageIndex(): number {
    return this.matPaginator.getNumberOfPages() - 1;
  }

  constructor(private matPaginator: MatPaginator, private renderer: Renderer2, private viewRef: ViewContainerRef) {
    this.matPaginator.page.subscribe((e: PageObject) => {
      if (this._curPageObj.pageSize != e.pageSize && this._curPageObj.pageIndex != 0) {
        e.pageIndex = 0;
        this._rangeStart = 0;
        this._rangeEnd = this._showTotalPages - 1;
      }

      this._curPageObj = e;
      this.initPageRange();
    });
  }

  private buildPageNumbers(midlePage: number) {
    const actionContainer = this.viewRef.element.nativeElement.querySelector('div.mat-mdc-paginator-range-actions');
    const nextPageNode = this.viewRef.element.nativeElement.querySelector('button.mat-mdc-paginator-navigation-next');

    if (this._buttons.length > 0) {
      this._buttons.forEach((button) => {
        this.renderer.removeChild(actionContainer, button);
      });

      this._buttons.length = 0;
    }

    let buttons;
    for (let i = 0; i < this.numOfPages; i++) {
      if (i >= this._rangeStart && i <= this._rangeEnd) {
        buttons = this.createButtons(i, this.matPaginator.pageIndex);
        this.renderer.insertBefore(actionContainer, buttons, nextPageNode);
      }

      if (i == this._rangeEnd) {
        buttons = this.createButtons(this._pageGapTxt, this._rangeEnd);
        this.renderer.insertBefore(actionContainer, buttons, nextPageNode);
      }
    }
  }

  private createButtons(i: any, pageIndex: number): any {
    const linkBtn: MatButton = this.renderer.createElement('button');

    this.renderer.setAttribute(linkBtn, 'class', 'btn btn-sm mat-custom-page-link');

    if (i == this.lastPageIndex) {
      this.renderer.addClass(linkBtn, 'last');
    }

    const pagingTxt = isNaN(i) ? this._pageGapTxt : +(i + 1);
    const text = this.renderer.createText(pagingTxt + '');

    switch (i) {
      case pageIndex:
        this.renderer.setAttribute(linkBtn, 'disabled', 'disabled');
        this.renderer.addClass(linkBtn, 'active');
        break;
      case this._pageGapTxt:
        let newIndex = this._curPageObj.pageIndex + this._showTotalPages;

        if (newIndex >= this.numOfPages) newIndex = this.lastPageIndex;

        if (pageIndex != this.lastPageIndex) {
          this.renderer.listen(linkBtn, 'click', () => {
            this.switchPage(newIndex);
          });
        }

        if (pageIndex == this.lastPageIndex) {
          this.renderer.setAttribute(linkBtn, 'disabled', 'disabled');
        }
        break;
      default:
        this.renderer.listen(linkBtn, 'click', () => {
          this.switchPage(i);
        });
        break;
    }

    this.renderer.appendChild(linkBtn, text);
    this._buttons.push(linkBtn);

    return linkBtn;
  }

  private initPageRange() {
    const middleIndex = (this.matPaginator.pageIndex + this.matPaginator.pageSize) / 2;
    let number = this.matPaginator.length / this.matPaginator.pageSize;
    this._rangeStart = this.matPaginator.pageIndex - 1;
    if (this._rangeStart < 0) {
      this._rangeStart = 0;
    }

    this._rangeEnd = this.matPaginator.pageIndex + 3;
    if (this._rangeEnd > number) {
      this._rangeEnd = number;
    }

    this.buildPageNumbers(middleIndex);
  }

  //Helper function to switch page on non first, last, next and previous buttons only.
  private switchPage(i: number): void {
    const previousPageIndex = this.matPaginator.pageIndex;
    this.matPaginator.pageIndex = i;
    this.matPaginator['_emitPageEvent'](previousPageIndex);
    this.initPageRange();
  }

  public ngAfterViewInit() {
    this._rangeStart = 0;
    this._rangeEnd = this._showTotalPages - 1;
    this.initPageRange();
  }
}
