import { Directive, ElementRef, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { debounceTime, Observable } from 'rxjs';

@Directive({
  selector: '[appObserveIntersection]',
  exportAs: 'intersection',
})
export class ObserverIntersectionDirective implements OnInit {
  @Input() root: HTMLElement | null = null;
  @Input() isContinuous = true;

  @Input() debounceTime = 0;
  @Input() threshold = 1;

  @Output() isIntersecting = new EventEmitter<boolean>();
  @Output() visible = new EventEmitter<HTMLElement>();

  _isIntersecting = false;

  constructor(private element: ElementRef) {}

  ngOnInit() {
    this.createAndObserve();
  }

  private createAndObserve() {
    const options = {
      rootMargin: '0px',
      threshold: this.threshold,
    };

    return new Observable<boolean>((subscriber) => {
      const intersectionObserver = new IntersectionObserver((entries) => {
        const { isIntersecting } = entries[0];
        subscriber.next(isIntersecting);

        isIntersecting && !this.isContinuous && intersectionObserver.disconnect();
      }, options);

      intersectionObserver.observe(this.element.nativeElement);

      return {
        unsubscribe() {
          intersectionObserver.disconnect();
        },
      };
    })
      .pipe(debounceTime(this.debounceTime))
      .subscribe((status) => {
        this.isIntersecting.emit(status);
        this._isIntersecting = status;
      });
  }
}
