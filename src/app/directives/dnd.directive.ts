import { Directive, EventEmitter, HostBinding, HostListener, Output } from '@angular/core';

@Directive({
  selector: '[appDnd]',
})
export class DndDirective {
  @Output() private filesChangeEmitter: EventEmitter<File[]> = new EventEmitter();

  @HostBinding('style.background') private background = 'transparent';
  @HostBinding('style.border') private borderStyle = '2px dashed';
  @HostBinding('style.border-color') private borderColor = 'var(--kt-gray-300)';
  @HostBinding('style.border-radius') private borderRadius = '5px';

  constructor() {}

  @HostListener('dragover', ['$event']) public onDragOver(evt: { preventDefault: () => void; stopPropagation: () => void }) {
    evt.preventDefault();
    evt.stopPropagation();
    this.background = 'rgba(118,148,64, 0.15)';
    this.borderColor = 'var(--kt-primary-active)';
    this.borderStyle = '1px solid';
  }

  @HostListener('dragleave', ['$event']) public onDragLeave(evt: { preventDefault: () => void; stopPropagation: () => void }) {
    evt.preventDefault();
    evt.stopPropagation();
    this.background = 'transparent';
    this.borderColor = 'var(--kt-gray-300)';
    this.borderStyle = '2px dashed';
  }

  @HostListener('drop', ['$event']) public onDrop(evt: { preventDefault: () => void; stopPropagation: () => void; dataTransfer: { files: any } }) {
    evt.preventDefault();
    evt.stopPropagation();
    this.background = 'transparent';
    this.borderColor = 'var(--kt-gray-300)';
    this.borderStyle = '2px dashed';
    let files = evt.dataTransfer.files;
    let valid_files: Array<File> = files;
    this.filesChangeEmitter.emit(valid_files);
  }
}
