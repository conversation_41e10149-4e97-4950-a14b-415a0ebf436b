import { TestBed } from '@angular/core/testing';

import { ProfileService } from './profile.service';
import { provideHttpClientTesting } from '@angular/common/http/testing';
// import { API } from '@config/constants/api.constant';
import { BaseService } from '@services/base-service.service';
import { of } from 'rxjs';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

export const DUMMY_PROFILE = {
  id: 'd468c2ea-b158-4fcd-9049-c654c33f1fb9',
  name: 'Paimo Super',
  email: '<EMAIL>',
  role: 'Super Admin',
  area: 'Jawa Barat',
  phone_number: '**********',
  address: 'tesss, Kedungrejo, Waru, Kabupaten Sidoarjo, Jawa Timur',
};

describe('ProfileService', () => {
  let profileService: ProfileService;
  // let httpTestingController: HttpTestingController;
  // let baseServiceSpy = jasmine.createSpyObj('BaseService', ['getData']);

  let baseServiceSpy = jasmine.createSpyObj('BaseService', ['getData']);

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        ProfileService,
        {
          provide: BaseService,
          useValue: baseServiceSpy,
        },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    profileService = TestBed.inject(ProfileService);
    // httpTestingController = TestBed.inject(HttpTestingController);
  });

  it(' should be created', () => {
    expect(profileService).toBeTruthy();
  });

  // get profile from base service
  it('getProfile() should retrieve profile data from base service', (done: DoneFn) => {
    baseServiceSpy.getData.and.returnValue(of({ data: DUMMY_PROFILE }));
    profileService.getProfile().subscribe((resp) => {
      expect(resp).toBeTruthy();
      expect(resp.data).toEqual(DUMMY_PROFILE);
      done();
    });

    expect(baseServiceSpy.getData).toHaveBeenCalledTimes(1);
  });

  // use http client directly
  // xit('#getProfile2() should return profile data', () => {
  //   profileService.getProfile2().subscribe((value) => {
  //     expect(value).withContext('no data returned').toBeTruthy();
  //   });
  //   const req = httpTestingController.expectOne(API.GET_DETAIL_PROFILE_ADMIN_CMS);
  //   expect(req.request.method).toEqual('GET');
  //
  //   req.flush({
  //     data: Object.values(DUMMY_PROFILE),
  //   });
  // });

  // afterEach(() => {
  //   httpTestingController.verify();
  // });
});
