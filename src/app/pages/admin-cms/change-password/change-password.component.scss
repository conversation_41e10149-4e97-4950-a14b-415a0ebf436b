.input-password-container {
  position: relative;


  .desc-list {
    color: #808080 !important;
  }

  .eye-icon-container {
    position: absolute;
    left: 0;
    top: 14px;
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;

    .icon {
      transform: translateX(60px);
    }
  }
}

.form-group-password {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

label {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  letter-spacing: 0.015em;
  color: #353535;
}

.form-control {

  background-color: #F8F8F8;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  color: #353535;
  height: 50px;
  border-radius: 6px;

  &::placeholder {
    color: #D1D1D1;
  }
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

.invalid-form-custom {
  color: red;
  margin-top: 5px;
}


.text-green {
  color: #688238;
}
