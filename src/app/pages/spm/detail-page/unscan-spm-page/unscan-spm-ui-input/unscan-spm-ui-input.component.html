<form [formGroup]="scannerInput" (ngSubmit)="submit()">
  <div *ngIf="isLoading.value">Loading</div>

  <ng-container *ngIf="!isLoading.value && !isManualInput">
    <ng-container [ngSwitch]="resetScanOne">
      <ng-container *ngSwitchCase="true">
        <label style="background-image: url('./assets/media/illustrations/il_unscan_product.svg')" class="background-input-qr"
               [htmlFor]="'qrScanOneInput'">
          <input type="text" (ngModelChange)="onChange($event)" #inputQROneForm autofocus formControlName="qrCodeOne"
                 id="qrScanOneInput" class="input-text-scanner"/>
        </label>
      </ng-container>
      <ng-container *ngSwitchDefault>
        <label style="background-image: url('./assets/media/illustrations/il_unscan_product.svg')" class="background-input-qr"
               [htmlFor]="'qrScanInput'">
          <input type="text" (ngModelChange)="onChange($event)" #inputQrForm autofocus formControlName="qrCode"
                 id="qrScanInput" class="input-text-scanner"/>
        </label>
      </ng-container>
    </ng-container>
  </ng-container>

  <div *ngIf="isManualInput">
    <input
      formControlName="qrManual"
      placeholder="Input kode QR barang"
      [value]="ManualInput?.value?.toUpperCase()"
      class="form-control form-control-solid {{ scannerInput.controls.qrCode.errors && 'is-invalid border border-danger' }}"
    />
    <div class="mt-2 text-danger" *ngIf="scannerInput.invalid && scannerInput.dirty">
      <div *ngIf="scannerInput.controls.qrManual.errors">Kode QR tidak valid, silahkan periksa ulang dan input lagi.
      </div>
    </div>
  </div>
</form>

<app-modal #modalErrorScan [modalConfig]="modalConfigErrorScan" [modalOptions]="{ size: 'md' }">
  <div class="text-center mt-6 mb-n6">
    <span [inlineSVG]="STRING_CONSTANTS.ILLUSTRATIONS.IL_ERROR_QR_SCAN"></span>
    <div class="mt-6 d-flex w-100 justify-content-center on_copy_password align-items-center">
      <span>{{ responseMessage.value.message }}</span>
      <div class="d-flex" ngbPopover="SO ID berhasil di copy" placement="top">
        <span (click)="copyToClipboard(responseMessage.value.spmId)" [inlineSVG]="STRING_CONSTANTS.ICON.IC_COPY"
              class="cursor-pointer"></span>
      </div>
    </div>
  </div>
</app-modal>
