import { Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { MatSort, Sort } from '@angular/material/sort';
import { MultipleSelectChipComponent } from '@shared/components/multiple-select-chip/multiple-select-chip.component';
import { BaseTableService } from '@shared/base/base-table-service.service';
import { IListSPM } from '@models/spm.model';
import { TableColumn } from '@shared/interface/table.interface';
import { BehaviorSubject } from 'rxjs';
import { InputSelectInterface } from '@shared/components/form/input-select/input-select.interface';
import { BaseDatasource } from '@shared/base/base.datasource';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { PageInfoService, PageLink } from '@metronic/layout';
import { ActivatedRoute } from '@angular/router';
import { UrlUtilsService } from '@utils/url-utils.service';
import { UtilitiesService } from '@services/utilities.service';
import { FilterService } from '@services/filter.service';
import { getFilterStatus } from '../spm.data';
import { API } from '@config/constants/api.constant';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { SpmService } from '../spm.service';

@Component({
  selector: 'app-pending',
  templateUrl: './pending.component.html',
  styleUrls: ['./pending.component.scss'],
})
export class PendingComponent implements OnInit {
  @ViewChild(MatSort, { static: false }) matSort: MatSort;
  @Output() totalCount = new EventEmitter();
  @ViewChild('inputSelectStatus') inputSelectMatChips: MultipleSelectChipComponent;
  STRING_CONSTANTS = STRING_CONSTANTS;
  iconNone = STRING_CONSTANTS.ICON.IC_SPM_NONE;
  isLoading = false;
  currentDate = new Date();
  // table
  baseTable: BaseTableService<IListSPM> = this.base;
  spmList: IListSPM[];
  tableColumns: TableColumn[];
  displayedColumns: string[];
  behaviorSubjectStatusList: BehaviorSubject<InputSelectInterface[]> = new BehaviorSubject<InputSelectInterface[]>([]);
  baseDatasource: BaseDatasource<IListSPM>;
  string_filter?: string;
  checkActive: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  isOpenFilter: boolean = false;
  isActiveFilter: boolean = false;
  filterForm: FormGroup;
  privilegeDetail: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  links: Array<PageLink> = [
    {
      title: 'Surat Perintah Muat',
      path: '',
      isActive: false,
    },
    {
      title: '',
      path: '',
      isActive: false,
      isSeparator: true,
    },
    {
      title: 'Diproses',
      path: '',
      isActive: true,
    },
  ];

  constructor(
    private activeRoute: ActivatedRoute,
    private pageInfoService: PageInfoService,
    private base: BaseTableService<IListSPM>,
    private paginationService: UrlUtilsService,
    public utilities: UtilitiesService,
    private formBuilder: FormBuilder,
    private filterService: FilterService,
    public spmService: SpmService
  ) {}

  ngOnInit() {
    this.privilegeDetail.next(this.spmService.getPrivilegeDetail('LIST_SPM_DIPROSES'));
    this.pageInfoService.updateBreadcrumbs(this.links);
    this.baseTable.responseDatabase.subscribe((response) => (this.baseDatasource = response));
    this.initFilterStatus();
    this.setTableData();
    this.initFilter();
    this.queryHandler();
    this.initPageInfo();
  }

  initFilterStatus() {
    let status: InputSelectInterface[] = getFilterStatus();
    this.behaviorSubjectStatusList.next(status);
  }

  initFilter() {
    this.filterForm = this.formBuilder.group({
      status: [''],
      start_date: new FormControl({ value: '', disabled: true }),
      end_date: new FormControl({ value: '', disabled: true }),
    });
  }

  initPageInfo() {
    this.baseDatasource.tableSubject.subscribe({
      next: (data) => {
        this.spmList = data;
      },
    });
  }

  setTableData() {
    this.spmService.setTableData(this.spmList, this.privilegeDetail.value);
    this.tableColumns = this.spmService.tableColumns;
    this.displayedColumns = this.spmService.displayedColumns;
    this.baseDatasource = this.spmService.baseDatasource;
  }

  sortTable(param: Sort) {
    this.filterService.sortTable(param, this.tableColumns);
  }

  changePageEvent($event: BaseDatasource<any>) {
    this.filterService.changePageEvent($event, this.string_filter ?? '');
  }

  async handleActionDetail(status: string, id: string) {
    return this.spmService.goToDetail(id, status.toLowerCase());
  }

  onSearch(event: string) {
    this.checkActive.next(!this.checkActive.value);
    this.filterService.onSearch(event);
    this.string_filter = '';
  }

  queryHandler() {
    this.activeRoute.queryParams.subscribe((data) => {
      this.string_filter = data.string_filter;
      this.filterForm.controls['status'].setValue(data.status);
      this.filterForm.controls['start_date'].setValue(data.start_date);
      this.filterForm.controls['end_date'].setValue(data.end_date);
      this.isActiveFilter = !!(data.status || data.start_date || data.end_date);
      let param = this.paginationService.sliceQueryParams();
      this.baseTable.loadDataTable(API.LIST_SPM_PENDING, param ? param : '');
      this.filterService.filterCountList(this.baseDatasource, `Surat Perintah Muat Diproses`);
    });
  }

  handleOpenFilter = () => {
    this.isOpenFilter = !this.isOpenFilter;
  };

  handleSubmitFilter = () => {
    this.isActiveFilter = true;
    this.isOpenFilter = false;

    this.checkActive.next(!this.checkActive.value);
    const _extras = {
      queryParams: {
        string_filter: this.string_filter,
        start_date: this.utilities.timeStampToDate(this.filterForm.controls['start_date'].value, 'yyyy-MM-dd'),
        end_date: this.utilities.timeStampToDate(this.filterForm.controls['end_date'].value, 'yyyy-MM-dd'),
      },
    };
    this.filterService.submitFilter(this.filterForm, _extras);
  };

  handleResetFilter = () => {
    this.isActiveFilter = false;
    this.isOpenFilter = false;
    const _keyFilter = ['status', 'start_date', 'end_date'];
    this.filterService.resetFilter(this.filterForm, _keyFilter);
    this.inputSelectMatChips?.resetOptionsTest();
  };
}
