import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { IPrivilegeChecklist, IPrivilegeList, IPrivileges, IRolePrivilegesData } from '../role.interface';
import { BehaviorSubject, Observable, of, Subscription, switchMap } from 'rxjs';
import { RoleService } from '../role.service';

@Component({
  selector: 'app-card-privilege-checkbox',
  templateUrl: './card-privilege-checkbox.component.html',
  styleUrls: ['./card-privilege-checkbox.component.scss'],
})
export class CardPrivilegeCheckboxComponent implements OnInit, OnChanges, OnDestroy {
  @Input() privileges: IPrivileges;
  @Input() role: string;
  @Input() appType: string | any;

  @Output() checkedPrivilegeList: EventEmitter<IPrivilegeChecklist[]> = new EventEmitter<IPrivilegeChecklist[]>();
  @Output() currentRolePrivilege = new EventEmitter<any>();

  private unsubscribe: Subscription[] = [];

  privilegesGroupChecklist: IPrivilegeChecklist[];
  rolePrivilegesDataSubject: BehaviorSubject<IRolePrivilegesData>;
  currentRolePrivilege$: Observable<IPrivilegeList>;

  constructor(public roleService: RoleService) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.privileges && !changes.privileges.firstChange) {
      this.initChecklistData();
    }
  }

  initChecklistData() {
    const getCurrentRolePrivilege$ = of(this.roleService.getCurrentRolePrivileges(this.role, this.appType));
    this.currentRolePrivilege$ = getCurrentRolePrivilege$.pipe(switchMap((response) => response));

    if (!this.privileges) return;
    // provide role privilege checkboxes list
    // map privileges list with current role privileges to set initial checkboxes value
    this.privilegesGroupChecklist = this.privileges.child.map((privilege) => {
      return {
        sequence: privilege.child.length ? this.privileges.sequence : privilege.sequence,
        parentMenu: this.privileges.parentMenu,
        pageView: privilege.pageView,
        name: privilege.displayName,
        enumName: privilege.name,
        completed: false,
        list: privilege.child,
      };
    });

    this.privilegesGroupChecklist.map((_pgcItem) => {
      if (_pgcItem?.list?.length) {
        _pgcItem.list.forEach((_pgcItemList) => {
          if (_pgcItemList?.child?.length) {
            _pgcItemList.checked = false;
            _pgcItemList.completed = false;
            _pgcItemList.child.forEach((_pgcItemListChild) => (_pgcItemListChild.checked = false));
          } else {
            _pgcItemList.completed = false;
            _pgcItemList.checked = false;
          }
        });
      }
    });

    const currentRolePrivilegeSubs = this.currentRolePrivilege$.subscribe((result) => {
      if (result) {
        const { privilege } = result;
        this.getCheckedList(privilege);
        this.currentRolePrivilege.emit(privilege);
      }
    });

    this.rolePrivilegesDataSubject = new BehaviorSubject<IRolePrivilegesData>({} as IRolePrivilegesData);
    this.unsubscribe.push(currentRolePrivilegeSubs);
  }

  updateAllChecked(_groupChecklist: IPrivilegeChecklist | any, _parentEnum = '', _enumKey = '') {

    // use _enumKey to handle nested level in privileges group checklist
    let _checkedGroupChecklist = this.privilegesGroupChecklist.find((_pgcItem) => {
      if (_enumKey === 'subDetailPage') {
        const { list } = _pgcItem;
        const _filteredList = list?.filter((_itemList) => _itemList.name === _parentEnum);

        _filteredList?.map((_list) => {
          const { child } = _list;

          if (child && child.length === 1) {
            _list.checked = child.every((_itemChild) => _itemChild.checked);
            _list.completed = _list.checked;
          }

          if (child && child.length > 1) {
            _list.completed = !!child.every((_itemChild) => _itemChild.checked);
          }
        });

        return _filteredList && _filteredList[0];
      }

      return _pgcItem.name === _groupChecklist.name;
    });

    if (_checkedGroupChecklist) {
      const { list } = _checkedGroupChecklist;
      // if item have child set complete to true if all child is checked
      if (list?.length) {
        _checkedGroupChecklist.completed = list.every((_groupListItem) => _groupListItem.checked);
      }
    }

    this.mapToRolePrivileges();
  }

  setAllChecked(_groupChecklist: IPrivilegeChecklist | any, _checked: boolean, _parentEnum = '') {
    let _checkedGroupChecklist: IPrivilegeChecklist | any = this.privilegesGroupChecklist.find((_groupList) => _groupList.enumName === _groupChecklist.enumName);

    if (_parentEnum && !_checkedGroupChecklist) {
      // find child in child
      const _parentGroupChecklist = this.privilegesGroupChecklist.find((_groupList) => _groupList.enumName === _parentEnum);
      this.updateAllChecked(_parentGroupChecklist, '', '');

      _checkedGroupChecklist = _parentGroupChecklist?.list?.find((_groupList) => _groupList.name === _groupChecklist.name);
      _checkedGroupChecklist.child.forEach((_child: any) => (_child.checked = _checked));
      _checkedGroupChecklist.completed = _checked;

      return;
    }

    if (_checkedGroupChecklist) {
      _checkedGroupChecklist?.list?.forEach((_listItem: any) => {
        _listItem.checked = _checked;
        _listItem.completed = _checked;

        if (_listItem.child.length > 0) {
          const { child } = _listItem;
          child.forEach((_item: any) => {
            _item.checked = _checked;
          });
        }
      });

      _checkedGroupChecklist.completed = _checked;
    }

    this.mapToRolePrivileges();
    return;
  }

  someChecked(_groupChecklist: IPrivilegeChecklist | any, _enumName?: string, _childState: boolean = false) {
    let _indeterminateState = _childState;
    if (!_groupChecklist.list) {
      return false;
    }

    let _checkedGroupChecklist = this.privilegesGroupChecklist.find((_pgcItem) => _pgcItem.name === _groupChecklist.name);
    if (_checkedGroupChecklist?.list) {
      _indeterminateState =
        _checkedGroupChecklist?.list?.filter((_listItem) => {
          return _listItem.child && _listItem.child.length > 0 ? _listItem.child.filter((_listChild: any) => _listChild.checked).length > 0 : _listItem.checked;
        }).length > 0 && !_checkedGroupChecklist?.completed;
    }

    return _indeterminateState;
  }

  checkChildIndeterminate(_groupItem: IPrivilegeChecklist | any, _parentGroup: IPrivilegeChecklist | any) {
    const { child } = _groupItem;

    if (!child) {
      return false;
    }

    const _indeterminateState = child.filter((_item: any) => _item.checked).length > 0 && !_groupItem.completed;

    if (_indeterminateState) {
      this.someChecked(_parentGroup, _parentGroup.enumName, _indeterminateState);
    }

    return _indeterminateState;
  }

  mapToRolePrivileges() {
    this.checkedPrivilegeList.emit(this.privilegesGroupChecklist);
    return;
  }

  getCheckedList(privileges: IPrivileges[]) {
    const parentMenu = this.privilegesGroupChecklist[0]?.parentMenu;
    const myPrivileges = privileges.filter((value) => value.parentMenu === parentMenu);

    myPrivileges.forEach((_myPrivilegeItem) => {
      _myPrivilegeItem.child.forEach((_child1) => {
        const myPrivilegeChecklist = this.privilegesGroupChecklist.find((_pgcItem) => _pgcItem.enumName === _child1.name);
        if (myPrivilegeChecklist) {
          if (!myPrivilegeChecklist?.list?.length) {
            myPrivilegeChecklist.completed = true;
          }

          _child1.child.forEach((_child2) => {
            const _mpcList = myPrivilegeChecklist.list?.find((_mpcListItem) => _mpcListItem.name == _child2.name);

            if (_mpcList) {
              if (_mpcList.child && _mpcList.child.length > 0) {
                if (_mpcList.child.length == _child2.child?.length) {
                  _mpcList.checked = true;
                }

                _child2.child?.forEach((_child3) => {
                  const lastChild = _mpcList.child?.find((value4) => value4.name == _child3.name);
                  if (lastChild) {
                    lastChild.checked = true;
                    this.updateAllChecked(lastChild, _mpcList.name, 'subDetailPage');
                  }
                });
              } else {
                _mpcList.checked = true;
                this.updateAllChecked(myPrivilegeChecklist);
              }
            }
          });
        }
      });
    });

    this.mapToRolePrivileges();
  }

  // Todo: remove unused methods
  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }
}
