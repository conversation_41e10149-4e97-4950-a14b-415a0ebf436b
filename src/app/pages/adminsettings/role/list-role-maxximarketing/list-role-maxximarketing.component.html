<app-card>
  <ng-container *ngIf="baseDataSource" cardBody>
    <div class="table-responsive">
      <table
        mat-table
        class="table w-100 gy-5 table-row-bordered align-middle"
        [dataSource]="baseDataSource"
      >
        <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">

          <!-- COLUMN HEAD -->
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-3"
            [ngClass]="{
              'min-w-70px text-center': tableColumn.key === 'actions'
            }"
            style="{{ tableColumn.key === 'enum_string' ? 'width: 90%' : '' }}"
          >
            <app-table-content
              [width]="70"
              [isFinishLoadingSubject]="baseDataSource.isFinishLoadingSubject"
            >
              {{ tableColumn.title }}
            </app-table-content>
          </th>

          <!-- COLUMN BODY -->
          <td class="px-3" *matCellDef="let element">
            <ng-container [ngSwitch]="tableColumn.key">
              <div class="text-center" *ngSwitchCase="'actions'">
                <app-table-content [isFinishLoadingSubject]="baseDataSource.isFinishLoadingSubject" [type]="'icon'">
                   <span
                     [inlineSVG]="'./assets/media/icons/ic_edit.svg'"
                     class="svg-icon svg-icon-2 cursor-pointer"
                     (click)="goToDetail(element['enum_value'])"
                   ></span>
                </app-table-content>
              </div>
              <div *ngSwitchDefault>
                <app-table-content [isFinishLoadingSubject]="baseDataSource.isFinishLoadingSubject">
                  <span>{{ element[tableColumn.key] }}</span>
                </app-table-content>
              </div>
            </ng-container>
          </td>

        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
    </div>
  </ng-container>

</app-card>
