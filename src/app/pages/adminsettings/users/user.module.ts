import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UserListComponent } from './user-list/user-list.component';
import { UserDetailComponent } from './component/user-detail/user-detail.component';
import { UserCreateComponent } from './user-create/user-create.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { AdminSettingsRoutingModule } from '../admin-settings-routing.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { DirectiveModule } from '@directives/directive.module';
import { ComponentsModule } from '@shared/components/components.module';
import { RoleModule } from '../role/role.module';
import { RetailersModule } from '../../retailer/retailers.module';
import { DetailUserComponent } from './detail-user/detail-user.component';
import { UserLogComponent } from './component/user-log/user-log.component';
import { MatChipsModule } from '@angular/material/chips';
import { AddSelectScopeComponent } from './component/add-select-scope/add-select-scope.component';
import { AddSelectDeactiveScopeComponent } from './component/add-select-deactive-scope/add-select-deactive-scope.component';
import { MatRadioModule } from '@angular/material/radio';

@NgModule({
  declarations: [UserListComponent, UserDetailComponent, UserCreateComponent, DetailUserComponent, UserLogComponent, AddSelectScopeComponent, AddSelectDeactiveScopeComponent],
  imports: [
    CommonModule,
    FormsModule,
    InlineSVGModule,
    MatTableModule,
    MatSortModule,
    MatProgressSpinnerModule,
    AdminSettingsRoutingModule,
    NgbModule,
    ReactiveFormsModule,
    MatSelectModule,
    MatTabsModule,
    DirectiveModule,
    ComponentsModule,
    RoleModule,
    RetailersModule,
    MatChipsModule,
    MatRadioModule,
  ],
})
export class UserModule {}
