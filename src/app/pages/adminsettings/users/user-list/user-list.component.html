<app-card [cardBodyClasses]="'pt-2'" [header]="true">
  <ng-container cardHeader>
    <app-input-search
      placeholder="Cari Nama / Email"
      [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
      (actionFilter)="onSearch($event)"
      [value]="string_filter || ''"
    ></app-input-search>

    <div class="ms-auto position-relative">
      <app-filter-table
        [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
        [isOpenFilter]="isOpenFilter"
        [isActiveFilter]="isActiveFilter"
        (actionClick)="handleOpenFilter()"
        (actionReset)="handleResetFilter()"
      >
        <div class="menu menu-sub menu-sub-dropdown w-300px w-md-350px show filter-body" id="kt-toolbar-filter">
          <div class="px-7 py-5">
            <div class="fs-4 text-dark fw-bold">Filter</div>
          </div>
          <div class="separator border-gray-200"></div>
          <form class="form" [formGroup]="FilterForm" (ngSubmit)="handleSubmitFilter()" [ngClass]="'w-100'">
            <div class="px-7 py-5">
              <div class="mb-10">
                <label class="form-label fs-5 mb-6">Status:</label>
                <div class="filter-pills">
                  <mat-chip-listbox #filterPillsBoxStatus multiple (change)="handleBatchStatusChange($event)">
                    <ng-container formArrayName="status">
                      <mat-chip-option class="chips" *ngFor="let item of statusFilterList.value" [value]="item.enum">
                        <div class="d-flex align-items-center justify-content-between">
                          <span>{{ item.value }}</span>
                          <span class="custom-x-icon"></span>
                        </div>
                      </mat-chip-option>
                    </ng-container>
                  </mat-chip-listbox>
                </div>
              </div>
              <div class="mb-10">
                <label class="form-label fs-5 mb-6">Role:</label>
                <div class="filter-pills">
                  <mat-chip-listbox #filterPillsBoxRole multiple (change)="handleBatchRoleChange($event)">
                    <ng-container formArrayName="role">
                      <mat-chip-option class="chips" *ngFor="let item of roleFilterList.value" [value]="item.enum">
                        <div class="d-flex align-items-center justify-content-between">
                          <span>{{ item.value }}</span>
                          <span class="custom-x-icon"></span>
                        </div>
                      </mat-chip-option>
                    </ng-container>
                  </mat-chip-listbox>
                </div>
              </div>
              <div class="mb-10">
                <label class="form-label fs-5 mb-7">Area:</label>
                <app-input-select
                  [class]="'w-100'"
                  (handleChangeData)="handleChangeArea($event)"
                  formControlName="area_id"
                  placeholder="Pilih salah satu"
                  [options]="listArea"
                  ngDefaultControl
                ></app-input-select>
              </div>
              <div class="d-flex justify-content-end mt-10">
                <button
                  type="reset"
                  (click)="handleResetFilter()"
                  class="btn btn-light btn-active-light-primary me-2"
                  data-kt-menu-dismiss="true"
                  data-kt-customer-table-filter="reset"
                >
                  Reset
                </button>
                <button
                  [disabled]="filterService.validateSubmitFilter(FilterForm)"
                  type="submit"
                  (click)="handleSubmitFilter()"
                  class="btn btn-primary"
                  data-kt-menu-dismiss="true"
                  data-kt-customer-table-filter="filter"
                >
                  Terapkan
                </button>
              </div>
            </div>
          </form>
        </div>
      </app-filter-table>
    </div>
  </ng-container>

  <ng-container cardBody>
    <ng-container cardBody *ngIf="FilterForm && baseDatasource.isTableLoaded && baseDatasource.totalItem$.getValue() === 0; else elseFilterBlock">
      <app-card-empty text="Belum terdapat data user." icon="{{ iconNone }}"></app-card-empty>
    </ng-container>

    <ng-template #elseFilterBlock>
      <div class="table-responsive">
        <table mat-table matSort (matSortChange)="sortTable($event)" [dataSource]="baseDatasource" class="table w-100 gy-5 table-row-bordered align-middle">
          <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
            <div>
              <th
                mat-header-cell
                *matHeaderCellDef
                class="min-w-125px px-3"
                [class.min-w-200px]="tableColumn.key !== 'actions' && tableColumn.key !== 'id'"
                [ngClass]="{
                  'min-w-70px text-center': tableColumn.key === 'actions',
                  'w-250px': tableColumn.key === 'alamat'
                }"
              >
                <div *ngIf="baseDatasource.isFinishLoadingSubject.value">
                  {{ tableColumn.title }}
                </div>
                <div *ngIf="!baseDatasource.isFinishLoadingSubject.value">
                  <app-table-content [width]="70"></app-table-content>
                </div>
              </th>
            </div>

            <td mat-cell *matCellDef="let element" class="px-3">
              <ng-container [ngSwitch]="tableColumn.key">
                <div *ngSwitchCase="'name'">
                  <app-table-content [type]="'text'" [count]="2" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    <span>{{ element[tableColumn.key] }}</span>
                    <span class="d-block">{{ element['phone_number'] }}</span>
                  </app-table-content>
                </div>

                <div *ngSwitchCase="'status'">
                  <app-table-content [height]="30" [width]="120" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    <span class="badge badge__status {{ 'badge__status--' + element['status_enum'] }}">
                      {{ element['status_string'] }}
                    </span>
                  </app-table-content>
                </div>

                <div *ngSwitchCase="'area_list'">
                  <app-table-content [type]="'text'" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    <span class="block-ellipsis-customs">
                      {{ displayArea(element) }}
                    </span>
                  </app-table-content>
                </div>

                <div *ngSwitchCase="'actions'" class="text-center">
                  <app-table-content [count]="1" [type]="'icon'" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    <span [inlineSVG]="'./assets/media/icons/ic_task.svg'" class="svg-icon svg-icon-2 cursor-pointer" (click)="handleAction(element['id'])"></span>
                  </app-table-content>
                </div>
                <div *ngSwitchDefault>
                  <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    <span>{{ element[tableColumn.key] }} </span>
                  </app-table-content>
                </div>
              </ng-container>
            </td>
          </ng-container>
          <tr mat-header-row *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
      </div>
      <div class="d-flex justify-content-between py-4">
        <app-mai-material-bottom-table
          [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
          class="w-100"
          [baseDataTableComponent]="baseDatasource"
          (changePage)="changePageEvent($event)"
        ></app-mai-material-bottom-table>
      </div>
    </ng-template>
  </ng-container>
</app-card>
