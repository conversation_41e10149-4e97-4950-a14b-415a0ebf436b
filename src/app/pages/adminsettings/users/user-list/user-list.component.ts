import { Component, OnInit, ViewChild } from '@angular/core';
import { PageInfoService, PageLink } from 'src/app/_metronic/layout';
import { IFilterChipUser, IListUser } from '../users.interface';
import { TableColumn } from '@shared/interface/table.interface';
import { BaseDatasource } from '@shared/base/base.datasource';
import { BaseComponent } from '@shared/base/base.component';
import { ActivatedRoute, Router } from '@angular/router';
import { MatSort, Sort } from '@angular/material/sort';
import { BaseTableService } from '@shared/base/base-table-service.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { UrlUtilsService } from '@utils/url-utils.service';
import { BehaviorSubject } from 'rxjs';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { BaseService } from '@services/base-service.service';
import { API } from '@config/constants/api.constant';
import { InputSelectInterface } from '@shared/components/form/input-select/input-select.interface';
import { IAreaList } from '@models/area.model';
import { UtilitiesService } from '@services/utilities.service';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { FilterService } from '@services/filter.service';
import { MatChipListbox, MatChipListboxChange } from '@angular/material/chips';
import { UserService } from '../user.service';

@Component({
  selector: 'app-users',
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.scss'],
})
export class UserListComponent extends BaseComponent implements OnInit {
  @ViewChild(MatSort, { static: false }) matSort: MatSort;
  STRING_CONSTANTS = STRING_CONSTANTS;
  string_filter?: string;
  isLoading = false;
  iconNone = STRING_CONSTANTS.ICON.IC_PROFILE_NONE;
  usersList: IListUser[];
  tableColumns: TableColumn[];
  displayedColumns: string[];
  tableColumn = ['id', 'status']; //mock
  baseDatasource: BaseDatasource<IListUser>;
  page: number = 1;
  size: number = 20;
  FilterForm: FormGroup;
  selectedArea: string;
  statusFilterList: BehaviorSubject<IFilterChipUser[]> = new BehaviorSubject<IFilterChipUser[]>([]);
  roleFilterList: BehaviorSubject<IFilterChipUser[]> = new BehaviorSubject<IFilterChipUser[]>([]);
  listArea: InputSelectInterface[] = [];
  isOpenFilter: boolean = false;
  isActiveFilter: boolean = false;
  checkActive: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  totalCount: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  privilegeDetail: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  links: Array<PageLink> = [
    {
      title: 'Admin Setting',
      path: '',
      isActive: false,
    },
    {
      title: '',
      path: '',
      isActive: false,
      isSeparator: true,
    },
    {
      title: 'User Management',
      path: '',
      isActive: false,
    },
    {
      title: '',
      path: '',
      isActive: false,
      isSeparator: true,
    },
    {
      title: '',
      path: '',
      isActive: true,
      attributes: `User List`,
    },
  ];

  @ViewChild('filterPillsBoxStatus') filterPillsBoxStatus: MatChipListbox;
  @ViewChild('filterPillsBoxRole') filterPillsBoxRole: MatChipListbox;

  constructor(
    private formBuilder: FormBuilder,
    private activeRoute: ActivatedRoute,
    private pageInfoService: PageInfoService,
    private router: Router,
    private baseTable: BaseTableService<IListUser>,
    private baseService: BaseService,
    private paginationService: UrlUtilsService,
    public utilities: UtilitiesService,
    private rolePrivilegeService: RolePrivilegeService,
    public filterService: FilterService,
    private userService: UserService,
  ) {
    super();
  }

  get userRoleControl() {
    return <FormControl>this.FilterForm.get('role');
  }

  get userStatusControl() {
    return <FormControl>this.FilterForm.get('status');
  }

  ngOnInit() {
    this.getListStatus();
    this.getListRole();
    this.getListArea();
    this.handlePrivilege();
    this.baseTable.responseDatabase.subscribe((response) => (this.baseDatasource = response));
    this.setTableData();
    this.initPageInfo();
    this.initFilter();
    this.queryHandler();
  }

  handlePrivilege() {
    this.privilegeDetail.next(this.rolePrivilegeService.checkPrivilege('SETTINGS', 'USER_MANAGEMENT', 'LIST_USER', 'CTA_VIEW_DETAIL'));
  }

  initFilter() {
    this.FilterForm = this.formBuilder.group({
      status: [''],
      role: [''],
      area_id: [''],
    });
  }

  handleResetFilter() {
    const _keyFilter = ['status', 'role', 'area_id'];
    this.filterService.resetFilter(this.FilterForm, _keyFilter);
    this.resetFilterPillsBox();
  }

  resetFilterPillsBox = () => {
    this.filterPillsBoxStatus.value = [];
    this.filterPillsBoxRole.value = [];
  };

  handleSubmitFilter() {
    this.isOpenFilter = false;
    this.checkActive.next(!this.checkActive.value);
    this.filterService.submitFilter(this.FilterForm);
  }

  onSearch(event: string) {
    this.checkActive.next(!this.checkActive.value);
    this.filterService.onSearch(event);
    this.string_filter = '';
  }

  getListArea() {
    this.baseService.getData<IAreaList[]>(API.LIST_AREA).subscribe((value) => {
      if (value) {
        this.listArea = value.data.map((value1) => {
          const inputSelector: InputSelectInterface = new InputSelectInterface();
          inputSelector.value = value1.id;
          inputSelector.display = value1.name;
          return inputSelector;
        });
      }
    });
  }

  getListStatus() {
    this.statusFilterList.next(this.userService.getStatusList());
  }

  getListRole = () => {
    this.userService.getAllRoleList().subscribe((value) => {
      if (value) {
        const _data = value.map((val) => {
          return { enum: val.enum_value, value: val.enum_string };
        });
        this.roleFilterList.next(_data);
      }
    });
  };

  updateTotalCount() {
    this.baseDatasource.totalItem$.subscribe((data) => {
      if (data) {
        this.totalCount.next(data);
      }
    });
  }

  updateCount() {
    this.baseDatasource.totalItem$.subscribe((data) => {
      if (data) {
        this.pageInfoService.updateTitle(`User List (` + data + `)`);
      } else {
        this.pageInfoService.updateTitle(`User List (0)`);
      }
    });
  }

  queryHandler() {
    this.activeRoute.queryParams.subscribe((data) => {
      this.string_filter = data.string_filter;
      this.FilterForm.controls['status'].setValue(data.status);
      this.FilterForm.controls['role'].setValue(data.role);
      this.FilterForm.controls['area_id'].setValue(data.area_id);
      this.isActiveFilter = !!(data.status || data.area_id || data.role);
      const param = this.paginationService.sliceQueryParams();
      if (param) {
        this.baseTable.loadDataTable(API.USER.LIST_USER, param);
      } else {
        this.baseTable.loadDataTable(API.USER.LIST_USER, '');
      }
      this.updateTotalCount();
      this.updateCount();
    });
  }

  initPageInfo() {
    this.baseDatasource.tableSubject.subscribe((data) => {
      if (data) {
        this.usersList = data;
        this.pageInfoService.updateBreadcrumbs(this.links, `(${data.length})`);
      }
    });
  }

  sortDataSource(sortParams: Sort) {
    const keyName = sortParams.active as keyof IListUser;
    if (!this.baseDatasource.isTableLoaded) {
      if (sortParams.direction === 'asc') {
        this.usersList = this.usersList.sort((a, b) => a[keyName].toString().localeCompare(b[keyName].toString()));
      } else if (sortParams.direction === 'desc') {
        this.usersList = this.usersList.sort((a, b) => b[keyName].toString().localeCompare(a[keyName].toString()));
      }
    }

    this.baseDatasource.tableSubject.next(this.usersList);
  }

  changePageEvent($event: BaseDatasource<any>) {
    this.filterService.changePageEvent($event, this.string_filter ?? '');
  }

  handleAction(id: string) {
    this.router.navigate([`/settings/user/` + id]).then(() => null);
  }

  handleBatchStatusChange = (event: MatChipListboxChange) => {
    this.userStatusControl.patchValue(event.value);
  };

  handleBatchRoleChange = (event: MatChipListboxChange) => {
    this.userRoleControl.patchValue(event.value);
  };

  handleChangeArea(event: string) {
    this.selectedArea = event;
  }

  setTableData() {
    this.tableColumns = [
      {
        key: 'code',
        title: 'Kode User',
        isSortable: true,
      },
      {
        key: 'name',
        title: 'Nama',
        isSortable: true,
      },
      {
        key: 'email',
        title: 'Email',
        isSortable: true,
      },
      {
        key: 'role_string',
        title: 'Role',
        isSortable: true,
      },
      {
        key: 'area_list',
        title: 'Area',
        isSortable: true,
      },
      {
        key: 'status',
        title: 'Status',
        isSortable: true,
      },
    ];
    this.tableColumns = this.utilities.privilegeTableColumns(this.privilegeDetail.value, this.tableColumns);
    this.displayedColumns = this.tableColumns.map((head) => head.key);
    this.baseDatasource = new BaseDatasource();
    if (this.usersList) {
      this.baseDatasource.tableSubject.next(this.usersList);
    }
  }

  sortTable(param: Sort) {
    const sortby = this.tableColumns.find((column) => column.key === param.active);
    param.active = sortby?.key ?? '';
    this.sortDataSource(param);
  }

  handleOpenFilter() {
    this.isOpenFilter = !this.isOpenFilter;
  }

  renderStringList = (arr: string[], show: number = 3) => {
    try {
      arr = arr.length > show ? arr.slice(0, show) : arr;
      return arr.join(', ');
    } catch {
      return;
    }
  };

  displayArea(value: IListUser | any) {
    if (!Object.keys(value).length) return;
    if (!value.area_list.length) return '-';
    return this.renderStringList(value.area_list, 5);
  }
}
