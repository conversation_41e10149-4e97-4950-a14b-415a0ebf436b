import { ChangeDetectorRef, Component, Input, <PERSON>Changes, OnDestroy, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { EnumDeactivateUser, IDeactivateUserResponse, IDetailUser, StatusUser } from '@models/user.model';
import { BehaviorSubject, Observable, of, Subscription } from 'rxjs';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { InputSelectScopeRoleComponent } from '@shared/components/input-select-scope-role/input-select-scope-role.component';
import { ISelectedOption } from '@shared/components/input-select-scope-role/input-select-scope-role.interface';
import { UserService } from '../../user.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { API } from '@config/constants/api.constant';
import { EnumRole } from '@models/role.model';
import { IGenericLabelValue, IGenericValueDisplay } from '@shared/interface/generic';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { IPayloadDeactivate } from '../../users.interface';

@Component({
  selector: 'app-add-select-deactive-scope',
  templateUrl: './add-select-deactive-scope.component.html',
  styleUrls: ['./add-select-deactive-scope.component.scss'],
})
export class AddSelectDeactiveScopeComponent implements OnInit, OnChanges, OnDestroy {
  @Input() detail: IDetailUser;

  userDetail$: Observable<IDetailUser>;
  isLoading = new BehaviorSubject(false);
  areaWarehouseValue: BehaviorSubject<IGenericLabelValue[]> = new BehaviorSubject([] as IGenericLabelValue[]);
  reasonDeactivate: IGenericValueDisplay[] = [];
  responseDeactivateUser: IDeactivateUserResponse = {} as IDeactivateUserResponse;
  responseActivateUser: IDeactivateUserResponse = {} as IDeactivateUserResponse;

  form: FormGroup;

  @ViewChild('modalSelectDeactiveScope') modalSelectDeactiveScope: ModalComponent;
  modalConfigSelectDeactiveScope = <ModalConfig>{
    modalTitle: 'NONAKTIFKAN USER',
    closeButtonLabel: 'Batal',
    dismissButtonLabel: 'Lanjutkan',
    disableDismissButton: () => this.validateFormScope(),
    onDismiss: () => this.onSubmitForm(),
  };

  @ViewChild('modalResponse') modalResponse: ModalComponent;
  modalConfigResponse: ModalConfig = {
    hideDetailButton: () => true,
    dismissButtonLabel: 'Oke',
    hideCloseButton(): boolean {
      return true;
    },
  };

  @ViewChild('selectScopeRole') selectScopeRole: InputSelectScopeRoleComponent;

  // selected scope data role
  selectedScopeRole: ISelectedOption;

  get DeactivateForm() {
    return this.form;
  }

  get ReasonForm() {
    return this.form.controls.reason;
  }

  get OtherReasonForm() {
    return this.form.controls.other_reason;
  }

  private unsubscribe: Subscription[] = [];

  constructor(private userService: UserService, private fb: FormBuilder, private cdr: ChangeDetectorRef) {}

  ngOnInit() {
    this.isLoading.next(true);
    this.userDetail$ = of(this.detail);
    this.initForm();
    this.generateReason();
    this.initValueChanges();
  }

  initForm() {
    this.form = this.fb.group({
      reason: this.fb.control('', Validators.required),
      other_reason: this.fb.control(''),
    });
  }

  generateReason() {
    this.reasonDeactivate = this.userService.generateReasonDeactivate();
  }

  initValueChanges() {
    const reason$ = this.ReasonForm.valueChanges.subscribe((value: string) => {
      if (value === EnumDeactivateUser.OTHER_REASON) {
        this.OtherReasonForm.setValidators(Validators.required);
      } else {
        this.OtherReasonForm.clearValidators();
      }
      this.OtherReasonForm.updateValueAndValidity();
    });
    this.unsubscribe.push(reason$);

    const user$ = this.userService.userDetailSubject.subscribe((value) => {
      if (!value) return;
      this.isLoading.next(false);
    });
    this.unsubscribe.push(user$);
  }

  setValueAreaWarehouse(value: IDetailUser) {
    const { list_area, list_sub_area, list_warehouse, role_enum } = value;
    const warehouseRole = [EnumRole.ADMIN_GUDANG, EnumRole.ADMIN_SCALES];
    if (!!list_sub_area?.length && list_area?.length) {
      const _change = list_sub_area.flatMap((value: any) => {
        return { value: value.id, label: value.name } as IGenericLabelValue;
      });
      this.areaWarehouseValue.next(_change);
    } else if (!!list_area?.length && !list_sub_area?.length && !warehouseRole.includes(role_enum)) {
      const _change = list_area.flatMap((value) => {
        return { value: value.id, label: value.name } as IGenericLabelValue;
      });
      this.areaWarehouseValue.next(_change);
    } else if (!!list_warehouse?.length && warehouseRole.includes(role_enum)) {
      const _change = list_warehouse.flatMap((value: any) => {
        return { value: value.id, label: value.name } as IGenericLabelValue;
      });
      this.areaWarehouseValue.next(_change);
    } else {
      this.areaWarehouseValue.next([] as IGenericLabelValue[]);
    }
  }

  openDialog() {
    return this.modalSelectDeactiveScope.open();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.detail && !changes.detail.firstChange) {
      this.userDetail$ = of(changes.detail.currentValue);
    }
    if (changes.detail.currentValue) {
      this.setValueAreaWarehouse(changes.detail.currentValue);
    }
  }

  onSelectScopeRole(e: any) {
    this.selectedScopeRole = e;
  }

  validateFormScope() {
    const { role_enum, status_enum } = this.detail;
    const roles = [EnumRole.ADMIN_GUDANG, EnumRole.ADMIN_SCALES, EnumRole.ADMIN, EnumRole.FINANCE, EnumRole.ACCOUNTANT, EnumRole.FIELD_ASSISTANT];
    if (!roles.includes(role_enum) && status_enum === StatusUser.ACTIVE) {
      return !(this.form.valid && this.selectScopeRole.DataForm.valid);
    } else {
      return this.form.invalid;
    }
  }

  onSubmitForm() {
    const payload: IPayloadDeactivate = this.userService.payloadSubmitDeactivateScope(
      this.selectedScopeRole,
      this.areaWarehouseValue.value,
      this.ReasonForm.value,
      this.OtherReasonForm.value
    );
    const deactivate$ = this.userService.deactivateUser(payload).subscribe((value) => {
      if (!value) return;
      this.responseDeactivateUser = value;
      this.modalResponse.open().then();
      this.cdr.detectChanges();
    });
    this.unsubscribe.push(deactivate$);
  }

  closeAssignModalResponse() {
    window.location.reload();
  }

  validateOptionalInput(): boolean {
    const roles = [EnumRole.ADMIN_GUDANG, EnumRole.ADMIN, EnumRole.FINANCE, EnumRole.ACCOUNTANT];
    return roles.includes(this.detail.role_enum);
  }

  showSelectRole() {
    const roles = [EnumRole.FIELD_ASSISTANT];
    return this.detail.status_enum === StatusUser.ACTIVE && !roles.includes(this.detail.role_enum);
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
  protected readonly API = API;
  protected readonly EnumRole = EnumRole;
  protected readonly Object = Object;
  protected readonly EnumDeactivateUser = EnumDeactivateUser;
}
