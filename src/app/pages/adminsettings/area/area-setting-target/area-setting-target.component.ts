import { Component, OnInit, ViewChild } from '@angular/core';
import { PageInfoService, PageLink } from '@metronic/layout';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { ModalConfig, ModalOption } from '@shared/components/modal/modal.interface';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { AreaService } from '../area.service';
import { BehaviorSubject, Subscription } from 'rxjs';
import { IMasterSettings } from '../area.interface';
import { UtilitiesService } from '@services/utilities.service';
import { IHeaderExportTarget, IHeaderExportForecast, masterType } from '../area.interface';

@Component({
  selector: 'app-area-setting-target',
  templateUrl: './area-setting-target.component.html',
  styleUrls: ['./area-setting-target.component.scss'],
})
export class AreaSettingTargetComponent implements OnInit {
  links: PageLink[];
  title: string = 'Download file dokumen Target dan Forecast Area';
  areaID: string;
  pesticide_id: string = '45de5d0d-1ef1-4af9-8fd7-62c878f9dda7';
  seed_id: string = 'e5a2338b-62c0-4722-8582-2b4adafe71c4';
  assetIcon = STRING_CONSTANTS.ICON;
  private unsubscribe = <Subscription[]>[];
  periodes: string = 'd3747ea7-6970-413c-955f-f7537ce76f07';
  label_periodes: string = 'Periode 2025';
  area_name: string = '';
  year: number = 2025;

  onPeriodeReceived(event: any) {
    this.periodes = event.value;
    this.label_periodes = event.label;
  }

  @ViewChild('modalDataDownload') modalDataDownload: ModalComponent;
  modalDataDownloadConfig: ModalConfig = {
    showFooter: false,
    showHeader: false,
  };

  @ViewChild('modalConfirmCancel') modalConfirmCancel: ModalComponent;
  modalConfirmCancelConfig: ModalConfig = {
    modalTitle: 'BATALKAN SETTING TARGET',
    closeButtonLabel: 'Cancel',
    dismissButtonLabel: 'Lanjutkan',
    onDismiss: () => this.handleModalDismiss(),
  };

  modalOptionDownloadConfig: ModalOption = {
    fullscreen: 'md',
    size: 'lg',
  };

  constructor(
    private pageInfoService: PageInfoService,
    private activeRoute: ActivatedRoute,
    private router: Router,
    private areaService: AreaService,
    private utils: UtilitiesService
  ) {
    this.areaID = this.activeRoute.snapshot.params.id;
    this.area_name = this.areaService.areaDetailSubject.value.area_name ?? '';
  }

  ngOnInit() {
    this.initPageInfo();
    this.setDataDetail();
    this.year = new Date().getFullYear();
  }

  exportTarget: BehaviorSubject<IHeaderExportTarget[]> = new BehaviorSubject<IHeaderExportTarget[]>([]);
  exportForecast: BehaviorSubject<IHeaderExportForecast[]> = new BehaviorSubject<IHeaderExportForecast[]>([]);

  masterSettings: IMasterSettings = {
    type: [{ pesticide: this.pesticide_id, seed: this.seed_id }],
  };

  modals = [
    { title: 'Target Pestisida', url: masterType.target, type: this.masterSettings.type[0].pesticide },
    { title: 'Target Benih', url: masterType.target, type: this.masterSettings.type[0].seed },
    { title: 'Forecast Pestisida', url: masterType.forecast, type: this.masterSettings.type[0].pesticide },
    { title: 'Forecast Benih', url: masterType.forecast, type: this.masterSettings.type[0].seed },
  ];

  getDownloadCSV(title: string, url: string, type: string) {
    const listData$ = this.areaService.getDownloadCSV({ url: url, business_type: type, area_id: this.areaID, periode_id: this.periodes });
    const listData = listData$.subscribe((resp) => {
      if (!resp) return;
      const { data } = resp;
      if (url === masterType.forecast) {
        this.exportForecast.next(data as IHeaderExportForecast[])
      } else {
        this.exportTarget.next(data as IHeaderExportTarget[])
      }
      this.exportToCSV(url, title);
    });
    this.unsubscribe.push(listData);
  }

  exportToCSV(url : string, title : string) {
    return url === masterType.forecast ?
      this.utils.downloadFile<IHeaderExportForecast>(this.exportForecast.value, title + ' ' + this.area_name + ' Periode ' + this.year, Object.keys(this.exportForecast.value[0])) :
      this.utils.downloadFile<IHeaderExportTarget>(this.exportTarget.value, title + ' ' + this.area_name + ' Periode ' + this.year, Object.keys(this.exportTarget.value[0]))
  }

  setDataDetail() {
    this.links = [
      { path: `settings/area/${this.areaID}`, title: 'Detail Area', isActive: false },
      { title: '', path: '', isActive: false, isSeparator: true },
      { path: '', title: this.title, isActive: true },
    ];
    this.pageInfoService.updateBreadcrumbs(this.links);
  }

  handleModalCancel() {
    return this.modalConfirmCancel.open();
  }

  async handleModalDismiss() {
    return this.router.navigate([`settings/area/${this.areaID}`]).then();
  }

  initPageInfo() {
    this.pageInfoService.updateTitle('Setting Target', undefined, false);
  }

  handleDownload() {
    this.modalDataDownload.open().then();
  }

}
