import { Component, EventEmitter, Input, OnChanges, OnInit, Output, ViewChild } from '@angular/core';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { BehaviorSubject, Subscription } from 'rxjs';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { IMasterSettings, IPeriode } from '../../../area.interface';
import { BaseDatasource } from '@shared/base/base.datasource';
import { InputSelectMaterialInterface, ISelectPeriode } from '@shared/components/form/input-select-material/input-select-material.interface';
import { MatSelectChange } from '@angular/material/select';
import { InputSelectMaterialComponent } from '@shared/components/form/input-select-material/input-select-material.component';
import { AreaService } from '../../../area.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-area-setting-target-form',
  templateUrl: './area-setting-target-form.component.html',
  styleUrls: ['./area-setting-target-form.component.scss'],
})
export class AreaSettingTargetFormComponent implements OnInit, OnChanges {
  @Input() cbdID: string;
  @Output() formPayload: EventEmitter<any> = new EventEmitter();
  @Output() handleCancel: EventEmitter<boolean> = new EventEmitter();
  @Output() periode: EventEmitter<any> = new EventEmitter();

  STRING_CONSTANTS = STRING_CONSTANTS;
  baseDatasource: BaseDatasource<IPeriode>;
  isLoadingPostData: BehaviorSubject<boolean>;
  lastSelectedPeriode: ISelectPeriode;
  pesticide_id: string = '45de5d0d-1ef1-4af9-8fd7-62c878f9dda7';
  seed_id: string = 'e5a2338b-62c0-4722-8582-2b4adafe71c4';
  periode_data: any[] = [];

  @ViewChild('modalResponseCreate') modalResponseCreate: ModalComponent;
  modalResponseCreateConfig: ModalConfig = {
    hideCloseButton: () => true,
    dismissButtonLabel: 'Oke',
    onDismiss: () => this.handleModalDismiss(),
  };
  modalResponseMessage = new BehaviorSubject<string>('');
  @ViewChild('inputPeriodeList') inputPeriodeList: InputSelectMaterialComponent;
  masterSettings: IMasterSettings = {
    type: [{ pesticide: this.pesticide_id, seed: this.seed_id }],
  };
  inputFile = [
    {
      title: 'Target Pestisida',
      name: 'target_pesticide',
      url: 'master-target',
      type: this.masterSettings.type[0].pesticide,
    },
    { title: 'Target Benih', name: 'target_seed', url: 'master-target', type: this.masterSettings.type[0].seed },
    {
      title: 'Forecast Pestisida',
      name: 'forecast_pesticide',
      url: 'master-forecast',
      type: this.masterSettings.type[0].pesticide,
    },
    { title: 'Forecast Benih', name: 'forecast_seed', url: 'master-forecast', type: this.masterSettings.type[0].seed },
  ];
  listPeriodeSubject = new BehaviorSubject<InputSelectMaterialInterface[]>([]);
  fbSettingTargetForm: FormGroup = this.fb.group({
    id_periode: new FormControl('', [Validators.required]),
    target_pesticide: new FormControl('', [Validators.required]),
    target_seed: new FormControl('', [Validators.required]),
    forecast_pesticide: new FormControl('', [Validators.required]),
    forecast_seed: new FormControl('', [Validators.required]),
  });
  csvData: string[][] = [[], [], [], []];
  years: string[] = ['2025'];
  private readonly debounceTimeMs = 1000;
  private unsubscribe = <Subscription[]>[];

  constructor(private fb: FormBuilder, private areaService: AreaService, private router: Router) {}

  async handleModalDismiss() {
    return this.router.navigate([`settings/area/${this.cbdID}`]).then();
  }

  handlePeriodeChange(e: MatSelectChange) {
    this.periode.emit(e.value);
    return (this.lastSelectedPeriode = e.value);
  }

  getListPeriode() {
    const listPeriode$ = this.areaService.getListPeriode();
    const listPeriodeSubs = listPeriode$.subscribe((resp) => {
      if (!resp) return;
      const { data } = resp;
      this.periode_data = data;
      this.listPeriodeSubject.next(this.areaService.mapInputSelectPeriode(data));
    });
    this.unsubscribe.push(listPeriodeSubs);
  }

  validateForm(): boolean {
    const id_periode = this.fbSettingTargetForm.get('id_periode')?.value || '';
    return [id_periode].some((value) => value === '');
  }

  postDataJSON(json: string[], url: string, type: string, year: string) {
    const foundPeriode = this.periode_data.find((periode) => periode.name === 'Periode Tahun ' + year);
    const periodeIdResult = foundPeriode ? foundPeriode.id : this.lastSelectedPeriode.value;
    const postDataJson = this.areaService
      .postDataJSON({
        json: json,
        url: url,
        business_type: type,
        periode_id: periodeIdResult,
      })
      .subscribe((resp) => {
        if (resp && !resp.success) {
          return this.modalResponseCreate.close();
        }
        if (resp && resp.success) {
          this.modalResponseMessage.next(resp.message);
        }
      });
    this.unsubscribe.push(postDataJson);
    return true;
  }

  handleFileInput(index: number, event: { index: number; json: any; name: string }) {
    this.csvData[index] = event.json;
    this.years[index] = event.name;
    this.fbSettingTargetForm.patchValue({
      target_pesticide: this.csvData[0] || '',
      target_seed: this.csvData[1] || '',
      forecast_pesticide: this.csvData[2] || '',
      forecast_seed: this.csvData[3] || '',
    });
  }

  ngOnInit(): void {
    this.getListPeriode();
  }

  ngOnChanges(): void {}

  async handleSubmit() {
    const file = this.inputFile;
    const target_pesticide = this.fbSettingTargetForm.get('target_pesticide')?.value || '';
    const target_seed = this.fbSettingTargetForm.get('target_seed')?.value || '';
    const forecast_pesticide = this.fbSettingTargetForm.get('forecast_pesticide')?.value || '';
    const forecast_seed = this.fbSettingTargetForm.get('forecast_seed')?.value || '';
    if (!!target_pesticide.length) {
      this.postDataJSON(target_pesticide, file[0].url, file[0].type, this.years[0]);
    }
    if (!!target_seed.length) {
      this.postDataJSON(target_seed, file[1].url, file[1].type, this.years[1]);
    }
    if (!!forecast_pesticide.length) {
      this.postDataJSON(forecast_pesticide, file[2].url, file[2].type, this.years[2]);
    }
    if (!!forecast_seed.length) {
      this.postDataJSON(forecast_seed, file[3].url, file[3].type, this.years[3]);
    }
    return this.modalResponseCreate.open();
  }
}
