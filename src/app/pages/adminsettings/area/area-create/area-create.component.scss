
.radio-card {

  &__item {
    flex: 1;
    border: 1px solid #d1d1d1;
    border-radius: 8px;
    transition: 120ms all ease-in-out;
  }

  &__item .title {
    color: var(--kt-gray-700);
  }

  &__item .svg-icon {
    filter: grayscale(1);

    svg {
      width: 3.429rem!important;
      height: 3.429rem!important;
    }
  }

  &__item.active,
  &__item:hover {
    color: var(--kt-primary);
    border-color: var(--kt-primary);
    background: #F1FCD6;

    .title {
      color: var(--kt-primary);
    }

    .svg-icon {
      filter: grayscale(0);
    }
  }

  .form-check-input[type="radio"] {
    -webkit-appearance: none;
    appearance: none;
    margin: 0;
    background: transparent;
    border: 1px solid var(--kt-gray-700);
    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
      content: "";
      width: 10px;
      height: 10px;
      box-shadow: inset 10px 10px #A5CE46;
      border-radius: 50%;
      transform: scale(0);
      transition: 120ms transform ease-in-out;
    }

    &:checked::before {
      transform: scale(1);
    }
  }

  &__item.active .form-check-input[type="radio"] {
    border-color: #A5CE46;
  }
}
