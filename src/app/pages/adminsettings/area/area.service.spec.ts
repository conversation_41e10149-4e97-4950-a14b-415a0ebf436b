import { TestBed } from '@angular/core/testing';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { of } from 'rxjs';
import { AreaService } from './area.service';
import { BaseService } from '@services/base-service.service';
import { DUMMY_AREA_DATA, DUMMY_GENERATED_SUBAREA_CODE, DUMMY_PROVINCE_LIST, DUMMY_REGENCY_LIST } from '@unit-testing-data/area.data';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

describe('AreaService', () => {
  let areaService: AreaService;
  let baseServiceSpy = jasmine.createSpyObj('BaseService', ['getData']);

  const { AREA_ID, DETAIL_HEADER, LIST_ALL, DETAIL_EDIT, SUBAREA_ID, DETAIL_SUB_AREA } = DUMMY_AREA_DATA;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        AreaService,
        {
          provide: BaseService,
          useValue: baseServiceSpy,
        },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    areaService = TestBed.inject(AreaService);
  });

  it('should be created', () => {
    expect(areaService).toBeTruthy();
  });

  // getAreaDetailHeader
  it('getAreaDetailHeader() should retrieve detail header data ', (done) => {
    baseServiceSpy.getData.and.returnValue(of({ data: DETAIL_HEADER }));
    areaService.getAreaDetailHeader(AREA_ID).subscribe((resp) => {
      expect(resp).toBeTruthy();
      expect(resp.data).toEqual(DETAIL_HEADER);
      done();
    });
  });

  // getAreaListAll
  it('getAreaListAll() should retrieve area list data', (done) => {
    baseServiceSpy.getData.and.returnValue(of({ success: true, data: LIST_ALL }));
    areaService.getAreaListAll().subscribe((resp) => {
      expect(resp).toBeTruthy();
      expect(resp.data).toEqual(LIST_ALL);
      done();
    });
  });

  // getAreaDetailEdit
  it('getAreaDetailEdit() should retrieve edit area detail data', (done) => {
    baseServiceSpy.getData.and.returnValue(of({ data: DETAIL_EDIT }));
    areaService.getAreaDetailEdit(AREA_ID).subscribe((resp) => {
      expect(resp).toBeTruthy();
      expect(resp.data).toEqual(DETAIL_EDIT);
      done();
    });
  });

  // getSubareaDetail
  it('getSubAreaDetail() should retrieve subarea detail data', (done) => {
    baseServiceSpy.getData.and.returnValue(of({ data: DETAIL_SUB_AREA }));
    areaService.getSubareaDetail(SUBAREA_ID).subscribe((resp) => {
      expect(resp).toBeTruthy();
      expect(resp.data).toEqual(DETAIL_SUB_AREA);
      done();
    });
  });

  // getProvinceList
  it('getProvinceList() should retrieve province list data ', (done) => {
    baseServiceSpy.getData.and.returnValue(of({ data: DUMMY_PROVINCE_LIST }));
    areaService.getProvinceList().subscribe((resp) => {
      expect(resp).toBeTruthy();
      expect(resp.data).toEqual(DUMMY_PROVINCE_LIST);
      done();
    });
  });

  // getListRegencyInArea
  it('getListRegencyInArea() should retrieve regency list data from selected area id', (done) => {
    baseServiceSpy.getData.and.returnValue(of({ data: DUMMY_REGENCY_LIST }));
    areaService.getListRegencyInArea(AREA_ID).subscribe((resp) => {
      expect(resp).toBeTruthy();
      expect(resp.data).toEqual(DUMMY_REGENCY_LIST);
      done();
    });
  });

  // generateSubAreaCode
  it('generateSubAreaCode() should retrieve generated subarea code from selected area id', (done) => {
    baseServiceSpy.getData.and.returnValue(of({ data: DUMMY_GENERATED_SUBAREA_CODE }));
    areaService.generateSubAreaCode(AREA_ID).subscribe((resp) => {
      expect(resp).toBeTruthy();
      expect(resp.data).toEqual(DUMMY_GENERATED_SUBAREA_CODE);
      done();
    });
  });
});
