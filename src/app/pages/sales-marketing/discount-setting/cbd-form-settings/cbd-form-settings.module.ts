import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RouterModule, Routes } from '@angular/router';
import { InlineSVGModule } from 'ng-inline-svg-2';

import { CbdFormSettingsComponent } from './cbd-form-settings.component';
import { ComponentsModule } from '@shared/components/components.module';
import { ProductsModule } from '../../../products/products.module';
import { CbdFormComponent } from './cbd-form/cbd-form.component';
import {DiscountSettingModule} from "../discount-setting.module";

const routing: Routes = [
  {
    path: 'form',
    component: CbdFormSettingsComponent,
  },
];

@NgModule({
  declarations: [CbdFormSettingsComponent, CbdFormComponent],
    imports: [CommonModule, RouterModule.forChild(routing), FormsModule, ReactiveFormsModule, ComponentsModule, ProductsModule, InlineSVGModule, MatProgressSpinnerModule, DiscountSettingModule],
    exports: [CbdFormSettingsComponent, CbdFormComponent],
})
export class CbdFormSettingsModule {}
