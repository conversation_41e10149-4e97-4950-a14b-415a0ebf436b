<app-card [cardBodyClasses]="'p-18'">
  <ng-container cardBody>
    <h2 class="form-title mb-7">Data CBD Discount</h2>
    <app-cbd-form
      [cbdID]="params.id"
      [mode]="params.mode"
      (formPayload)="handleFormPayload($event)"
      (handleCancel)="modalConfirmCancel.open()"
    ></app-cbd-form>
  </ng-container>
</app-card>

<app-modal #modalResponseCreate [modalConfig]="modalResponseCreateConfig">
  <div class="d-flex flex-column justify-content-center align-items-center">
    <ng-container *ngIf="isLoadingPostData | async; else responseDone">
      <div class="mt-8">
        <mat-spinner></mat-spinner>
      </div>
    </ng-container>

    <ng-template #responseDone>
      <span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT"></span>
      <div class="mb-n8">
        <span>{{ modalResponseMessage.value }}</span>
      </div>
    </ng-template>
  </div>
</app-modal>

<app-modal #modalConfirmCancel [modalConfig]="modalConfirmCancelConfig">
  <div class="text-center">
    <p>Apakah anda yakin untuk keluar dari halaman discount setting? Data yang sudah diinput akan hilang.</p>
  </div>
</app-modal>
