<app-modal-form #modalCancel [modalConfig]="modalConfigCancel" [modalOptions]="{ size: 'md' }">
  <p>Silakan input alasan pembatalan Program Marketing:</p>
  <form [formGroup]="form">
    <fieldset>
      <textarea formControlName="cancel_reason" class="form-control form-control-solid min-h-100px" cols="10" placeholder="Silahkan input alasan pembatalan" rows="3"></textarea>
    </fieldset>
  </form>

  <div class="mt-8 d-flex flex-nowrap align-items-center">
    <button type="button" mat-button class="btn btn-outline btn-outline-secondary text-primary w-100 me-lg-3" (click)="close()">
      <span>{{ modalConfigCancel.closeButtonLabel }}</span>
    </button>
    <button
      type="button"
      class="btn btn-primary w-100 text-white"
      mat-raised-button
      color="primary"
      [disabled]="modalConfigCancel.disableDismissButton !== undefined && modalConfigCancel.disableDismissButton()"
      (click)="handleSubmit()"
    >
      <span>{{ modalConfigCancel.dismissButtonLabel }}</span>
    </button>
  </div>
</app-modal-form>
<app-modal-response #modalResponse [data]="dataModalResponse" [isLoading]="isLoadingSubject" [callbackSubmit]="onDismissModalResponse" />
