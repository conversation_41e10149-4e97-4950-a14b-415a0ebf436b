<app-input-search (actionFilter)="actionSearch($event)" [isFinishLoadingSubject]="finishLoadingSubject"
                  [value]="searchInputValue" placeholder="Cari nama toko/pemilik" />
<div class="ms-auto position-relative">
  <div class="d-flex align-items-center justify-content-center">

    <div class="ms-3">
      <app-filter-table
        (actionClick)="toggleOpenFilter()"
        (actionReset)="actionResetFilter()"
        [isActiveFilter]="filterInputActivated"
        [isFinishLoadingSubject]="finishLoadingSubject"
        [isOpenFilter]="filterInputOpened"
        [resetLabel]="'Reset Filter'"
      >
        <div class="menu menu-sub menu-sub-dropdown w-300px w-md-350px show filter-body" id="kt-toolbar-filter">
          <div class="px-7 py-5">
            <div class="d-flex align-items-center justify-content-between">
              <div class="fs-4 text-dark fw-bold">Filter</div>
              <div>
                <span (click)="toggleOpenFilter()" [inlineSVG]="STRING_CONSTANTS.ICON.IC_CLOSE_MODAL"
                      class="svg-icon svg-icon2 cursor-pointer"></span>
              </div>
            </div>
          </div>

          <div class="separator border-gray-300"></div>

          <form (ngSubmit)="actionSubmitFilter()" [formGroup]="filterForm" class="form w-100">
            <div class="px-7 py-5">
              <div class="mb-10">
                <label class="form-label mb-6">Progress Penyelesaian Program</label>
                <div class="form-check p-0">
                  <mat-radio-group [formControlName]="'progress'" class="w-100">
                    <ng-container *ngFor="let option of filterRetailerProgress">
                      <mat-radio-button [value]="option.value" class="w-100 d-block">
                        {{ option.display }}
                      </mat-radio-button>
                    </ng-container>
                  </mat-radio-group>
                </div>
              </div>

              <ng-container *ngIf="dataInfoProgram$ | async as programTerm">
                <div class="mb-10">
                  <label class="form-label mb-4">Area</label>
                  <app-input-select
                    [class]="'w-100'"
                    [options]="listRetailerAreaSubject.value"
                    formControlName="area_id"
                    ngDefaultControl
                    placeholder="Pilih area"
                    (handleChangeData)="handleChangeAreaFilter($event)"
                  />
                </div>

<!--                Di Hide-->

<!--                <div class="mb-4" *ngIf="programTerm.program_term.scope_enum !== 'AREA'">-->
<!--                  <label class="form-label mb-4">Sub Area</label>-->
<!--                  <app-input-select [class]="'w-100'" [options]="listRetailerSubAreaSubject.value"-->
<!--                                    formControlName="sub_area_id" ngDefaultControl placeholder="Pilih sub area" />-->
<!--                </div>-->
              </ng-container>

              <div class="d-flex justify-content-end">
                <button (click)="actionResetFilter()" [disabled]="validateFilter()" class="btn btn-outline me-4"
                        mat-stroked-button type="reset">
                  <span class="text-primary">Reset</span>
                </button>
                <button [disabled]="validateFilter()" class="btn btn-primary" color="primary" mat-raised-button
                        type="submit">
                  <span class="text-white">Terapkan</span>
                </button>
              </div>
            </div>
          </form>
        </div>
      </app-filter-table>
    </div>
  </div>
</div>
