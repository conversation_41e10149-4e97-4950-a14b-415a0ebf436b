import { EnumProgramMarketingRewardType, EnumProgramMarketingScope } from '../../program-marketing/program-marketing.enum';
import { EnumProgramMarketingRewardRule, EnumProgramMarketingScanRule, EnumProgramRetailerStatusEnum, EnumProgramRetailerTargetScanType } from '../program-marketing-retailer.enum';
import { IGenericNameUrl } from '@shared/interface/generic';
import { FormArray, FormControl, FormGroup } from '@angular/forms';

interface IFormSection {
  revision_note?: string;
}

interface IFormSectionScope {
  scope_enum: EnumProgramMarketingScope;
  area_ids: string[];
  subarea_ids: string[];
  sub_area_ids?: string[]; //temp.fix different interface in create and edit
}

interface IFormSectionScope__Program extends IFormSectionScope {
  retailer_ids: string[];
}

interface IFormSectionScope__ProductOrigin extends IFormSectionScope {
  distributor_ids: string[];
}

interface IFormSectionReward {
  reward_type_enum: EnumProgramMarketingRewardType;
  mai_product: {
    variant_id: string;
    variant_name?: string;
    qty: number;
    maximum_budget: number;
  };
  non_mai_product: {
    product_other_reward: string;
    maximum_budget: number;
    reward_url: string;
  };
}
interface IFormSectionReward__MultiLevel extends IFormSectionReward {
  enum_level: string;
}

export interface IProgramProductScan {
  variant_id: string;
  variant_name?: string;
  brand_id?: string;
  brand_name?: string;
  delivery_unit?: string;
  conversion?: IProgramProductScan__Conversion[];
  value?: number;
}

export interface IProgramProductScan__Conversion {
  variant_id: string;
  variant_name?: string;
  qty_origin: number;
  qty_conversion: number;
}

export interface IFormMPR {
  status_enum: EnumProgramRetailerStatusEnum;
  information: IFormMPR__ProgramInformation;
  program_term: IFormMPR__ProgramTerm;
  target_scan_term: IFormMPR__TargetScanTerm;
  product_scan_term: IFormMPR__ProductScanTerm;
  reward_term: IFormMPR__RewardTerm;
}

// sections form
export interface IFormMPR__ProgramInformation extends IFormSection {
  program_name: string;
  period_start: string;
  period_end: string;
  management_note: string;
  reference_number: string;
  document_url: IGenericNameUrl[];
}

export interface IFormMPR__ProgramTerm extends IFormSection {
  scope: IFormSectionScope__Program;
  product_origin: IFormSectionScope__ProductOrigin;
  is_combined: boolean;
}

export interface IFormMPR__TargetScanTerm extends IFormSection {
  rule_target: { is_multilevel_target: boolean | string; enum_level: string | null };
  type_target: EnumProgramRetailerTargetScanType;
}

export interface IFormMPR_ProductScanTerm__Product {
  variant_id: string | null;
  brand_id: string | null;
  value: number;
}
export interface IFormMPR__ProductScanTerm extends IFormSection {
  rule_scan: EnumProgramMarketingScanRule;
  product_scan: IProgramProductScan[] | null;
  target_scan_qr: IFormMPR_ProductScanTerm__Product[] | null;
  multilevel_target: { enum_level: string; products: IFormMPR_ProductScanTerm__Product[]; value: number }[] | null;
  multi_level_target?: { enum_level: string; products: IFormMPR_ProductScanTerm__Product[]; value: number }[] | null; // temp.fix different be response key
  target_program: number | null;
  rule_scan_enum?: EnumProgramMarketingScanRule; // temp.fix response key changes from be
}

export interface IFormMPR__RewardTerm extends IFormSection {
  multiplication_enum: EnumProgramMarketingRewardRule;
  reward: IFormSectionReward | null;
  multilevel_reward: IFormSectionReward__MultiLevel[] | null;
  multi_level_reward?: IFormSectionReward__MultiLevel[] | null; //temp.fix different be response key
}

export interface IFormMPR__Payload {
  status_enum?: EnumProgramRetailerStatusEnum;
  information: IFormMPR__ProgramInformation;
  program_term: IFormMPR__ProgramTerm;
  target_scan_term: IFormMPR__TargetScanTerm;
  product_scan_term: IFormMPR__ProductScanTerm;
  reward_term: IFormMPR__RewardTerm;
}

// form type
type ConversionFormGroup = FormGroup<{
  variant_id: FormControl<{ id: string; name: string } | null>;
  qty_origin: FormControl<string | null>;
  qty_conversion: FormControl<string | null>;
}>;

export type ProductScanFormGroup = FormGroup<{
  id: FormControl<string | null>;
  name: FormControl<string | null>;
  delivery_unit: FormControl<string | null>;
  is_variant: FormControl<boolean | null>;
  conversion: FormArray<ConversionFormGroup>;
}>;

export type TargetScanQrFormGroup = FormGroup<{
  name: FormControl<string | null>;
  variant_id: FormControl<string | null>;
  brand_id: FormControl<string | null>;
  delivery_unit: FormControl<string | null>;
  value: FormControl<string | null>;
}>;

type MultiLevelProductFormGroup = FormGroup<{
  name: FormControl<string | null>;
  variant_id: FormControl<string | null>;
  brand_id: FormControl<string | null>;
  delivery_unit: FormControl<string | null>;
  value: FormControl<string | null>;
}>;

export type MultiLevelTargetFormGroup = FormGroup<{
  enum_level: FormControl<string | null>;
  value: FormControl<string | null>;
  products: FormArray<MultiLevelProductFormGroup>;
}>;
