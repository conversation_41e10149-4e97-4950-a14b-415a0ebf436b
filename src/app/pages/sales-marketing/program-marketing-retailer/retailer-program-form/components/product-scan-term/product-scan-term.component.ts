import { AfterViewInit, Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { RetailerProgramFormService } from '../../retailer-program-form.service';
import { InputSelectInterface } from '@shared/components/form/input-select/input-select.interface';
import { EnumProgramMarketingScanRule, EnumProgramRetailerSection, EnumProgramRetailerTargetScanType } from '../../../program-marketing-retailer.enum';
import { BehaviorSubject, distinctUntilChanged, EMPTY, merge, Subscription, switchMap, tap } from 'rxjs';
import {
  IFormMPR__ProductScanTerm,
  IFormMPR_ProductScanTerm__Product,
  IProgramProductScan,
  MultiLevelTargetFormGroup,
  ProductScanFormGroup,
  TargetScanQrFormGroup,
} from '../../retailer-program-form.interface';
import { IProgramListBrandVariant } from '../../../program-marketing-retailer.interface';
import { IChips } from '@shared/components/v1/chips/chips.interface';
import { filter, map } from 'rxjs/operators';
import { AddProductScanComponent } from '../add-product-scan/add-product-scan.component';
import { AddProductConversionComponent } from '../add-product-conversion/add-product-conversion.component';
import { ActivatedRoute } from '@angular/router';
import { UtilitiesService } from '@services/utilities.service';

@Component({
  selector: 'app-product-scan-term',
  templateUrl: './product-scan-term.component.html',
  styleUrls: ['./product-scan-term.component.scss'],
})
export class ProductScanTermComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {
  @Input() data!: IFormMPR__ProductScanTerm;
  @ViewChild('inputProductScanRef') inputProductScanRef: AddProductScanComponent;
  @ViewChild('inputProductConversionRef') inputProductConversionRef: AddProductConversionComponent;

  optionsRadioRuleScan!: InputSelectInterface[];
  isLoadingSubject = new BehaviorSubject(false);
  form!: FormGroup;
  targetScanTermForm: FormGroup; // siblings form

  // temp data selected product scan data form
  selectedProductScanData!: ProductScanFormGroup[];
  // for scheduled program, store product scan data for disabled state comparison
  scheduledProductScan!: IChips[];

  get RuleTargetScan() {
    return <FormGroup>this.targetScanTermForm?.get('rule_target');
  }

  get TypeTargetScan() {
    return <FormControl>this.targetScanTermForm?.get('type_target');
  }

  get MultiLevelEnum() {
    return <FormControl>this.RuleTargetScan?.controls.enum_level;
  }

  get RuleScan() {
    return <FormControl>this.form.controls.rule_scan;
  }

  get TargetProgram() {
    return <FormControl>this.form.controls.target_program;
  }

  get ProductScanForm() {
    return <FormArray>this.form.controls.product_scan;
  }

  get TargetScanQrForm() {
    return <FormArray>this.form.controls.target_scan_qr;
  }

  get MultiLevelTargetForm() {
    return <FormArray>this.form.controls.multilevel_target;
  }

  getMultiLevelTargetProducts(idx: number) {
    return <FormArray>this.getMultiLevelTargetFormGroup(idx).get('products');
  }

  getMultiLevelTargetFormGroup(idx: number) {
    return <FormGroup>this.MultiLevelTargetForm.controls[idx];
  }

  get GetPayload() {
    return this.mapFormToPayload();
  }

  dataProductScan: IProgramProductScan[] = [];
  programID!: string;
  private isViewInitialized = false; // flag for ngAfterViewInit
  private unsubscribe: Subscription[] = [];

  constructor(private activeRoute: ActivatedRoute, private fb: FormBuilder, private mprFormService: RetailerProgramFormService, protected utils: UtilitiesService) {
    this.programID = this.activeRoute.snapshot.params.id;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.data?.currentValue && !changes.data.firstChange) {
      this.data = changes.data.currentValue;
      this.setupDataToForm(this.data);
    }
  }

  ngOnInit() {
    this.initForm();
    this.initFormSubscriptionChanges();
  }

  ngAfterViewInit() {
    this.isViewInitialized = true;
  }

  initForm() {
    this.form = this.fb.group({
      rule_scan: this.fb.control(null, [Validators.required]),
      target_program: this.fb.control(null),
      product_scan: this.fb.array<ProductScanFormGroup>([], Validators.required),
      target_scan_qr: this.fb.array<TargetScanQrFormGroup>([]),
      multilevel_target: this.fb.array<MultiLevelTargetFormGroup>([]),
    });

    this.getOptionsRadio();
  }

  initFormSubscriptionChanges() {
    const _formSubs = this.form.valueChanges.subscribe(() => this.mprFormService.updateSectionForm(this.form, EnumProgramRetailerSection.PRODUCT_SCAN_TERM));

    const _productScanSubs = this.ProductScanForm.valueChanges.subscribe((f) => {
      if (f.length < this.selectedProductScanData?.length) {
      }
      this.updateConversionData();
    });

    const _ruleScanSubs = this.RuleScan.valueChanges
      .pipe(distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)))
      .subscribe(() => this.handleTargetScanQrValidator());

    this.targetScanTermFormChanges();

    this.unsubscribe.push(_formSubs, _productScanSubs, _ruleScanSubs);
  }

  targetScanTermFormChanges() {
    const _targetScanTermSubs = this.mprFormService.targetScanTermForm$
      .pipe(
        filter((form): form is FormGroup => !!form),
        tap((form) => (this.targetScanTermForm = form)),
        switchMap((form) => {
          const _controls = ['rule_target', 'type_target'].map(
            (key) =>
              form.get(key)?.valueChanges.pipe(
                map((value) => ({
                  key,
                  value,
                }))
              ) ?? EMPTY
          );
          return merge(..._controls);
        }),
        distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr))
      )
      .subscribe((res: { key: string; value: any }) => {
        if (res.key === 'rule_target' && !this.utils.isObjectEmpty(res.value)) {
          const isMultilevel = res.value.is_multilevel_target === true || res.value.is_multilevel_target === 'true';
          this.handleGenerateTargetScanQrForm(isMultilevel);
        }
      });

    this.unsubscribe.push(_targetScanTermSubs);
  }

  handleSelectedProduct(selectedItems: IProgramListBrandVariant[]) {
    const existingProductIds = new Set(this.ProductScanForm.controls.map((ctrl) => ctrl.value.id));
    if (this.programID && !selectedItems.length) return;

    for (let i = this.ProductScanForm.controls.length - 1; i >= 0; i--) {
      const ctrl = this.ProductScanForm.at(i) as FormGroup;
      // temp.fix no delivery unit key from response data
      if (selectedItems.some((item) => item.id === ctrl.value.id) && !ctrl.value.delivery_unit) {
        ctrl.patchValue({ delivery_unit: selectedItems[0].delivery_unit });
        ctrl.updateValueAndValidity({ emitEvent: false });
      }

      if (!selectedItems.some((item) => item.id === ctrl.value.id)) {
        this.ProductScanForm.removeAt(i);
      }
    }

    // add new products
    selectedItems.forEach((item) => {
      if (!existingProductIds.has(item.id)) {
        this.ProductScanForm.push(
          this.createProductScanGroup({
            variant_id: item.is_variant ? item.id : '',
            variant_name: item.is_variant ? item.name : undefined,
            brand_id: !item.is_variant ? item.id : '',
            brand_name: !item.is_variant ? item.name : undefined,
            delivery_unit: item.delivery_unit,
            conversion: [],
          })
        );
      }
    });

    // keep disabled state in selected product scheduled program
    const selectedScheduledProduct = new Set(this.scheduledProductScan?.map((item) => item.id));
    if (selectedScheduledProduct.size > 0) {
      selectedItems.map((item) => {
        item.disabled = selectedScheduledProduct.has(item.id);
      });
    }

    this.handleGenerateTargetScanQrForm(this.isMultiLevelTarget());
    this.form.updateValueAndValidity();
  }

  handleGenerateTargetScanQrForm(isMultilevel: boolean) {
    if (!isMultilevel && this.isScanQrRule(EnumProgramMarketingScanRule.ACCUMULATION_PRODUCTS)) {
      this.generateTargetScanQRForm(undefined);
      this.TargetProgram.setValidators([Validators.required, Validators.min(1)]);
      this.TargetProgram.updateValueAndValidity();
    }

    if (isMultilevel) {
      const initialMultiLevelData = this.programID ? this.data?.multi_level_target ?? this.data?.multilevel_target : undefined;
      this.generateMultiLevelTargetForm(initialMultiLevelData);
      this.TargetProgram.clearValidators();
      this.TargetProgram.updateValueAndValidity();
    }
  }

  handleEmittedConversionForm(f: any) {
    // map product scan form - conversion value
    const _productScan = this.ProductScanForm;
    if (!_productScan) return;

    const mapChipsConversion = (data: { chips: IChips; qty_origin: string; qty_conversion: string }) => {
      const { chips, qty_origin, qty_conversion } = data;
      return this.fb.group({
        variant_id: chips,
        qty_origin,
        qty_conversion,
      });
    };

    this.ProductScanForm?.controls.forEach((ctrl) => {
      const product = ctrl.value;
      if (!product.is_variant) return;

      const matchedProduct = f.find((v: any) => {
        if (typeof v.variant_id === 'string') return v.variant_id === product.id;
        else if (typeof v.variant_id === 'object' && v.variant_id.value) return v.variant_id.value === product.id;
        return false;
      });

      const conversionFormArray = ctrl.get('conversion') as FormArray;
      conversionFormArray.clear(); // also to clear unselected

      if (matchedProduct) {
        matchedProduct.selected_chips.forEach((data: any) => conversionFormArray.push(mapChipsConversion(data)));
        conversionFormArray.updateValueAndValidity();
        ctrl.updateValueAndValidity();
      }
    });

    this.ProductScanForm.updateValueAndValidity();
  }

  handleTargetScanQrValidator() {
    const isAccumulation = this.isScanQrRule(EnumProgramMarketingScanRule.ACCUMULATION_PRODUCTS);
    // remove 'required' validator if accumulation
    this.TargetScanQrForm.controls.forEach((ctrlGroup) => {
      const valueControl = ctrlGroup.get('value');

      if (isAccumulation) valueControl?.clearValidators();
      else valueControl?.setValidators([Validators.required, Validators.min(1)]);

      valueControl?.updateValueAndValidity({ onlySelf: true });
    });
  }

  handleDisableInputMultilevelTarget(idx: number, ctrlName: string) {
    if (this.isDisableEdit()) return this.isAddedNewProductScan();

    // this.isMultiLevelTarget() and not in scheduled program
    if (idx > 0) {
      // disable input if previous level value is empty
      const _previousInputValue = this.getMultiLevelTargetFormGroup(idx - 1)?.get(ctrlName)?.value;
      return !_previousInputValue;
    }

    return false;
  }

  setupDataToForm(data: IFormMPR__ProductScanTerm | null) {
    if (!data) {
      this.form.reset();
      this.ProductScanForm.clear();
      this.MultiLevelTargetForm.clear();
      return;
    }

    this.isLoadingSubject.next(true);

    // patch top level controls
    const targetProgramValue = data.target_program ? this.utils.reverseFormatInternationalNumber(data.target_program) : null;
    this.form.patchValue(
      {
        rule_scan: data.rule_scan,
        target_program: targetProgramValue,
      },
      { emitEvent: false }
    );

    // populate productScanForm
    this.ProductScanForm.clear();
    if (data.product_scan && data.product_scan.length > 0) {
      // temp.fix remove duplicated item if any
      // const _productScan = this.utils.removeDuplicateByKey(data.product_scan, 'id');

      // sort: data with conversion should come first
      const _sortedProductScan = data.product_scan.sort((a, b) => {
        return Number(!a.conversion?.length) - Number(!b.conversion?.length);
      });

      _sortedProductScan.forEach((product) => this.ProductScanForm.push(this.createProductScanGroup(product), { emitEvent: false }));
    }

    // determine multilevel status - scan rule
    const isMultilevel = this.isMultiLevelTarget();
    const isAccumulationRule = data.rule_scan === EnumProgramMarketingScanRule.ACCUMULATION_PRODUCTS;

    // populate target/multilevel forms
    this.TargetScanQrForm.clear();
    this.MultiLevelTargetForm.clear();

    // if (isMultilevel) this.generateMultiLevelTargetForm(data.multilevel_target);
    // temp.fix different key be response.
    if (isMultilevel) this.generateMultiLevelTargetForm(data.multi_level_target);

    if (isAccumulationRule) {
      if (data.target_scan_qr) this.generateTargetScanQRForm(data.target_scan_qr);
      else {
        const targetScanData = <IFormMPR_ProductScanTerm__Product[]>[];

        data?.product_scan?.forEach((product) => {
          targetScanData.push({
            variant_id: product.variant_id,
            brand_id: product.brand_id ?? null,
            value: product.value ?? 0,
          });
        });

        this.generateTargetScanQRForm(targetScanData);
      }
    }

    // update validity and emit final value
    this.form.updateValueAndValidity();
    this.mprFormService.updateSectionForm(this.form, EnumProgramRetailerSection.PRODUCT_SCAN_TERM);

    if (this.isViewInitialized) this.setupChildComponents(data);
    this.isLoadingSubject.next(false);
  }

  setupChildComponents(data: IFormMPR__ProductScanTerm) {
    if (!this.inputProductScanRef) return;

    const { product_scan } = data;
    if (!product_scan || product_scan.length === 0) {
      this.inputProductScanRef.setSelectedChips([]);
      return;
    }

    // map product scan to selected chips
    let selectedChipsData = product_scan.map(
      (item) =>
        ({
          id: item.variant_id ?? item.brand_id,
          name: item.variant_name ?? item.brand_name,
          selected: true,
          disabled: this.isDisableEdit(),
        } as IChips)
    );

    selectedChipsData = [...new Map(selectedChipsData.map((chips) => [chips.id, chips])).values()]; // remove duplicated item
    this.setupProductScanChips(selectedChipsData);

    const productScanHasBrand = data.product_scan?.some((item) => item.brand_id);
    this.selectedProductScanData = this.ProductScanForm?.value;
    this.inputProductScanRef.getListData(undefined, productScanHasBrand);

    if (this.isDisableEdit()) this.scheduledProductScan = selectedChipsData;
    this.updateConversionData();
  }

  setupProductScanChips(selectedChips: IChips[]) {
    this.inputProductScanRef.setSelectedChips(selectedChips);
  }

  createProductScanGroup(productData: IProgramProductScan) {
    // map conversion if any
    const _conversionArray = this.fb.array<FormGroup>([]);

    if (productData.conversion && productData.conversion.length > 0) {
      productData.conversion.forEach((conversion) => {
        const { variant_id, variant_name, qty_origin, qty_conversion } = conversion;
        const conversionGroup = this.fb.group({
          variant_id: [{ id: variant_id, name: variant_name }],
          qty_origin: [qty_origin ? this.utils.reverseFormatInternationalNumber(qty_origin) : null],
          qty_conversion: [qty_conversion ? this.utils.reverseFormatInternationalNumber(qty_conversion) : null],
        });
        _conversionArray.push(conversionGroup);
      });
    }

    const isVariant = !!productData.variant_id;
    return this.fb.group({
      id: [isVariant ? productData.variant_id : productData.brand_id],
      name: [isVariant ? productData.variant_name : productData.brand_name],
      delivery_unit: [productData.delivery_unit],
      is_variant: [isVariant],
      conversion: _conversionArray,
    });
  }

  generateTargetScanQRForm(initialData?: IFormMPR_ProductScanTerm__Product[] | null) {
    this.TargetScanQrForm.clear();
    if (!this.ProductScanForm?.controls.length) return;

    this.ProductScanForm.controls.forEach((ctrl) => {
      const { id, name, delivery_unit, is_variant } = ctrl.value;

      const targetItem = initialData?.find((item) => (is_variant && item.variant_id === id) || (!is_variant && item.brand_id === id));
      const initialValue = targetItem?.value ? this.utils.reverseFormatInternationalNumber(targetItem.value) : null;

      const targetFormGroup = this.fb.group({
        name: [name],
        variant_id: [is_variant ? id : null],
        brand_id: [!is_variant ? id : null],
        delivery_unit: [delivery_unit],
        value: [initialValue],
      });

      this.TargetScanQrForm.push(targetFormGroup, { emitEvent: false });
    });

    this.handleTargetScanQrValidator();
  }

  generateMultiLevelTargetForm(initialData?: { enum_level: string; products: IFormMPR_ProductScanTerm__Product[]; value: number }[] | null) {
    this.MultiLevelTargetForm.clear();
    if (!this.ProductScanForm?.controls.length) return;

    const numOfLevel = this.getNumberOfTargetLevel();
    if (isNaN(numOfLevel) || numOfLevel <= 0) return;

    for (let i = 0; i < numOfLevel; i++) {
      const levelEnum = `LEVEL_${i + 1}`;
      const levelData = initialData?.find((level) => {
        return level.enum_level?.trim() === levelEnum.trim();
      });

      const productGroupArray: FormArray = this.fb.array([]);
      this.ProductScanForm.controls.forEach((ctrl) => {
        const { id, name, delivery_unit, is_variant } = ctrl.value;

        // find product within the level
        const targetProduct = levelData?.products?.find((p) => (is_variant && p.variant_id === id) || (!is_variant && p.brand_id === id));
        const initialProductValue = targetProduct?.value ? this.utils.reverseFormatInternationalNumber(targetProduct.value) : null;

        const productGroup = this.fb.group({
          name: [name],
          variant_id: [is_variant ? id : null],
          brand_id: [!is_variant ? id : null],
          delivery_unit: [delivery_unit],
          value: [initialProductValue],
        });

        productGroupArray.push(productGroup, { emitEvent: false });
      });

      const initialLevelValue = levelData?.value ? this.utils.reverseFormatInternationalNumber(levelData.value) : null;
      this.MultiLevelTargetForm.push(
        this.fb.group({
          enum_level: [levelEnum],
          value: [initialLevelValue],
          products: productGroupArray,
        }),
        { emitEvent: false }
      );
    }

    this.MultiLevelTargetForm.setValidators([this.multilevelTargetValidator()]);
    this.MultiLevelTargetForm.updateValueAndValidity();
  }

  getOptionsRadio = () => (this.optionsRadioRuleScan = this.mprFormService.optionsRadioScanRule);

  getSingleTargetProgramUnit = () => (!this.ProductScanForm?.value.length ? '-' : this.ProductScanForm.value[0]?.delivery_unit);

  getNumberOfTargetLevel = () => {
    const _enumLevel = this.MultiLevelEnum?.value;
    const _resValue = typeof _enumLevel === 'string' ? _enumLevel : typeof _enumLevel === 'object' && _enumLevel !== null ? _enumLevel.value : null;
    return _resValue ? _resValue.split('_').pop() : 0;
  };

  isScanQrRule = (rule: EnumProgramMarketingScanRule) => this.RuleScan?.value === rule;

  isTargetScanQRQty = () => !!(this.TypeTargetScan?.value && this.TypeTargetScan.value === EnumProgramRetailerTargetScanType.QTY_QR);

  isMultiLevelTarget = () => this.RuleTargetScan?.value.is_multilevel_target === 'true';

  isHasConversion = () => this.isTargetScanQRQty() && this.ProductScanForm?.value.length > 0;

  isDisableEdit = () => this.mprFormService.DisableEditInScheduled;

  isAddedNewProductScan() {
    const productFormLength = this.isMultiLevelTarget() ? this.getMultiLevelTargetProducts(0)?.value?.length : this.ProductScanForm?.value.length;
    return this.scheduledProductScan?.length >= productFormLength;
  }

  updateConversionData() {
    const _productScanValue = this.ProductScanForm.getRawValue();
    this.dataProductScan = _productScanValue
      .filter((item: { is_variant: boolean }) => item.is_variant)
      .map((product: any) => {
        let { id, name, delivery_unit, conversion } = product;

        if (this.data) {
          const { product_scan } = this.data;
          const itemWithConversion = product_scan?.find((p) => p.variant_id === id);
          if (itemWithConversion) conversion = itemWithConversion.conversion;
        }

        return {
          variant_id: id,
          variant_name: name,
          delivery_unit,
          conversion,
        };
      });
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  multilevelTargetValidator() {
    return (control: AbstractControl): ValidationErrors | null => {
      const formArray = control as FormArray;
      if (!formArray || !formArray.controls || formArray.length === 0) return null;

      let previousValue: number | null = null;

      for (let i = 0; i < formArray.length; i++) {
        const groupControl = formArray.at(i) as FormGroup;
        const valueControl = groupControl.get('value');

        const currentValueRaw = valueControl?.value;
        const currentValue = currentValueRaw !== null ? this.utils.formatInternationalNumber(currentValueRaw) : null;

        // clear old errors
        valueControl?.setErrors(null);

        if (currentValue !== null) {
          if (currentValue < 1) valueControl?.setErrors({ min: true });
          if (previousValue !== null && currentValue <= previousValue) {
            valueControl?.setErrors({ notHigherThanPrevious: true });
          }
          previousValue = currentValue;
        }
      }

      return null;
    };
  }

  mapFormToPayload() {
    let _payload: IFormMPR__ProductScanTerm;

    const _targetProgram = this.form.controls.target_program?.value;
    const _targetScanQr = this.mapTargetScanQRFormPayload();
    const _multilevelTarget = this.isMultiLevelTarget() ? this.mapMultilevelTargetFormPayload() : [];
    const _productScan = this.mapProductScanFormPayload();

    _payload = {
      rule_scan: this.RuleScan?.value,
      target_program: _targetProgram ? this.utils.formatInternationalNumber(_targetProgram) : null,
      target_scan_qr: _targetScanQr,
      product_scan: _productScan,
      multilevel_target: _multilevelTarget,
    };

    return _payload;
  }

  mapTargetScanQRFormPayload() {
    if (this.isMultiLevelTarget() || (this.isMultiLevelTarget() && this.isScanQrRule(EnumProgramMarketingScanRule.ACCUMULATION_PRODUCTS))) {
      return [];
    }
    return (
      this.TargetScanQrForm?.value.map(({ brand_id, variant_id, value }: any) => ({
        variant_id: variant_id ?? null,
        brand_id: brand_id ?? null,
        value: value ? this.utils.formatInternationalNumber(value) : null,
      })) || []
    );
  }

  mapMultilevelTargetFormPayload() {
    return (
      this.MultiLevelTargetForm?.value?.map((item: any) => ({
        enum_level: item.enum_level,
        value: item.value ? this.utils.formatInternationalNumber(item.value) : null,
        products: item.products.map(({ brand_id, variant_id, value }: any) => ({
          brand_id: brand_id ?? null,
          variant_id: variant_id ?? null,
          value: value ? this.utils.formatInternationalNumber(value) : null,
        })),
      })) || []
    );
  }

  mapProductScanFormPayload() {
    const mapConversionPayload = (data: any) => {
      if (!data || !data.length) return [];
      return data.map((d: { variant_id: any; qty_origin: any; qty_conversion: any }) => {
        const { variant_id, qty_origin, qty_conversion } = d;
        return {
          variant_id: variant_id.id,
          qty_origin: typeof qty_origin === 'number' ? qty_origin : this.utils.formatInternationalNumber(qty_origin),
          qty_conversion: typeof qty_conversion === 'number' ? qty_conversion : this.utils.formatInternationalNumber(qty_conversion),
        };
      });
    };

    return (
      this.ProductScanForm?.value.map((item: any) => {
        return {
          variant_id: item.is_variant ? item.id : null,
          brand_id: !item.is_variant ? item.id : null,
          conversion: mapConversionPayload(item.conversion),
        };
      }) || []
    );
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
  protected readonly EnumProgramRetailerTargetScanType = EnumProgramRetailerTargetScanType;
  protected readonly Array = Array;
  protected readonly EnumProgramMarketingScanRule = EnumProgramMarketingScanRule;
}
