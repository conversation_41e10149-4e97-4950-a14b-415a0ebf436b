<app-card [header]="true" [cardClasses]="'card-note card-note--danger mb-8 border border-gray-700'" [borderHeader]="true" [cardHeaderClasses]="'border-gray-700'">
  <ng-container cardHeader>
    <div class="card-title">
      <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_DANGER" class="svg-icon svg-icon-1 me-4"></span>
      <span class="fw-bolder">Catatan Perbaikan</span>
    </div>
  </ng-container>

  <ng-container cardBody>
    <ng-container *ngIf="detailProgram$ | async as detailProgram">
      <!-- information -->
      <div class="row" *ngIf="detailProgram.information && detailProgram.information.revision_note">
        <div class="col-12 col-md-2 mb-2">
          <span class="text-gray-700">Informasi Program</span>
        </div>
        <div class="col-12 col-md-auto mb-4">
          <span>: {{ detailProgram.information.revision_note }}</span>
        </div>
      </div>

      <!-- program term -->
      <div class="row" *ngIf="detailProgram.program_term && detailProgram.program_term.revision_note">
        <div class="col-12 col-md-2 mb-2">
          <span class="text-gray-700">Ketentuan Program</span>
        </div>
        <div class="col-12 col-md-auto mb-4">
          <span>: {{ detailProgram.program_term.revision_note }}</span>
        </div>
      </div>

      <!-- order term -->
      <div class="row" *ngIf="detailProgram.order_term && detailProgram.order_term.revision_note">
        <div class="col-12 col-md-2 mb-2">
          <span class="text-gray-700">Ketentuan Pembelian</span>
        </div>
        <div class="col-12 col-md-auto mb-4">
          <span>: {{ detailProgram.order_term.revision_note }}</span>
        </div>
      </div>

      <!-- discount term -->
      <div class="row" *ngIf="detailProgram.discount_term && detailProgram.discount_term.revision_note">
        <div class="col-12 col-md-2 mb-2">
          <span class="text-gray-700">Ketentuan Diskon</span>
        </div>
        <div class="col-12 col-md-auto mb-4">
          <span>: {{ detailProgram.discount_term.revision_note }}</span>
        </div>
      </div>

      <!-- reward -->
      <div class="row" *ngIf="detailProgram.reward && detailProgram.reward.revision_note">
        <div class="col-12 col-md-2 mb-2">
          <span class="text-gray-700">Ketentuan Hadiah</span>
        </div>
        <div class="col-12 col-md-auto mb-4">
          <span>: {{ detailProgram.reward.revision_note }}</span>
        </div>
      </div>

      <!-- compensation  -->
      <div class="row" *ngIf="detailProgram.compensation && detailProgram.compensation.revision_note">
        <div class="col-12 col-md-2 mb-2">
          <span class="text-gray-700">Informasi Kompensasi</span>
        </div>
        <div class="col-12 col-md-auto mb-4">
          <span>: {{ detailProgram.compensation.revision_note }}</span>
        </div>
      </div>
    </ng-container>

    <!-- section: note CTA -->
    <!--    <pre>can edit? {{ hasPrivilegeEdit() }}</pre>-->
    <div class="mt-2" *ngIf="hasPrivilegeEdit()">
      <button mat-raised-button color="primary" class="btn btn-primary" type="button" (click)="goToEdit()">
        <span>Edit Program Marketing</span>
      </button>
    </div>
  </ng-container>
</app-card>
