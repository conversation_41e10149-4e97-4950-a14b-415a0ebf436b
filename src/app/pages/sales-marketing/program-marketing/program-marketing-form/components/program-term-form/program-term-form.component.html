<h3 class="mb-10 fw-bold">Ketentuan Program</h3>
<!--<pre>{{ form.value | json }}</pre>-->
<!--<pre>{{ QuotaScopeListForm.value.length | json }}</pre>-->
<!--<pre>{{ AreaForm.value.le | json }}</pre>-->
<ng-container *ngIf="(isLoadingSubject | async) === false; else loadingState">
  <app-note-view-revision *ngIf="!!data?.revision_note" [note]="data?.revision_note ?? ''" [title]="'Catatan Perbaikan'" />
  <div [formGroup]="form" class="form-group">
    <div class="w-100 align-items-center mb-4">
      <!--Cakupan Distributor-->
      <div class="d-flex flex-wrap my-4">
        <label [class.required]="true" class="col-form-label col-12 col-lg-4">Cakupan Program</label>
        <div class="col-12 col-lg-8">
          <div class="form-check text-capitalize p-0 ms-n5">
            <mat-radio-group [formControlName]="'scope_enum'" class="w-100 row" ngDefaultControl>
              <ng-container *ngFor="let option of optionRadioScope">
                <mat-radio-button [disabled]="!isDisableForm" [value]="option.value" class="col-12 col-lg-6 mb-4">{{ option.display }} </mat-radio-button>
              </ng-container>
            </mat-radio-group>
          </div>
        </div>
      </div>

      <div *ngIf="OptionScope.value === EnumProgramMarketingScope.AREA && AreaForm.controls.length > 1" class="d-flex flex-wrap my-4">
        <label [class.required]="true" class="col-form-label col-12 col-lg-4"> Area </label>
        <div class="col-12 col-lg-8 d-flex flex-wrap">
          <div *ngFor="let option of AreaForm.controls; index as i; let last = last" [formArrayName]="'area_ids'" class="col-6">
            <div [formGroupName]="i" class="form-check my-6 text-capitalize">
              <fieldset [disabled]="!isDisableForm">
                <input
                  (click)="onAreaChange(option.value)"
                  [formControlName]="'checked'"
                  [value]="option.get('checked')?.value"
                  class="form-check-input cursor-pointer"
                  id="flexCheckDefault-{{ option.get('id')?.value }}"
                  type="checkbox"
                />
                <label class="form-check-label cursor-pointer ms-2 text-gray-900 text-capitalize" for="flexCheckDefault-{{ option.get('id')?.value }}">
                  {{ option.get('name')?.value | lowercase }}
                </label>
              </fieldset>
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="OptionScope.value === EnumProgramMarketingScope.DISTRIBUTOR" class="d-flex flex-wrap my-4">
        <div class="col-12 col-lg-12">
          <fieldset [disabled]="!isDisableForm">
            <app-input-list-box-with-area
              (selectionOutput)="distributorSelection($event)"
              [UrlEndpoint]="API.PROGRAM_MARKETING.GET_DISTRIBUTOR_AREA"
              [chipsListData]="valueDistributor"
              [disabled]="!isDisableForm"
              [required]="true"
              [showChipsModal]="true"
              formControlName="distributor_ids"
              label="Distributor"
              ngDefaultControl
            >
            </app-input-list-box-with-area>
          </fieldset>
        </div>
      </div>

      <div *ngIf="OptionScope.value === EnumProgramMarketingScope.SUB_AREA" class="d-flex flex-wrap my-4">
        <div class="col-12 col-lg-12">
          <fieldset [disabled]="!isDisableForm">
            <app-input-list-box-with-area
              (selectionOutput)="subAreaSelection($event)"
              [UrlEndpoint]="API.PROGRAM_MARKETING.GET_LIST_SUB_AREA"
              [chipsListData]="valueSubArea"
              [disabled]="!isDisableForm"
              [required]="true"
              [showChipsModal]="true"
              formControlName="sub_area_ids"
              label="Sub Area"
              ngDefaultControl
            >
            </app-input-list-box-with-area>
          </fieldset>
        </div>
      </div>

      <div [formGroupName]="'quota'">
        <fieldset class="d-flex flex-wrap my-4">
          <label [class.required]="true" class="col-form-label col-12 col-lg-4">Tipe Kuota</label>
          <div class="col-12 col-lg-8">
            <div class="form-check text-capitalize p-0 ms-n5">
              <mat-radio-group [formControlName]="'enum_type'" class="w-100 d-flex flex-column" ngDefaultControl>
                <ng-container *ngFor="let option of optionRadioQuotaType">
                  <mat-radio-button [disabled]="disableEnumType()" [value]="option.value" class="mb-4">
                    <div class="d-flex flex-column">
                      {{ option.display }}
                      <span class="text-gray-700 mt-1">{{ option.description }}</span>
                    </div>
                  </mat-radio-button>
                </ng-container>
              </mat-radio-group>
            </div>
          </div>
        </fieldset>

        <div *ngIf="QuotaTypeForm.value === EnumProgramMarketingQuotaType.ALL" class="d-flex flex-wrap my-4">
          <label [class.required]="true" class="col-form-label col-12 col-lg-4">Kuota</label>
          <div class="col-12 col-lg-8">
            <fieldset
              [class.border-danger]="(QuotaForm.get('max_qty')?.dirty && form.get('max_qty')?.invalid) || form.get('max_qty')?.hasError('max_qty')"
              class="input-group form-control form-control-solid p-0 w-100"
            >
              <input
                [formControlName]="'max_qty'"
                [min]="0"
                [required]="true"
                appNumberInput
                class="form-control form-control-solid"
                placeholder="Silakan input jumlah kuota penggunaan"
                type="text"
              />
              <span class="input-group-text fs-12">PO</span>
            </fieldset>
          </div>
        </div>

        <div *ngIf="QuotaTypeForm.value === EnumProgramMarketingQuotaType.PER_SCOPE">
          <div *ngFor="let item of QuotaScopeListForm.controls; index as i; let last = last" [formArrayName]="'scope_list'" class="d-flex flex-wrap my-7">
            <label [class.required]="true" class="col-form-label col-12 col-lg-4">Kuota {{ item.value.name }}</label>
            <div [formGroupName]="i" class="col-12 col-lg-8">
              <fieldset
                [class.border-danger]="(QuotaForm.get('max_qty')?.dirty && form.get('max_qty')?.invalid) || form.get('max_qty')?.hasError('max_qty')"
                class="input-group form-control form-control-solid p-0 w-100"
              >
                <input
                  [formControlName]="'max_qty'"
                  [min]="0"
                  [required]="true"
                  appNumberInput
                  class="form-control form-control-solid"
                  placeholder="Silakan input jumlah kuota penggunaan"
                  type="text"
                />
                <span class="input-group-text fs-12">PO</span>
              </fieldset>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-container>

<ng-template #loadingState>
  <div class="d-flex flex-column justify-content-center align-items-center my-5">
    <mat-spinner></mat-spinner>
    <div class="my-4">Loading Form</div>
  </div>
</ng-template>
