<form (ngSubmit)="handleSubmit()" [formGroup]="fbMasterDataForm">
  <ng-container>
    <div class="mb-10 pt-10 border-top border-gray-300">
      <div class="d-flex flex-wrap w-100 align-items-center mb-4">
        <label [class.required]="true" class="col-form-label col-12 col-lg-4 label-name">
          Nama
        </label>
        <div class="col-12 col-lg-8">
          <div class="input-group input-group-solid">
            <input (input)="onCheck($event)"
                   [class.border-danger]="
                                (getName.dirty && getName.invalid) ||
                                getName.hasError('hasName')
                              "
                   [required]="true"
                   class="form-control form-control-solid"
                   formControlName="name"
                   ngDefaultControl
                   placeholder="Silahkan input nama data"
            />
          </div>
          <ng-container *ngIf="getName.dirty && getName.invalid">
            <mat-error>
              <div class="px-2 invalid-feedback-input-name">
                <ng-container *ngIf="getName.errors?.required">Nama harus diisi</ng-container>
                {{ getName.errors?.hasName }}
              </div>
            </mat-error>
          </ng-container>
        </div>
      </div>
      <app-input-select2
              (handleChangeData)="handleChangeDataType($event)"
              [disableSelect]="mode === 'edit'"
              [options]="listDataTypeInput.value"
              [required]="true"
              [value]="getDataType.value"
              formControlName="data_type"
              label="Tipe Data"
              ngDefaultControl
              placeholder="Pilih salah satu"
      ></app-input-select2>
    </div>
  </ng-container>

  <div class="d-flex align-items center justify-content-between mt-6">
    <button (click)="handleCancel.emit(true)" class="btn btn-outline btn-outline-secondary min-w-150px" type="button">
      Cancel
    </button>
    <button [disabled]="validateForm()" class="btn btn-primary min-w-150px" type="submit">
      Submit
    </button>
  </div>
</form>

<ng-container *ngIf="fbMasterDataForm.valid">
  <app-modal #modalConfirmCreate [modalConfig]="modalConfirmCreateConfig">
    <div class="d-flex justify-content-center align-items-center text-center mt-8">
      <p *ngIf="getName.value" class="mb-0">Apakah anda yakin akan menambah master data {{ dataType }}<br/>
        <span class="fw-bold">{{ getName.value }}?</span><br/>
        Pastikan data yang anda input sudah benar.
      </p>
    </div>
  </app-modal>
</ng-container>

<ng-container *ngIf="fbMasterDataForm.valid">
  <app-modal #modalConfirmUpdate [modalConfig]="modalConfirmUpdateConfig">
    <div class="d-flex flex-column mt-8">
      <p *ngIf="getName.value">Apakah anda yakin dengan perubahan:</p>
      <div class="w-100 d-flex justify-content-between fw-bold gap-5">
        <div class="w-50">
          {{ getOldNameValue() }}
        </div>
        <div>
          <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_ARROW_LINE_RIGHT"></span>
        </div>
        <div class="w-50">
          {{ getName.value }}
        </div>
      </div>
    </div>
  </app-modal>
</ng-container>
