import { AfterViewInit, ChangeDetectorRef, Component, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { PageInfoService, PageLink } from '@metronic/layout';
import { UtilitiesService } from '@services/utilities.service';
import { BaseService } from '@services/base-service.service';
import { BehaviorSubject, Subscription } from 'rxjs';
import { API } from '@config/constants/api.constant';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { TabListLabelPending } from '../enum/tablist-label.enum';
import { PendingRegistrationComponent } from './pending-registration/pending-registration.component';
import { PendingSubmissionComponent } from './pending-submission/pending-submission.component';
import { MatTabChangeEvent } from '@angular/material/tabs';

@Component({
  selector: 'app-pending',
  templateUrl: './pending.component.html',
  styleUrls: ['./pending.component.scss'],
})
export class PendingComponent implements OnInit, AfterViewInit, OnDestroy {
  links: Array<PageLink> = [
    {
      title: 'Retailer',
      path: '',
      isActive: false,
    },
    {
      title: '',
      path: '',
      isActive: false,
      isSeparator: true,
    },
    {
      title: 'Sedang Menunggu',
      path: '',
      isActive: true,
    },
  ];

  countRegister = new BehaviorSubject(0);
  countSubmission = new BehaviorSubject(0);

  tabIndex: number = 0;
  tab: any | null = null;

  @ViewChild('pendingRegisterTpl', { static: true }) pendingRegisterTpl!: TemplateRef<any>;
  @ViewChild('pendingSubmissionTpl', { static: true }) pendingSubmissionTpl!: TemplateRef<any>;
  TabListLabel = TabListLabelPending;
  tabList: any[] = [];

  private unsubscribe: Subscription[] = [];

  constructor(
    private pageInfoService: PageInfoService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private baseService: BaseService,
    public utilities: UtilitiesService,
    private ref: ChangeDetectorRef,
    private rolePrivilegeService: RolePrivilegeService
  ) {}

  ngOnInit(): void {
    this.initTabList();
    this.initPageInfo();
    this.handlePrivilege();
  }

  ngAfterViewInit() {
    this.initGetListDataCount();
  }

  initTabList() {
    this.tabList = [
      {
        tab: 'register',
        enumChild: 'TAB_LIST_REGISTER',
        labelKey: 'register',
        privilege: null,
        dataCount: this.countRegister,
        component: PendingRegistrationComponent,
        template: this.pendingRegisterTpl,
      },
      {
        tab: 'submission',
        enumChild: 'TAB_LIST_SUBMISSION',
        labelKey: 'submission',
        privilege: null,
        dataCount: this.countSubmission,
        component: PendingSubmissionComponent,
        template: this.pendingSubmissionTpl,
      },
    ];

    this.activatedRoute.paramMap.subscribe((params) => {
      this.tab = params.get('tab');
      const _currentActiveTab = this.tabList.filter((item) => item.tab === this.tab);
      this.tabIndex = this.tabList.indexOf(_currentActiveTab[0]);
    });
  }

  handlePrivilege() {
    this.tabList.map((tab) => {
      tab.privilege = this.rolePrivilegeService.checkPrivilege('RETAILER', 'LIST_RETAILER_MENUNGGU', tab.enumChild);
    });

    this.changeFirstTab();
  }

  changeFirstTab() {
    const _data: { tab: string; privilege: boolean }[] = [];
    this.tabList.forEach((item) => {
      _data.push({ tab: item.tab, privilege: !!item.privilege });
    });

    if (!this.tabList[0].privilege) {
      const _changeTab = this.rolePrivilegeService.changePrivilegeUrlTab(_data, 'retailer', 'pending');
      this.router.navigate([_changeTab]).then();
    }
  }

  initPageInfo() {
    this.pageInfoService.updateTitle(`Retailer Pending`);
    this.pageInfoService.updateBreadcrumbs(this.links);
  }

  tabClick(e: MatTabChangeEvent) {
    this.tabIndex = e.index;

    const _tabLabel = e.tab.textLabel;
    const _indexOfTab = Object.values(TabListLabelPending).indexOf(_tabLabel as TabListLabelPending);

    const { tab } = this.tabList[_indexOfTab];

    this.router.navigate([`/retailer/pending/${tab}`]).then(() => this.initPageInfo());
  }

  initGetListDataCount() {
    const _dataCounterSubs = this.baseService.getData<any>(API.RETAILER.LIST_COUNTER.PENDING).subscribe((resp) => {
      if (!resp) return;
      const { count_submission_registration, count_submission_update_data } = resp && resp.data;
      this.countRegister.next(count_submission_registration);
      this.countSubmission.next(count_submission_update_data ?? 0);
    });

    this.unsubscribe.push(_dataCounterSubs);
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }
}
