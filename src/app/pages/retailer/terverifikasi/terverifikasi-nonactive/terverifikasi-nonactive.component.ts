import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit, Output, ViewChild } from '@angular/core';
import { MatSort } from '@angular/material/sort';
import { BaseTableService } from '@shared/base/base-table-service.service';
import { IListRetailerNonActive } from '@models/retailer.model';
import { TableColumn } from '@shared/interface/table.interface';
import { BaseDatasource } from '@shared/base/base.datasource';
import { BehaviorSubject, Subscription } from 'rxjs';
import { FormGroup } from '@angular/forms';
import { IDataRetailerList } from '../../retailers.interface';
import { PageInfoService, PageLink } from '@metronic/layout';
import { UtilitiesService } from '@services/utilities.service';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { FilterService } from '@services/filter.service';
import { RetailerService } from '../../retailer.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { API } from '@config/constants/api.constant';

@Component({
  selector: 'app-terverifikasi-nonactive',
  templateUrl: './terverifikasi-nonactive.component.html',
  styleUrl: './terverifikasi-nonactive.component.scss',
})
export class TerverifikasiNonactiveComponent implements OnInit, OnDestroy {
  @ViewChild(MatSort, { static: false }) matSort: MatSort;
  @Output() totalCount = new EventEmitter();

  STRING_CONSTANTS = STRING_CONSTANTS;
  iconNone = STRING_CONSTANTS.ICON.IC_RETAIL_NONE;
  isLoading = false;
  // table
  baseTable: BaseTableService<IListRetailerNonActive> = this.base;
  tableColumns: TableColumn[];
  displayedColumns: string[];
  baseDatasourcePending: BaseDatasource<IListRetailerNonActive>;
  string_filter?: string;
  checkActive: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  isOpenFilter: boolean = false;
  isActiveFilter: boolean = false;
  filterForm: FormGroup;
  privilageDetailPending: boolean;

  dataList: IDataRetailerList;
  links: Array<PageLink> = [
    {
      title: 'Retailer',
      path: '',
      isActive: false,
    },
    {
      title: '',
      path: '',
      isActive: false,
      isSeparator: true,
    },
    {
      title: 'Terverifikasi',
      path: '',
      isActive: true,
    },
  ];

  public static totalCount = new BehaviorSubject(0);
  private unsubscribe: Subscription[] = [];

  constructor(
    private pageInfoService: PageInfoService,
    private base: BaseTableService<IListRetailerNonActive>,
    public utilities: UtilitiesService,
    private rolePrivilegeService: RolePrivilegeService,
    public filterService: FilterService,
    private retailerService: RetailerService
  ) {}

  ngOnInit() {
    this.pageInfoService.updateTitle(`Retailer Terverifikasi`);
    this.privilageDetailPending = this.rolePrivilegeService.checkPrivilege('RETAILER', 'LIST_RETAILER_MENUNGGU', 'TAB_LIST_REGISTER', 'CTA_VIEW_DETAIL');
    this.pageInfoService.updateBreadcrumbs(this.links);
    this.setTableData();
    this.dataList = {
      status: 'terverifikasi',
      tab: 'nonactive',
      view: 'nonactive',
    };
  }

  setTableData() {
    this.tableColumns = this.retailerService.getTerverifikasiNonactiveTableColumns();
    this.tableColumns = this.utilities.privilegeTableColumns(this.privilageDetailPending, this.tableColumns);
  }

  onTotalCount = (e: number) => TerverifikasiNonactiveComponent.totalCount.next(e);

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  protected readonly API = API;
}
