<ng-container  [ngTemplateOutlet]="isEmptyData() ? emptyState : dataTpl">
</ng-container>

<ng-template #dataTpl>
  <app-card [cardBodyClasses]="'pt-2'" [header]="true">
    <ng-container cardHeader>
      <div class="card-toolbar d-flex align-items-center my-auto w-100">
        <app-input-search
          (actionFilter)="handleSearch($event)"
          [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
          [placeholder]="'Cari nama program'"
          [value]="string_filter"
        ></app-input-search>
        <ng-template #loaderButton>
          <ngx-skeleton-loader [theme]="{ 'border-radius': '5', height: '40px', width: '100px' }" count="1"></ngx-skeleton-loader>
        </ng-template>
      </div>
    </ng-container>

    <ng-container cardBody>
      <ng-container *ngIf="searchNotFound(); else tableListTpl">
        <app-card-empty [icon]="STRING_CONSTANTS.ILLUSTRATIONS.IL_PROMO"
                        text="Program Marketing tidak ditemukan."></app-card-empty>
      </ng-container>
      <ng-template #tableListTpl>
        <div class="table-responsive">
          <table (matSortChange)="sortTable($event)" [dataSource]="baseDatasource" class="table w-100 gy-5 table-row-bordered align-middle" mat-table matSort>
            <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
              <ng-container *ngIf="tableColumn.isSortable; else notSortable">
                <th
                  *matHeaderCellDef
                  [class.min-w-200px]="tableColumn.key !== 'actions' && tableColumn.key !== 'id'"
                  [mat-sort-header]="tableColumn.key"
                  [ngClass]="{
                  'min-w-70px text-center': tableColumn.key === 'actions'
                }"
                  class="min-w-125px px-3"
                  mat-header-cell
                >
                  <app-table-content [width]="70" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    {{ tableColumn.title }}
                  </app-table-content>
                </th>
              </ng-container>
              <ng-template #notSortable>
                <th
                  *matHeaderCellDef
                  [class.min-w-200px]="tableColumn.key !== 'actions' && tableColumn.key !== 'id'"
                  [ngClass]="{
                  'min-w-70px text-center': tableColumn.key === 'actions'
                }"
                  class="min-w-125px px-3"
                  mat-header-cell
                >
                  <app-table-content [width]="70" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    {{ tableColumn.title }}
                  </app-table-content>
                </th>
              </ng-template>

              <!-- COLUMN DATA -->
              <td *matCellDef="let element" class="px-3">
                <ng-container [ngSwitch]="tableColumn.key">
                  <div *ngSwitchCase="'program_name'">
                    <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                      <ng-container *ngIf="element">
                        <div>
                          {{ displayProgramName(element, false) }}
                        </div>
                        <div class="text-gray-700">
                          {{ displayProgramName(element, true) }}
                        </div>
                      </ng-container>
                    </app-table-content>
                  </div>

                  <div *ngSwitchCase="'qr_scan_quantity'">
                    <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                      <ng-container *ngIf="element">
                        <div>
                          {{ displayQRScan(element, false) }} QR
                        </div>
                        <div class="text-gray-700">
                          {{ displayQRScan(element, true) }}
                        </div>
                      </ng-container>
                    </app-table-content>
                  </div>

                  <div *ngSwitchCase="'string_level'">
                    <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                      <div>{{element['is_multilevel_target'] === true ?  element['string_level'] ?? '' : '-' }}</div>
                    </app-table-content>
                  </div>

                  <div *ngSwitchCase="'progress.completion'">
                    <app-table-content [count]="2" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
                                       [type]="'text'">
                      <ng-container *ngIf="element">
                        <div>
                          {{ displayProgress(element, true) }}
                        </div>
                        <div class="text-gray-700">
                          {{ displayProgress(element, false) }}
                        </div>
                      </ng-container>

                    </app-table-content>
                  </div>

                  <div *ngSwitchCase="'status_string'">
                    <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                      <span class="badge badge__status {{ 'badge__status--' + element['status_enum'] }}">
                        {{ element['status_string'] }}
                      </span>
                    </app-table-content>
                  </div>

                  <div *ngSwitchCase="'actions'" class="text-center">
                    <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'icon'">
                      <span (click)="handleAction(element['id'], element['is_multilevel_target'])" [inlineSVG]="'./assets/media/icons/ic_task.svg'" class="svg-icon svg-icon-2 ms-2 cursor-pointer"></span>
                    </app-table-content>
                  </div>

                  <div *ngSwitchDefault>
                    <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                      <span *ngIf="finishLoading$ | async">{{ element[tableColumn.key] }}</span>
                    </app-table-content>
                  </div>
                </ng-container>
              </td>
            </ng-container>
            <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
            <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
          </table>
        </div>
        <div class="d-flex justify-content-between py-4">
          <app-mai-material-bottom-table
            (changePage)="changePageEvent($event)"
            [baseDataTableComponent]="baseDatasource"
            [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
            class="w-100"
          ></app-mai-material-bottom-table>
        </div>
      </ng-template>

      <app-modal-progress-scan-retailer [isRetailer]="true" [detailMpr]="getDetailModalScan()" (download)="handleDownloadProgressScan()" [downloaded]="downloaded" [isMultilevel]="is_multilevel" #modalDetail></app-modal-progress-scan-retailer >
    </ng-container>
  </app-card>
</ng-template>

<ng-template #emptyState>
  <app-card-empty [icon]="STRING_CONSTANTS.ILLUSTRATIONS.IL_PROMO" text="Belum terdapat Program Marketing yang diikuti Retailer."></app-card-empty>
</ng-template>
