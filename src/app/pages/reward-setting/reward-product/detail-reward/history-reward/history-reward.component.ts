import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChild } from '@angular/core';
import { TableColumn } from '@shared/interface/table.interface';
import { PageInfoService, PageLink } from '@metronic/layout';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { IListHistoryRewardSetting } from '../../../reward-setting.interface';
import { BaseDatasource } from '@shared/base/_base.datasource';
import { BehaviorSubject, Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { UrlParamService } from '@services/urlParam.service';
import { BaseTableService } from '@shared/base/base-table-service.service';
import { UtilitiesService } from '@services/utilities.service';
import { FilterService } from '@services/filter.service';
import { API } from '@config/constants/api.constant';
import { RewardSettingStatus } from '@config/enum/reward-setting.enum';
import { IModalDetailWithDocument } from '@shared/components/modal/modal.interface';
import { IKeyValue } from '../../../../distributor/interfaces/distributor.interface';
import { ModalDetailWithDocumentComponent } from '@shared/components/modal/modal-detail-with-document/modal-detail-with-document.component';
import { Sort } from '@angular/material/sort';

@Component({
  selector: 'app-history-reward',
  templateUrl: './history-reward.component.html',
  styleUrls: ['./history-reward.component.scss'],
})
export class HistoryRewardComponent implements OnInit, OnDestroy {
  @ViewChild('modalDetail') private modalDetail: ModalDetailWithDocumentComponent;

  tableColumns: TableColumn[];
  links: Array<PageLink>;
  displayedColumns: string[];
  string_filter: string = '';
  ASSET_ICON = STRING_CONSTANTS.ICON;
  ASSER_ILLUSTRATION = STRING_CONSTANTS.ILLUSTRATIONS;

  status: string = '';
  id: string = '';

  dataDetailModal: BehaviorSubject<IModalDetailWithDocument> = new BehaviorSubject<IModalDetailWithDocument>({} as IModalDetailWithDocument);
  productList: IListHistoryRewardSetting[];
  baseDatasource: BaseDatasource<IListHistoryRewardSetting>;

  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);

  private unsubscribe: Subscription[] = [];

  constructor(
    private activeRoute: ActivatedRoute,
    private urlParamService: UrlParamService,
    // private baseService: BaseService,
    private baseTableService: BaseTableService<any>,
    // private ref: ChangeDetectorRef,
    private router: Router,
    private pageInfoService: PageInfoService,
    public utilities: UtilitiesService,
    private filterService: FilterService
  ) {}

  ngOnInit(): void {
    this.baseTableService.responseDatabase.subscribe((response) => (this.baseDatasource = response));

    this.status = this.activeRoute.snapshot.params['status'];
    this.id = this.activeRoute.snapshot.params['id'];
    this.setTableData();
    this.queryHandler();
    this.initPageInfo();
  }

  setTableData() {
    this.isLoading.next(false);
    this.tableColumns = [
      { title: 'NAMA PRODUK', key: 'brand_name' },
      { title: 'JUMLAH KUPON RETAILER', key: 'qty_coupon' },
      { title: 'DIUBAH OLEH', key: 'modified_by' },
      { title: 'TANGGAL DIUBAH', key: 'modifiedDate', isSortable: true },
      { title: 'ACTION', key: 'actions' },
    ];

    this.displayedColumns = this.tableColumns.map((head) => head.key);

    if (this.productList) {
      this.baseDatasource.tableSubject.next(this.productList);
    }
  }

  queryHandler() {
    const paramSubs = this.activeRoute.queryParams.subscribe((data) => {
      this.string_filter = data.string_filter;
      const _queryParams = this.urlParamService.sliceQueryParams();
      this.loadDataTable(_queryParams ? _queryParams : '');
    });
    this.unsubscribe.push(paramSubs);
  }

  loadDataTable(params: string) {
    return this.baseTableService.loadDataTable(API.LIST_HISTORY_REWARD_SETTING + this.id, params);
  }

  initPageInfo() {
    this.links = [
      {
        title: 'Reward Setting',
        path: '',
        isActive: false,
      },
      {
        title: '',
        path: '',
        isActive: false,
        isSeparator: true,
      },
      {
        title: 'List Reward',
        path: 'reward-setting/product/list',
        isActive: false,
      },
      {
        title: '',
        path: '',
        isActive: false,
        isSeparator: true,
      },
      {
        title: 'Periode 2025',
        path: 'reward-setting/product/' + this.status + '/' + this.id,
        isActive: false,
      },
      {
        title: '',
        path: '',
        isActive: false,
        isSeparator: true,
      },
      {
        title: 'Histori Reward Setting',
        path: '',
        isActive: true,
      },
    ];
    this.pageInfoService.updateBreadcrumbs(this.links);
    this.pageInfoService.updateTitle('Detail Reward Produk');
  }

  openDetail(data: IListHistoryRewardSetting) {
    if (!data) return;
    this.setDetailProduct(data);
    return this.modalDetail.openModal();
  }

  setDetailProduct(data: IListHistoryRewardSetting) {
    const title: string = 'DETAIL KUPON PRODUK';
    const detail: IKeyValue[] = [
      { key: 'Nama Produk', value: data.brand_name },
      { key: 'Jumlah Kupon Retailer', value: data.qty_coupon },
      { key: 'Diubah Oleh', value: data.modified_by },
      {
        key: 'Tanggal Diubah',
        value: this.utilities.formatEpochToDate(data.modified_date, 'dd-MM-yyyy HH:mm:ss'),
      },
    ];
    const documents = [];
    if (data.document) {
      documents.push({ name: data.document.name, url: data.document.url });
    }
    this.dataDetailModal.next({ title, detail, documents });
  }

  onSearch(event: string) {
    this.filterService.onSearch(event);
    this.string_filter = '';
  }

  changePageEvent($event: any) {
    this.filterService.changePageEvent($event, this.string_filter ?? '');
  }

  async handleBack() {
    return this.router.navigate(['reward-setting/product/' + this.status + '/' + this.id]).then();
  }

  sortTable(param: Sort) {
    this.filterService.sortTable(param, this.tableColumns).then();
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  protected readonly RewardSettingStatus = RewardSettingStatus;
}
