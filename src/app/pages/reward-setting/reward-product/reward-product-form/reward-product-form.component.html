<form [formGroup]="rewardProductForm" (ngSubmit)="handleSubmit()">
  <app-card [cardBodyClasses]="'p-18'">
    <ng-container cardBody>
      <h2 class="form-title mb-7">Periode Reward</h2>
      <app-input-select-material
        #inputPeriodeList
        [label]="'Periode'"
        [required]="true"
        [optionsData]="listPeriodeSubject"
        [readOnly]="pageModeIsEdit() || pageModeIsAdd()"
        [useFilterList]="false"
        (handleChangeData)="handlePeriodeChange($event)"
        placeholder="Silahkan pilih periode reward"
        formControlName="id_periode"
        ngDefaultControl
      ></app-input-select-material>
    </ng-container>
  </app-card>

  <!-- LIST KUPON PRODUK -->
  <ng-container *ngIf="(tableSourceProduct | async)?.length">
    <app-card [cardBodyClasses]="'p-18'" [cardClasses]="'my-8'">
      <ng-container cardBody>
        <ng-container [ngTemplateOutlet]="infoKuponProduk"></ng-container>

        <h2 class="form-title mb-7 d-flex align-items-center">
          <span class="col">Kupon Produk</span>
          <div class="col-auto" *ngIf="!pageModeIsEdit() && !pageModeIsAddFromCatalogue()">
            <ng-container [ngTemplateOutlet]="ctaAddProductButton" [ngTemplateOutletContext]="{ btnOutline: true }"></ng-container>
          </div>
        </h2>

        <div class="table-responsive">
          <table mat-table class="table w-100 gy-5 table-row-bordered" [dataSource]="tableSourceProduct" [formArrayName]="'list_brand'">
            <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
              <th
                mat-header-cell
                *matHeaderCellDef
                class="px-3"
                [ngClass]="{
                  'min-w-70px': tableColumn.key === 'action',
                  'text-center': tableColumn.key === 'qty' || tableColumn.key === 'action',
                  'min-w-200px': tableColumn.key !== 'action',
                  'min-w-250px': tableColumn.key === 'qty' && !pageModeIsAdd(),
                  'w-100': tableColumn.key === 'brand_name' && !pageModeIsAdd()
                }"
              >
                {{ tableColumn.title }}
                <ng-container *ngIf="tableColumn.key === 'qty'">
                  <span
                    class="svg-icon icon-note-view ms-3 cursor-pointer"
                    inlineSVG="{{ STRING_CONSTANTS.ICON.IC_INFO }}"
                    ngbPopover="Jumlah scan QR produk untuk mendapatkan 1 Maxxi Poin"
                    popoverClass="tooltip-info"
                    placement="bottom"
                  ></span>
                </ng-container>
              </th>

              <td mat-cell *matCellDef="let element; let i = index" class="px-3 align-middle" [formGroupName]="i">
                <ng-container [ngSwitch]="tableColumn.key">
                  <ng-container *ngSwitchCase="'qty'">
                    <app-input-counter [formControlName]="'qty'" [fieldIndex]="i" [max]="MAX_NUMBER_COUNTER" [counterButton]="false" ngDefaultControl></app-input-counter>
                  </ng-container>

                  <div *ngSwitchCase="'action'" class="text-center">
                    <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_TRASH_RED" class="svg-icon svg-icon2 cursor-pointer" (click)="handleRemoveProduct(element, i)"></span>
                  </div>

                  <ng-container *ngSwitchDefault>
                    <span>{{ element[tableColumn.key] }}</span>
                  </ng-container>
                </ng-container>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
        </div>
      </ng-container>
    </app-card>
  </ng-container>

  <!-- EMPTY STATE: KUPON PRODUK -->
  <ng-template [ngTemplateOutlet]="emptyListTpl"></ng-template>

  <!-- DOCUMENT -->
  <app-card [cardBodyClasses]="'p-18'">
    <ng-container cardBody>
      <h2 class="form-title mb-7">Dokumen</h2>
      <div class="position-relative">
        <app-document-upload
          [type]="'FILE'"
          formControlName="document"
          (documentOutput)="handleDocument($event)"
          (loadingState)="handleUploadState($event)"
          [required]="true"
          [data]="documentForm.value"
          [isDisabled]="idPeriodeForm.invalid"
          ngDefaultControl
        ></app-document-upload>
      </div>
    </ng-container>
  </app-card>

  <div class="d-flex align-items center justify-content-between my-8">
    <button class="btn btn-outline btn-outline-secondary min-w-150px" type="button" (click)="modalConfirmCancel.open()">Batal</button>
    <button mat-raised-button color="primary" class="btn btn-primary min-w-150px" type="submit" [disabled]="rewardProductForm.invalid || isLoadingUpload">Simpan</button>
  </div>

  <app-input-listbox-product #listBoxProductComponent [periodeID]="idPeriodeForm.value.value" (selectionOutput)="handleSelectedProduct($event)"></app-input-listbox-product>
</form>

<ng-template #ctaAddProductButton let-btnOutline="btnOutline">
  <ng-container *ngIf="btnOutline; else defaultBtnTpl">
    <button mat-stroked-button type="button" color="primary" class="btn btn-outline" [disabled]="idPeriodeForm.invalid" (click)="openListBoxProduct()">
      <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_PLUS" class="svg-icon svg-icon-2"></span>
      <span class="text-primary" style="font-size: 18px">Tambah Produk</span>
    </button>
  </ng-container>
  <ng-template #defaultBtnTpl>
    <button mat-raised-button color="primary" class="btn btn-primary" type="button" [disabled]="idPeriodeForm.invalid" (click)="openListBoxProduct()">Tambah Produk</button>
  </ng-template>
</ng-template>

<ng-template #emptyListTpl>
  <div class="my-8" *ngIf="!tableSourceProduct.value.length">
    <app-card-empty [icon]="STRING_CONSTANTS.ILLUSTRATIONS.IL_BOX_NONE" [text]="'Silakan tambahkan produk untuk mengatur kupon produk'">
      <div class="mt-8">
        <ng-container [ngTemplateOutlet]="ctaAddProductButton"></ng-container>
      </div>
    </app-card-empty>
  </div>
</ng-template>

<ng-template #infoKuponProduk>
  <app-note-view [color]="'info'" [text]="handleNoteViewText()" [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION" [classNoteView]="'mb-10'"></app-note-view>
</ng-template>

<app-modal #modalConfirmCancel [modalConfig]="modalConfirmCancelConfig">
  <div class="text-center">
    <p class="mt-8 mb-4">Apakah anda yakin untuk keluar dari halaman setting reward produk? Data yang sudah diinput akan hilang.</p>
  </div>
</app-modal>

<app-modal #modalConfirmSubmit [modalConfig]="modalConfirmSubmitConfig" [modalOptions]="{ size: 'md' }">
  <ng-container [ngTemplateOutlet]="infoKuponProduk"></ng-container>
  <ng-container *ngIf="payloadCreateReward">
    <p>Reward {{ getPeriodeById(payloadCreateReward.periode_id) }} akan memiliki kupon untuk produk:</p>
    <div class="mh-200px overflow-auto">
      <ul class="ps-5">
        <li *ngFor="let item of payloadCreateReward.list_brand">
          <span>{{ getBrandNameById(item.brand_id) }}: {{ item.qty }} Kupon</span>
        </li>
      </ul>
    </div>
  </ng-container>
</app-modal>

<app-modal #modalResponseCreate [modalConfig]="modalResponseCreateConfig" [modalOptions]="{ size: 'md' }">
  <div class="d-flex flex-column justify-content-center align-items-center">
    <div class="mt-8" *ngIf="loadingPostData | async; else responseDone">
      <mat-spinner></mat-spinner>
    </div>

    <!--  response done  -->
    <ng-template #responseDone>
      <span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT"></span>
      <div class="mb-n8 mt-4 text-center" *ngIf="payloadCreateReward">
        <span>Reward produk untuk {{ getPeriodeById(payloadCreateReward.periode_id) }} berhasil {{ pageModeIsEdit() ? 'diperbarui' : 'ditambahkan' }}.</span>
      </div>
    </ng-template>
  </div>
</app-modal>

<app-modal #modalConfirmPeriodeChange [modalConfig]="modalConfirmPeriodeChangeConfig" [modalOptions]="{ size: 'md' }">
  <div class="d-flex flex-column justify-content-center align-items-center text-center">
    <span>
      Apakah anda yakin untuk mengganti periode reward produk?<br />
      Data yang diinput akan hilang.
    </span>
  </div>
</app-modal>
