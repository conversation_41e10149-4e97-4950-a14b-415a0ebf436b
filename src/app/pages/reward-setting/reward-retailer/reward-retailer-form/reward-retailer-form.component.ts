import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { PageInfoService, PageLink } from '@metronic/layout';
import { RewardSettingService } from '../../reward-setting.service';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { EmbedVideoService } from '@services/embed-video.service';
import { IDetailRetailerReward, IParamPage_RewardSettingRetailerForm } from '../../reward-setting.interface';
import { SafeHtml } from '@angular/platform-browser';
import { RewardRetailerService } from '../reward-retailer.service';
import { BehaviorSubject } from 'rxjs';
import { ModalConfirmationRewardRetailerComponent } from '../components/modal-confirmation-reward-retailer/modal-confirmation-reward-retailer.component';

@Component({
  selector: 'app-reward-retailer-form',
  templateUrl: './reward-retailer-form.component.html',
  styleUrls: ['./reward-retailer-form.component.scss'],
})
export class RewardRetailerFormComponent implements OnInit {
  form: FormGroup;
  params: Params | IParamPage_RewardSettingRetailerForm;
  links: PageLink[];
  imageData: { id: string; images: string[] };
  videoIframeHtml?: SafeHtml;
  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  payloadForm: any;
  detailRetailer: BehaviorSubject<IDetailRetailerReward> = new BehaviorSubject<IDetailRetailerReward>({} as IDetailRetailerReward);

  get ImageArray() {
    return this.form.get('image_urls') as FormArray;
  }

  get FormVidio() {
    return this.form.get('video_url');
  }

  @ViewChild('modalConfirmation') modalConfirmation: ModalConfirmationRewardRetailerComponent;

  constructor(
    private fb: FormBuilder,
    private pageInfoService: PageInfoService,
    private rewardSettingService: RewardSettingService,
    private rewardRetailerService: RewardRetailerService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private snackbar: MatSnackBar,
    private embedService: EmbedVideoService,
    private ref: ChangeDetectorRef
  ) {
    this.initForm();
    this.initPageInfo();
  }

  ngOnInit() {
    this.handleData();
    this.FormVidio?.valueChanges.subscribe((value) => {
      if (this.isValidYouTubeUrl(value)) {
        this.onVideoUrlChange({ target: { value } });
      } else {
        this.videoIframeHtml = undefined;
      }
    });
    setTimeout(() => {
      this.isLoading.next(false);
      this.ref.detectChanges();
    }, 2000);
  }

  initForm() {
    this.form = this.fb.group({
      code: this.fb.control(history.state.code),
      level_name: this.fb.control(history.state.level_name, Validators.required),
      product_name: this.fb.control('', Validators.required),
      description: this.fb.control('', Validators.required),
      image_urls: this.fb.array([], Validators.required),
      video_url: this.fb.control(''),
    });
  }

  handleData() {
    if (this.pageModeIsActivated() || this.pageModeIsEdit()) {
      this.rewardRetailerService.getDetailRetailerReward(this.params.level_id).subscribe((value) => {
        if (Object.keys(value).length === 0) return;
        this.form.controls['product_name'].setValue(value.product_name);
        this.form.controls['description'].setValue(value.description);
        this.form.controls['video_url'].setValue(value.video_url);
        const images = value.image_urls.map((image) => ({ id: '', url: image }));
        this.imageData = { id: '', images: value.image_urls };
        this.handleFileUrl(images);
        this.form.updateValueAndValidity();
        this.form.markAsDirty();
        this.detailRetailer.next(value);
        this.pageLinkActivated();
        this.isLoading.next(false);
        this.ref.detectChanges();
      });
    }
  }

  pageLinkActivated() {
    if (this.pageModeIsActivated()) {
      this.links.push(
        {
          title: '...',
          path: '',
          isActive: false,
        },
        {
          title: '',
          path: '',
          isActive: false,
          isSeparator: true,
        },
        {
          title: this.detailRetailer.value.product_name ?? '-',
          path: '/reward-setting/retailer-level/retailer/detail/' + this.params.level_id,
          isActive: false,
        },
        {
          title: '',
          path: '',
          isActive: false,
          isSeparator: true,
        },
        {
          title: 'Aktifkan Hadiah Retailer',
          path: '',
          isActive: true,
        }
      );
    }
  }

  initPageInfo() {
    const state = history.state;
    this.handlePageParams();
    this.links = [
      {
        title: 'Reward Setting',
        path: '',
        isActive: false,
      },
      {
        title: '',
        path: '',
        isActive: false,
        isSeparator: true,
      },
    ];
    if (this.pageModeIsAdd() || this.pageModeIsEdit()) {
      this.links.push({
        title: 'Hadiah Retailer',
        path: 'reward-setting/retailer-level/retailer/list/' + this.params.level_id,
        isActive: false,
      });
      this.links.push({
        title: '',
        path: '',
        isActive: false,
        isSeparator: true,
      });
      if (this.pageModeIsAdd()) {
        this.links.push(
          {
            title: state.level_name ?? '-',
            path: 'reward-setting/retailer-level/retailer/list/' + this.params.level_id,
            isActive: false,
          },
          {
            title: '',
            path: '',
            isActive: false,
            isSeparator: true,
          },
          {
            title: this.pageModeIsAdd() ? 'Tambah' : 'Edit',
            path: '',
            isActive: true,
          }
        );
      } else {
        this.links.push({
          title: this.pageModeIsAdd() ? 'Tambah' : 'Edit',
          path: '',
          isActive: true,
        });
      }
    } else {
    }
    const _pageTitle = this.pageModeIsAdd() ? 'Tambah Hadiah Retailer' : 'Edit Hadiah Retailer';
    this.pageInfoService.updateTitle(_pageTitle, undefined, false);
    this.pageInfoService.updateBreadcrumbs(this.links);
  }

  pageModeIsActivated = () => this.pageHasParams() && this.params.mode === 'activated' && !!this.params.level_id;
  pageModeIsEdit = () => this.pageHasParams() && this.params.mode === 'edit' && !!this.params.level_id;
  pageModeIsAdd = () => this.pageHasParams() && this.params.mode == 'add' && !!this.params.level_id;
  pageHasParams = () => !!Object.keys(this.params).length;

  handlePageParams() {
    this.params = this.activatedRoute.snapshot.queryParams;
    if (!this.pageHasParams()) return;
  }

  handleFileUrl(evt: { id: string; url: string }[]) {
    if (evt.length <= 0) return;
    this.ImageArray.clear();
    evt.map((value) => {
      const fb = this.fb.group({
        id: value.id,
        url: value.url,
      });
      this.ImageArray.push(fb);
    });
  }

  handleEditFile(evt: { id: number; url: string }) {
    const { id, url } = evt;
    // this.imageData.images[id] = url;
    this.ImageArray.at(id).get('url')?.setValue(url);
  }

  handleRemoveFileUrl(evt: { id: string; url: string }) {
    const { url } = evt;
    const index = this.ImageArray.value.findIndex((_item: any) => _item.url === url);
    this.ImageArray.removeAt(index);
  }

  isValidYouTubeUrl(url: string): boolean {
    const regex = /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|embed\/)|youtu\.be\/)([a-zA-Z0-9_-]{11})(\S*)?$/;
    return regex.test(url);
  }

  onVideoUrlChange(e: any) {
    const value = (e.target as HTMLInputElement).value;
    if (!value) return;

    if (value.substring(0, 7) !== 'http://' && value.substring(0, 8) !== 'https://') {
      this.videoIframeHtml = undefined;
      return;
    }

    this.videoIframeHtml = this.embedService.embed(value);
  }

  handleSubmit() {
    const payload = {
      product_name: this.form.get('product_name')?.value,
      description: this.form.get('description')?.value,
      image_urls: this.form.get('image_urls')?.value?.map((item: any) => item.url),
      video_url: this.form.get('video_url')?.value,
    };
    this.payloadForm = payload;

    if (this.pageModeIsAdd()) {
      this.rewardRetailerService.createRewardRetailer(payload, this.params.level_id).subscribe((resp) => {
        if (resp && resp.success) {
          this.router.navigate(['reward-setting/retailer-level/retailer/list', this.params.level_id]);
        }
      });
    } else {
      this.modalConfirmation.open();
    }
  }
}
