<app-card [cardBodyClasses]="'pt-2'" [header]="false">
  <ng-container cardBody>
    <ng-container *ngIf="baseDatasource.isTableLoaded && baseDatasource.totalItem$.getValue() === 0; else elseFilterBlock" cardBody>
      <!--      <app-card-empty icon="{{ iconNone }}" text="Data sales discount tidak ditemukan."></app-card-empty>-->
    </ng-container>

    <ng-template #elseFilterBlock>
      <div class="table-responsive">
        <table [dataSource]="baseDatasource" class="table w-100 gy-5 table-row-bordered align-middle" mat-table matSort>
          <ng-container *ngFor="let tableColumn of tableColumns; index as i" [matColumnDef]="tableColumn.key">
            <ng-container *ngIf="tableColumn.isSortable; else notSortTable">
              <th
                *matHeaderCellDef
                [arrowPosition]="'after'"
                [class.min-w-200px]="tableColumn.key !== 'actions' && tableColumn.key !== 'id'"
                [mat-sort-header]="tableColumn.key"
                class="min-w-125px px-3"
                mat-header-cell
              >
                <div *ngIf="baseDatasource.isFinishLoadingSubject.value">
                  {{ tableColumn.title }}
                </div>
                <div *ngIf="!baseDatasource.isFinishLoadingSubject.value">
                  <app-table-content [width]="70"></app-table-content>
                </div>
              </th>
            </ng-container>
            <ng-template #notSortTable>
              <th
                *matHeaderCellDef
                [class.min-w-200px]="tableColumn.key !== 'actions' && tableColumn.key !== 'id'"
                [ngClass]="{
                  'min-w-70px text-center': tableColumn.key === 'actions',
                  'w-800px': tableColumn.key === 'name'
                }"
                class="min-w-125px px-3"
                mat-header-cell
              >
                <div *ngIf="baseDatasource.isFinishLoadingSubject.value">
                  {{ tableColumn.title }}
                </div>
                <div *ngIf="!baseDatasource.isFinishLoadingSubject.value">
                  <app-table-content [width]="180"></app-table-content>
                </div>
              </th>
            </ng-template>

            <td *matCellDef="let element" class="px-3" mat-cell>
              <ng-container [ngSwitch]="tableColumn.key">
                <div *ngSwitchCase="'actions'" class="text-center">
                  <button
                    [ngClass]="{
                      'is-download': element['is_download']
                    }"
                    class="btn btn-table-actions-download"
                  >
                    <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'icon'">
                      <span (click)="goToDetail(element['id'], element['name'])" [inlineSVG]="ASSET_ICON.IC_ACTIONS_TABLE" class="mx-3"></span>
                    </app-table-content>
                  </button>
                </div>

                <div *ngSwitchCase="'name'">
                  <app-table-content [count]="1" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
                    {{ element['name'] ?? '-' }}
                  </app-table-content>
                </div>

                <div *ngSwitchCase="'poin'">
                  <app-table-content [count]="1" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
                    <div>{{ element['minimum_point'] + '-' + element['maximum_point'] }}
                    </div>
                  </app-table-content>
                </div>

                <!--                <div *ngSwitchCase="'status'">-->
                <!--                  <app-table-content [height]="30" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [width]="120">-->
                <!--                    <span-->
                <!--                      class="badge badge__status"-->
                <!--                      [ngClass]="element['status_enum'] !== RewardSettingStatus.FINISH ? 'badge__status&#45;&#45;' + element['status_enum'] : 'text-gray-600'"-->
                <!--                    >-->
                <!--                      {{ element['status_string'] }}-->
                <!--                    </span>-->
                <!--                  </app-table-content>-->
                <!--                </div>-->

                <div *ngSwitchDefault>
                  <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    <span>{{ element[tableColumn.key] ?? '-' }} </span>
                  </app-table-content>
                </div>
              </ng-container>
            </td>
          </ng-container>

          <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
          <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
        </table>
      </div>
    </ng-template>
  </ng-container>
</app-card>
