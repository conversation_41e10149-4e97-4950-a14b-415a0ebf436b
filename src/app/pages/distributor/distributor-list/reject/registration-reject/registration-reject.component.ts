import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UtilitiesService } from '@services/utilities.service';
import { TableColumn } from '@shared/interface/table.interface';
import { BehaviorSubject } from 'rxjs';
import { API } from '@config/constants/api.constant';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { getDistributorTableColumns } from '../../../distributor.data';

@Component({
  selector: 'app-registration-reject',
  styleUrls: ['./registration-reject.component.scss'],
  template: `
    <app-card-table-list
      [url]="API.LIST_DISTRIBUTOR_REJECT_REGISTRATION"
      [tableColumns]="tableColumns"
      [privilegeDetail]="privilegeDetailReject"
      [actionPathDetail]="'/distributor/detail/reject/registration/'"
      (totalCount)="onTotalCount($event)"
    />
  `,
})
export class RegistrationRejectComponent implements OnInit {
  tableColumns: TableColumn[] = getDistributorTableColumns('reject');
  privilegeDetailReject = new BehaviorSubject(false);

  public static totalCount = new BehaviorSubject(0);

  constructor(public utilities: UtilitiesService, private rolePrivilegeService: RolePrivilegeService) {}

  ngOnInit() {
    this.privilegeDetailReject.next(this.rolePrivilegeService.checkPrivilege('DISTRIBUTOR', 'LIST_DITOLAK', 'TAB_LIST_REGISTRATION_REJECT', 'CTA_VIEW_DETAIL'));
    this.tableColumns = this.utilities.privilegeTableColumns(this.privilegeDetailReject.value, this.tableColumns);
  }

  onTotalCount = (e: number) => RegistrationRejectComponent.totalCount.next(e);

  protected readonly API = API;
}
