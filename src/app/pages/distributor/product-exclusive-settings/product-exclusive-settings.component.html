<ng-container *ngIf="listBrand$ | async"></ng-container>
<app-fullscreen-loader *ngIf="!listBrand.length" />
<app-product-exclusive-settings-form
  *ngIf="params"
  [businessName]="businessName"
  [distributorID]="params.id"
  [areaID]="distributorService.DistributorInfoData.area_id"
  [listBrand]="listBrand"
  [mode]="params.mode"
  (formPayload)="handleFormPayload($event)"
  (handleCancel)="handleConfirmCancel()"
></app-product-exclusive-settings-form>

<app-modal #modalResponseCreate [modalConfig]="modalResponseCreateConfig">
  <div class="d-flex flex-column justify-content-center align-items-center">
    <ng-container *ngIf="isLoadingPostData | async; else responseDone">
      <div class="mt-8">
        <mat-spinner></mat-spinner>
      </div>
    </ng-container>

    <ng-template #responseDone>
      <span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT"></span>
      <div class="mb-n8">
        <span>{{ modalResponseMessage.value }}</span>
      </div>
    </ng-template>
  </div>
</app-modal>

<app-modal #modalConfirmCancel [modalConfig]="modalConfirmCancelConfig">
  <div class="text-center">
    <p>{{ modalResponseMessage.value }}</p>
  </div>
</app-modal>
