import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RouterModule, Routes } from '@angular/router';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { ProductExclusiveSettingsComponent } from './product-exclusive-settings.component';
import { ComponentsModule } from '@shared/components/components.module';
import { ProductsModule } from '../../products/products.module';
import { ProductExclusiveSettingsFormComponent } from './product-exclusive-settings-form/product-exclusive-settings-form.component';
import { MatRadioModule } from '@angular/material/radio';

import { InputListboxCakupanSubareaComponent } from '../components/input-listbox-cakupan-subarea/input-listbox-cakupan-subarea.component';
import { MatButtonModule } from '@angular/material/button';
import { DirectiveModule } from '@directives/directive.module';
import { MatCheckboxModule } from '@angular/material/checkbox';

const routing: Routes = [
  {
    path: 'form',
    component: ProductExclusiveSettingsComponent,
  },
];

@NgModule({
  declarations: [ProductExclusiveSettingsComponent, ProductExclusiveSettingsFormComponent, InputListboxCakupanSubareaComponent],
  imports: [
    CommonModule,
    RouterModule.forChild(routing),
    FormsModule,
    ReactiveFormsModule,
    ComponentsModule,
    ProductsModule,
    InlineSVGModule,
    MatProgressSpinnerModule,
    MatRadioModule,
    MatButtonModule,
    DirectiveModule,
    MatCheckboxModule,
  ],
  exports: [ProductExclusiveSettingsComponent, ProductExclusiveSettingsFormComponent],
})
export class ProductExclusiveSettingsModule {}
