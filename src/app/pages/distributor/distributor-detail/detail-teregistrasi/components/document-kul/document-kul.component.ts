import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { UtilitiesService } from '@services/utilities.service';
import { IPrintKUL } from '../../../../interfaces/distributor.interface';
import { Observable } from 'rxjs';
import { NgxPrintElementDirective, NgxPrintElementService } from 'ngx-print-element';
import { ActivatedRoute, Router } from '@angular/router';
import { PageInfoService } from '@metronic/layout';
import { DistributorService } from '../../../../distributor.service';
import { AsyncPipe, NgIf } from '@angular/common';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { ComponentsModule } from '@shared/components/components.module';
import { MatButton } from '@angular/material/button';

@Component({
  selector: 'app-document-kul',
  templateUrl: './document-kul.component.html',
  styleUrls: ['./document-kul.component.scss'],
  standalone: true,
  imports: [NgxPrintElementDirective, AsyncPipe, InlineSVGModule, NgIf, ComponentsModule, MatButton],
})
export class DocumentKULComponent implements OnInit {
  @Input() dataKul: IPrintKUL;

  imgHeader: string = STRING_CONSTANTS.ICON.IC_HEADER_KUL;
  browser: string;
  id: string;
  dataKUL$: Observable<IPrintKUL>;

  @ViewChild('printSectionRef') printSectionRef: ElementRef<HTMLElement>;

  constructor(
    public utilities: UtilitiesService,
    private pageInfoService: PageInfoService,
    private activeRoute: ActivatedRoute,
    private router: Router,
    private printService: NgxPrintElementService,
    private distributorService: DistributorService
  ) {}

  ngOnInit(): void {
    this.browser = this.utilities.getBrowserName();
    this.initPage();
  }

  initPage() {
    this.id = this.activeRoute.snapshot.params['id'];
    this.pageInfoService.updateTitle('', undefined, false);
    this.getDataPrintKul();
  }

  getDataPrintKul() {
    this.dataKUL$ = this.distributorService.getDataPrintKUL(this.id);
  }

  handleCredit = (data: any) => {
    if (!data || data === 'null') return '-';
    return this.utilities.toRupiah(Number(data));
  };

  handleDataNull = (value: string | number) => {
    if (!value || value === 'null') return '-';
    return value;
  };

  onCancel = () => {
    const _q = this.activeRoute.snapshot.queryParams;
    return this.router.navigate([`/distributor/detail/${_q.status}/${_q.tab}/${this.id}`]);
  };

  onPrint() {
    this.printService.print(this.printSectionRef).subscribe((res) => res);
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
