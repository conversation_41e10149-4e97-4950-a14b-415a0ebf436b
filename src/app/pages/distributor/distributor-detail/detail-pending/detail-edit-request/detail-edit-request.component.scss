::ng-deep {
  .mat-stepper-horizontal {
    font-family: 'Inter', sans-serif;
  }

  .mat-stepper-horizontal .mat-horizontal-content-container {
    padding: 0 !important;
  }

  .mat-step-header {
    margin: 20px 0px 20px 0px;
  }
}

.mat-stepper-horizontal {
  margin-top: 8px;
}

mat-stepper {
  ::ng-deep .mat-stepper-horizontal-line {
    color: #D1D1D1;
  }

  ::ng-deep .mat-step-icon {
    width: 40px;
    height: 40px;
    background-color: #D1D1D1;
    color: #fff;

    &-state-done {
      background-color: #688238;
    }
  }

  ::ng-deep .mat-step-label {
    color: #808080;
  }

  ::ng-deep .mat-step-icon-selected {
    background-color: #688238;
    color: #fff;
  }

  ::ng-deep .mat-step-label-selected {
    font-weight: bold;
  }
}

.btn-before {
  color: #688238;
}

.min-w-200 {
  min-width: 200px;
}
