<ng-container>
  <div class="mb-7">
    <app-card-verification-form
      (afterActionVerification)="afterActionVerification($event)"
      (cardNoteChanged)="noteChanged($event)"
      [bodyData]="bodyDataBusiness.value"
      [cardNoteValue]="noteValue(EnumCardVerificationSection.BUSINESS_INFORMATION)"
      [dataValue]="dataValue"
      [isLoading]="isLoading"
      [isNotHaveRevision]="detailRegistrationService.checkNotHaveRevision(dataValue.value.business_information)"
      [isViewOnly]="isViewOnly"
      [sessionKey]="revisionType"
      [status]="dataValue.value.business_information?.status_verified_enum"
      [title]="'Informasi Usaha'"
      [typeSection]="EnumCardVerificationSection.BUSINESS_INFORMATION"
      [type]="EnumCardVerificationForm.ASIDE_DOCUMENT"
    />
  </div>
  <div class="mb-7">
    <app-card-verification-form
      (afterActionVerification)="afterActionVerification($event)"
      (cardNoteChanged)="noteChanged($event)"
      [bodyDataBranch]="bodyDataSupplier.value"
      [cardNoteValue]="noteValue(EnumCardVerificationSection.SUPPLIER_INFORMATION)"
      [dataValue]="dataValue"
      [isLoading]="isLoading"
      [isNotHaveRevision]="checkNotHaveRevisionSupplier()"
      [isViewOnly]="isViewOnly"
      [sessionKey]="revisionType"
      [status]="dataValue.value.supplier_information?.status_verified_enum"
      [title]="'Informasi Supplier'"
      [typeSection]="EnumCardVerificationSection.SUPPLIER_INFORMATION"
      [type]="EnumCardVerificationForm.GROUP"
    />
  </div>
</ng-container>
