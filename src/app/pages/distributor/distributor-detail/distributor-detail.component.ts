import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { DistributorStatusEnum, DistributorStatusEnumTabString, DistributorTabStatus } from '@config/enum/distributor.enum';
import { BehaviorSubject } from 'rxjs';
import { IHeaderDistributorDetail } from '../interfaces/detail-header.interface';
import { ICardProfileBody } from '@models/card.model';
import { PageInfoService, PageLink } from '@metronic/layout';
import { DistributorService } from '../distributor.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { UtilitiesService } from '@services/utilities.service';
import { SubMenu } from '../enum/submenu.enum';
import { IListRejectReason } from '../interfaces/distributor.interface';
import { DetailRegistrationDistributorService } from './detail-pending/detail-registration/detail-registration.service';

@Component({
  selector: 'app-distributor-detail',
  templateUrl: './distributor-detail.component.html',
  styleUrls: ['./distributor-detail.component.scss'],
})
export class DistributorDetailComponent implements OnInit {
  snapshotParams!: Params;
  links!: PageLink[];
  isLoading = new BehaviorSubject(false);

  distributorID!: string;
  detailHeaderSubject = new BehaviorSubject({} as IHeaderDistributorDetail);
  detailCardHeaderLabelList: ICardProfileBody[];
  rejectReason!: IListRejectReason[];

  constructor(
    private activatedRoute: ActivatedRoute,
    private pageInfoService: PageInfoService,
    private distributorService: DistributorService,
    private registrationService: DetailRegistrationDistributorService,
    private utils: UtilitiesService
  ) {}

  ngOnInit() {
    this.snapshotParams = this.activatedRoute.snapshot.params;
    this.distributorID = this.snapshotParams.id;
    this.registrationService.idValue = this.distributorID;
    this.getDetailHeader();
  }

  getDetailHeader() {
    this.isLoading.next(true);
    this.distributorService.getDistributorDetailHeader(this.distributorID).subscribe((resp) => {
      if (!resp) return;
      this.detailHeaderSubject.next(resp);
      this.detailCardHeaderLabelList = this.generateCardHeaderLabel();
      this.rejectReason = resp.reject_reason;
      this.initPageInfo();
      this.isLoading.next(false);
    });
  }

  initPageInfo() {
    const { status, tab } = this.snapshotParams;
    if (status === 'detail' && tab === 'log') return;
    this.pageInfoService.updateTitle('Detail Distributor');
    this.links = [
      {
        title: 'Distributor',
        path: '',
        isActive: false,
      },
      this.pageInfoService.BreadcrumbSeparator,
      {
        title: this.utils.SubMenuBreadcrumbTitles(status, SubMenu),
        path: `distributor/${status}/${tab}`,
        isActive: false,
      },
      this.pageInfoService.BreadcrumbSeparator,
      {
        title: this.utils.toCapitalize(this.detailHeaderSubject.value.business_name),
        path: '',
        isActive: true,
      },
    ];

    this.pageInfoService.updateBreadcrumbs(this.links);
  }

  generateCardHeaderLabel() {
    const { code, sales_sub_area, sales_area, created_date } = this.detailHeaderSubject.value;
    return [
      {
        icon: STRING_CONSTANTS.ICON.IC_DISTRIBUTOR,
        value: code,
      },
      {
        icon: STRING_CONSTANTS.ICON.IC_LOCATION,
        value: sales_sub_area + ', ' + sales_area,
      },
      {
        icon: STRING_CONSTANTS.ICON.IC_CALENDAR,
        value: 'Bergabung sejak ' + this.utils.formatEpochToDate(created_date, 'intl'),
      },
    ];
  }

  getRejectReasonTitleCard() {
    const { status_enum } = this.detailHeaderSubject.value;
    let _text = 'Alasan Penolakan';
    _text += status_enum === DistributorStatusEnum.REGISTRATION_REJECTED ? ' Admin' : status_enum === DistributorStatusEnum.REGISTRATION_REJECTED_FINANCE ? ' Finance' : '';
    return _text;
  }

  getProfileHeaderStatus() {
    const { status_title, status_enum } = this.detailHeaderSubject.value;
    if (this.snapshotParams.status === 'detail') return undefined;
    else return { string: status_title, value: status_enum };
  }

  hasRejectReason = () => this.detailHeaderSubject.value && !!this.detailHeaderSubject.value.reject_reason?.length;

  isDistributorParamStatus = (val: DistributorTabStatus) => this.snapshotParams && this.snapshotParams.status === val;

  isDistributorParamTab = (val: DistributorStatusEnumTabString) => this.snapshotParams && this.snapshotParams.tab === val;

  showReasonNonActive() {
    const { status_enum } = this.detailHeaderSubject.value;
    return status_enum === DistributorStatusEnum.NON_ACTIVE;
  }

  protected readonly DistributorTabStatus = DistributorTabStatus;
  protected readonly DistributorStatusEnumTabString = DistributorStatusEnumTabString;
}
