import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { DistributorDetailComponent } from './distributor-detail.component';
import { DetailEditRequestComponent } from './detail-pending/detail-edit-request/detail-edit-request.component';
import { DetailRegistrationComponent } from './detail-pending/detail-registration/detail-registration.component';
import { DetailVerificationComponent } from './detail-pending/detail-verification/detail-verification.component';
import { ComponentsModule } from '@shared/components/components.module';
import { MatTabsModule } from '@angular/material/tabs';
import { MatStepperModule } from '@angular/material/stepper';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { BusinessDataComponent } from './detail-pending/detail-registration/business-data/business-data.component';
import { CreditCeilingComponent } from './detail-pending/detail-registration/credit-ceiling/credit-ceiling.component';
import { ShippingAddressComponent } from './detail-pending/detail-registration/shipping-address/shipping-address.component';
import { CardVerificationFormComponent } from '../components/card-verification-form/card-verification-form.component';
import { DistributorDataComponent } from './detail-pending/detail-registration/distributor-data/distributor-data.component';
import { GoogleMapsModule } from '@angular/google-maps';
import { TabInformasiDistributorComponent } from './components/tab-informasi-distributor/tab-informasi-distributor.component';
import { TabPlafonKreditComponent } from './components/tab-plafon-kredit/tab-plafon-kredit.component';
import { TabProductExclusiveComponent } from './components/tab-product-exclusive/tab-product-exclusive.component';
import { TabAlamatPengirimanComponent } from './components/tab-alamat-pengiriman/tab-alamat-pengiriman.component';
import { TabDokumenComponent } from './components/tab-dokumen/tab-dokumen.component';
import { CardTabSectionComponent } from '../components/card-tab-section/card-tab-section.component';
import { DocumentComponent } from './detail-pending/detail-registration/document/document.component';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbPopoverModule } from '@ng-bootstrap/ng-bootstrap';
import { InformasiNpwpComponent } from './detail-pending/detail-registration/informasi-npwp/informasi-npwp.component';
import { InformasiDistributorComponent } from './detail-pending/detail-registration/informasi-distributor/informasi-distributor.component';
import { EditRequestTabPlafonKreditComponent } from './detail-pending/detail-edit-request/edit-request-plafon/edit-request-tab-plafon-kredit.component';
import { DetailTeregistrasiActiveComponent } from './detail-teregistrasi/detail-teregistrasi-active/detail-teregistrasi-active.component';
import { DetailTeregistrasiRegisteredComponent } from './detail-teregistrasi/detail-teregistrasi-registered/detail-teregistrasi-registered.component';
import { VerificationTabInformasiDistributorComponent } from './detail-pending/detail-verification/verification-tab-informasi-distributor/verification-tab-informasi-distributor.component';
import { TabLogComponent } from './components/tab-log/tab-log.component';
import { EditRequestTabInformasiDistributorComponent } from './detail-pending/detail-edit-request/edit-request-informasi-distributor/edit-request-tab-informasi-distributor.component';
// import { NgxPrintElementModule } from 'ngx-print-element';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
// import { DocumentKULComponent } from '../'
import { ProductExclusiveComponent } from './components/product-exclusive/product-exclusive.component';
import { HistoryComponent } from './components/product-exclusive/history/history.component';
import { DetailNonActiveComponent } from './detail-pending/detail-non-active/detail-non-active.component';
import { DocumentKULComponent } from './detail-teregistrasi/components/document-kul/document-kul.component';

const routing: Routes = [
  {
    path: ':status/:tab/:id',
    component: DistributorDetailComponent,
    data: {
      enum: 'DETAIL_DISTRIBUTOR',
    },
  },
];

@NgModule({
  declarations: [
    DetailRegistrationComponent,
    DetailVerificationComponent,
    DetailEditRequestComponent,
    DetailTeregistrasiActiveComponent,
    DetailTeregistrasiRegisteredComponent,
    DistributorDataComponent,
    BusinessDataComponent,
    CreditCeilingComponent,
    ShippingAddressComponent,
    DocumentComponent,
    CardVerificationFormComponent,
    TabInformasiDistributorComponent,
    TabPlafonKreditComponent,
    TabProductExclusiveComponent,
    TabAlamatPengirimanComponent,
    TabDokumenComponent,
    CardTabSectionComponent,
    InformasiNpwpComponent,
    InformasiDistributorComponent,
    EditRequestTabPlafonKreditComponent,
    EditRequestTabInformasiDistributorComponent,
    VerificationTabInformasiDistributorComponent,
    // legacy detail components
    // PlafonKreditComponent,
    // AlamatPengirimanComponent,
    // DokumentComponent,
    // DocumentKULComponent,
    TabLogComponent,
    ProductExclusiveComponent,
    HistoryComponent,
    DetailNonActiveComponent,
  ],
  imports: [
    DocumentKULComponent,
    // modules
    MatTableModule,
    MatSortModule,
    MatTabsModule,
    MatStepperModule,
    MatButtonModule,
    MatMenuModule,
    FormsModule,
    ReactiveFormsModule,
    InlineSVGModule,
    NgbPopoverModule,
    CommonModule,
    RouterModule.forChild(routing),
    ComponentsModule,
    GoogleMapsModule,
    // NgxPrintElementModule,
    MatProgressSpinnerModule,
  ],
  exports: [
    CardTabSectionComponent,
    DetailRegistrationComponent,
    DetailVerificationComponent,
    DetailEditRequestComponent,
    DetailTeregistrasiActiveComponent,
    DetailTeregistrasiRegisteredComponent,
    // PlafonKreditComponent,
    // AlamatPengirimanComponent,
    // DokumentComponent,
    ProductExclusiveComponent,
    InformasiNpwpComponent,
    InformasiDistributorComponent,
    CreditCeilingComponent,
    CardVerificationFormComponent,
    DistributorDataComponent,
    ShippingAddressComponent,
    DocumentComponent,
    DocumentKULComponent,
    EditRequestTabPlafonKreditComponent,
    VerificationTabInformasiDistributorComponent,
    ProductExclusiveComponent,
    HistoryComponent,
    DetailNonActiveComponent,
  ],
})
export class DistributorDetailModule {}
