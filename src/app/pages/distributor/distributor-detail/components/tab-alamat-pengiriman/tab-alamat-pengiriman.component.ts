import { Component, OnInit } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { ActivatedRoute, Params } from '@angular/router';
import { DistributorService } from '../../../distributor.service';
import { CardTabUtilsService } from '../../../components/card-tab-section/card-tab-utils.service';
import { IDeliveryAddresses, IDetailDeliveryV1 } from '../../../interfaces/detail-delivery.interface';
import { CardSectionEnums, ICardTabSection } from '../../../components/card-tab-section/card-tab-section.interface';
import { InterfaceDataMapperService } from '@services/interface-data-mapper.service';
import { EnumDeliveryAddressCard } from '../../../enum/label-section.enum';
import { STRING_CONSTANTS } from '@config/constants/string.constants';

@Component({
  selector: 'app-tab-alamat-pengiriman',
  templateUrl: './tab-alamat-pengiriman.component.html',
  styleUrls: ['./tab-alamat-pengiriman.component.scss'],
})
export class TabAlamatPengirimanComponent implements OnInit {
  isLoading = new BehaviorSubject(false);
  snapshotParams!: Params;

  detailDeliverySubject = new BehaviorSubject({} as IDeliveryAddresses);

  //sections data
  bodyDelivery = new BehaviorSubject({ cardTitle: '' } as ICardTabSection<typeof CardSectionEnums.EnumCardSectionDeliveryAddress>);
  bodyDeliveryGroup: any = [];

  constructor(
    private activatedRoute: ActivatedRoute,
    private distributorService: DistributorService,
    private cardTabUtilsService: CardTabUtilsService,
    private interfaceMapperService: InterfaceDataMapperService
  ) {}

  ngOnInit() {
    this.snapshotParams = this.activatedRoute.snapshot.params;
    this.getDetail();
  }

  getDetail() {
    this.isLoading.next(true);
    this.distributorService.getDetailDeliveryAddress(this.snapshotParams.id).subscribe((resp) => {
      if (!resp) return;
      this.detailDeliverySubject.next({
        delivery_address: resp && resp.data,
      });
      this.mapBodyGroupData();
      this.isLoading.next(false);
    });
  }

  mapBodyGroupData() {
    const _data = this.detailDeliverySubject.value;
    _data.delivery_address.map((item) => {
      const _body = this.generateBodyData('delivery_address' as EnumDeliveryAddressCard, item);
      this.bodyDeliveryGroup.push(_body);
    });
  }

  generateBodyData(key: string, data: IDetailDeliveryV1) {
    const { label } = data;
    const bodySubject = new BehaviorSubject({ cardTitle: label } as ICardTabSection<typeof CardSectionEnums.EnumCardSectionDeliveryAddress>);
    // generate card body group
    const _bodyGroup = this.cardTabUtilsService.handleDeliveryAddressBodyGroup(data);
    // console.log('_bodyGroup', _bodyGroup);
    bodySubject.next({
      ...bodySubject.value,
      cardEnum: key,
      cardBodyGroup: _bodyGroup,
    });

    return bodySubject;
  }

  generateCardBodyGroup() {}

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
