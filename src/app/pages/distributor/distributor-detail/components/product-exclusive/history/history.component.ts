import { ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { TableColumn } from '@shared/interface/table.interface';
import { IModalDetailWithImage, ModalConfig, ModalOption } from '@shared/components/modal/modal.interface';
import { PageInfoService, PageLink } from '@metronic/layout';
import { BehaviorSubject, Subscription } from 'rxjs';
import { BaseDatasource } from '@shared/base/_base.datasource';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { ActivatedRoute, Router } from '@angular/router';
import { UrlParamService } from '@services/urlParam.service';
import { BaseService } from '@services/base-service.service';
import { BaseTableService } from '@shared/base/base-table-service.service';
import { UtilitiesService } from '@services/utilities.service';
import { API } from '@config/constants/api.constant';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { ModalDetailWithImageComponent } from '@shared/components/modal/modal-detail-with-image/modal-detail-with-image.component';
import { IGenericIdName } from '@shared/interface/generic';
import { FilterService } from '@services/filter.service';
import { IKeyValue, IListProductExclusive } from '../../../../interfaces/distributor.interface';
import { IDetailInformasi } from '../../../../interfaces/detail-informasi.interface';

@Component({
  selector: 'app-product-exclusive-history',
  templateUrl: './history.component.html',
  styleUrls: ['./history.component.scss'],
})
export class HistoryComponent implements OnInit, OnDestroy {
  tableColumns: TableColumn[];
  modalDetailConfig: ModalConfig;
  typeTab: string | null = '';
  // status: string | null = '';
  modalDetailOption: ModalOption;
  displayedColumns: string[];
  links: Array<PageLink>;
  string_filter: string = '';
  distributorExclusiveList: IListProductExclusive[];
  baseDatasource: BaseDatasource<IListProductExclusive>;
  detailProductExclusiveData: BehaviorSubject<IModalDetailWithImage> = new BehaviorSubject<IModalDetailWithImage>({} as IModalDetailWithImage);
  id: BehaviorSubject<string | null> = new BehaviorSubject<string | null>('');
  detailDistributor: BehaviorSubject<IDetailInformasi | null> = new BehaviorSubject<IDetailInformasi | null>(null);
  imageList: BehaviorSubject<string[]> = new BehaviorSubject<string[]>([]);
  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  iconNone: string = STRING_CONSTANTS.ILLUSTRATIONS.IL_BOX_NONE;
  iconInfo: string = STRING_CONSTANTS.ICON.IC_INFO;
  iconAction: string = STRING_CONSTANTS.ICON.IC_ACTIONS_TABLE;
  STRING_CONSTANTS = STRING_CONSTANTS;
  productId: string;
  parentMenu: string = 'DISTRIBUTOR';
  privilegeApproval: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  privilegePrintKUL: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  privilegeResetPassword: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  keyDetailPrivilege: string = 'DETAIL_DISTRIBUTOR';
  @ViewChild('modalResetPassword') private modalResetPassword: ModalComponent;
  @ViewChild('modalDetail') private modalDetail: ModalDetailWithImageComponent;
  private unsubscribe: Subscription[] = [];

  constructor(
    private pageInfoService: PageInfoService,
    private activeRoute: ActivatedRoute,
    private urlParamService: UrlParamService,
    private baseService: BaseService,
    private baseTableService: BaseTableService<any>,
    private activatedRoute: ActivatedRoute,
    private ref: ChangeDetectorRef,
    private router: Router,
    public utilities: UtilitiesService,
    private rolePrivilegeService: RolePrivilegeService,
    private filterService: FilterService
  ) {}

  ngOnInit(): void {
    this.baseTableService.responseDatabase.subscribe((response) => (this.baseDatasource = response));

    this.getId();
    this.setTableData();
    this.handlePrivilege();
    this.queryHandler();
    this.initPageInfo();
  }

  getId() {
    this.id.next(this.activeRoute.snapshot.params.id);
    this.getDetailDistributor();
    this.typeTab = this.activatedRoute.snapshot.url[1].path;

    this.modalDetailConfig = {
      showFooter: false,
      showHeader: false,
    };

    this.modalDetailOption = {
      size: 'lg',
    };
  }

  initPageInfo() {
    this.pageInfoService.updateTitle('Detail Distributor');
    this.detailDistributor.subscribe((data) => {
      this.links = [
        {
          title: 'Distributor',
          path: '',
          isActive: false,
        },
        {
          title: '',
          path: '',
          isActive: false,
          isSeparator: true,
        },
        {
          title: 'Teregistrasi',
          path: 'distributor/teregistrasi/active',
          isActive: false,
        },
        {
          title: '',
          path: '',
          isActive: false,
          isSeparator: true,
        },
        {
          title: data?.business_name ?? '-',
          path: 'distributor/teregistrasi/active/' + this.id.value,
          isActive: false,
        },
        {
          title: '',
          path: '',
          isActive: false,
          isSeparator: true,
        },
        {
          title: 'Riwayat Produk Eksklusif',
          path: '',
          isActive: true,
        },
      ];
      this.pageInfoService.updateBreadcrumbs(this.links);
    });

    const dataSubs = this.baseDatasource.tableSubject.subscribe((resp) => (this.distributorExclusiveList = resp));
    this.unsubscribe.push(dataSubs);
    this.ref.detectChanges();
  }

  setTableData() {
    this.isLoading.next(false);
    this.tableColumns = [
      { title: 'PRODUK', key: 'product_name' },
      { title: 'VARIAN', key: 'list_variants' },
      { title: 'CAKUPAN AREA', key: 'area' },
      { title: 'CAKUPAN SUB AREA', key: 'list_sub_area' },
      { title: 'DIUBAH OLEH', key: 'submitted_by' },
      { title: 'TANGGAL DIUBAH', key: 'submitted_time' },
      { title: 'ACTION', key: 'actions' },
    ];

    this.displayedColumns = this.tableColumns.map((head) => head.key);

    if (this.distributorExclusiveList) {
      this.baseDatasource.tableSubject.next(this.distributorExclusiveList);
    }
  }

  setDetailProduct(data: IListProductExclusive) {
    const title: string = 'DETAIL PRODUK EKSKLUSIF';
    const detail: IKeyValue[] = [
      { key: 'Reason', value: data.reasons },
      { key: 'Produk', value: data.product_name },
      { key: 'Cakupan Area', value: data.area ?? '-' },
      { key: 'Cakupan Sub Area', value: this.mapSubAreaObject(data.list_sub_area) },
      { key: 'Varian', value: data.list_variants?.length > 0 ? data.list_variants.join(', ').toString() : '-' },
      { key: 'Dibuat Oleh', value: data.submitted_by },
      {
        key: 'Tanggal Dibuat',
        value: data.submitted_time ? this.utilities.timeStampToDate(data.submitted_time, 'dd-MM-yyyy HH:mm:ss') : '-',
      },
    ];
    const images = data.product_document ? data.product_document : [];
    this.detailProductExclusiveData.next({ title, detail, images });
  }

  queryHandler() {
    const paramSubs = this.activeRoute.queryParams.subscribe((data) => {
      this.string_filter = data.string_filter;
      const _queryParams = this.urlParamService.sliceQueryParams();
      this.loadDataTable(_queryParams ? _queryParams : '');
    });

    this.unsubscribe.push(paramSubs);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  loadDataTable(params: string) {
    return this.baseTableService.loadDataTable(API.GET_LIST_HISTORY_PRODUCT_EXCLUSIVE + this.id.value, params);
  }

  getDetailDistributor() {
    this.baseService.getData<IDetailInformasi>(API.DETAIL_INFORMASI_DISTRIBUTOR + this.id.value).subscribe((response) => {
      if (response) {
        this.detailDistributor.next(response.data);
      }
    });
  }

  handleAction(data: IListProductExclusive) {
    const mainData = data;
    if (mainData) {
      this.modalDetail.setUrlPreview = mainData.product_document ? mainData.product_document[0] : '';
      this.modalDetail.setImageList = mainData.product_document ? mainData.product_document : [];
      this.setDetailProduct(mainData);
      this.ref.detectChanges();
      return this.modalDetail.openModal();
    }
  }

  changePageEvent($event: any) {
    this.filterService.changePageEvent($event, this.string_filter ?? '');
  }

  onSearch(event: string) {
    this.filterService.onSearch(event);
    this.string_filter = '';
  }

  handlePrivilege() {
    this.privilegeApproval.next(this.rolePrivilegeService.checkPrivilege(this.parentMenu, this.keyDetailPrivilege, 'CTA_APPROVAL_PENDING_REGISTRATION'));
    this.privilegePrintKUL.next(this.rolePrivilegeService.checkPrivilege(this.parentMenu, this.keyDetailPrivilege, 'CTA_PRINT_KUL'));
    this.privilegeResetPassword.next(this.rolePrivilegeService.checkPrivilege(this.parentMenu, this.keyDetailPrivilege, 'CTA_RESET_PASSWORD'));
  }

  async handleBack() {
    return this.router.navigate(['distributor/teregistrasi/active/' + this.id.value]);
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  onClickResetPassword() {
    this.modalResetPassword.open().then();
  }

  setVariantItem(item: string[]) {
    if (item?.length) {
      const data = item[0];
      return {
        count: item.length - 1,
        name: data,
      };
    }
  }

  mapSubAreaObject(item: IGenericIdName[]) {
    const result = item?.length ? item.join(', ') : 'SEMUA';
    return this.utilities.toCapitalize(result);
  }
}
