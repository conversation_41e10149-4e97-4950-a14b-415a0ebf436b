import { Component, Input, OnInit } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { PageInfoService, PageLink } from '@metronic/layout';
import { ActivatedRoute, Params } from '@angular/router';
import { IHeaderDistributorDetail } from '../../interfaces/detail-header.interface';
import { UtilitiesService } from '@services/utilities.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { TabLabelEnumDetailDistributor, TabListLabelDetailDistributor } from '../../enum/tablist-label.enum';
import { ITabList } from '@shared/interface/tablist.interface';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { DistributorStatusEnum } from '@config/enum/distributor.enum';

@Component({
  selector: 'app-detail-distributor-log',
  templateUrl: './detail-distributor-log.component.html',
  styleUrls: ['./detail-distributor-log.component.scss'],
})
export class DetailDistributorLogComponent implements OnInit {
  @Input() detailHeaderSubject!: BehaviorSubject<IHeaderDistributorDetail>;

  snapshotParams!: Params;
  links!: PageLink[];
  isLoading: BehaviorSubject<boolean> = new BehaviorSubject(false);

  tabIndex = 0;
  tabList: ITabList[] = [
    {
      tab: 'informasi_distributor',
      enum: TabLabelEnumDetailDistributor.TAB_DISTRIBUTOR_INFORMATION,
      labelKey: 'informasi_distributor',
      privilege: null,
      index: 0,
    },
    {
      tab: 'plafon_kredit',
      enum: TabLabelEnumDetailDistributor.TAB_PLAFON_CREDIT,
      labelKey: 'plafon_kredit',
      privilege: null,
      index: 1,
    },
    {
      tab: 'alamat_pengiriman',
      enum: TabLabelEnumDetailDistributor.TAB_ALAMAT_PENGIRIMAN,
      labelKey: 'alamat_pengiriman',
      privilege: null,
      index: 2,
    },
    {
      tab: 'dokumen',
      enum: TabLabelEnumDetailDistributor.TAB_DOCUMENTS,
      labelKey: 'dokumen',
      privilege: null,
      index: 3,
    },
  ];

  get DetailHeader() {
    return this.detailHeaderSubject.value;
  }

  get IsRejectNonActive() {
    return Object.keys(this.DetailHeader).length && this.DetailHeader.status_enum === DistributorStatusEnum.REJECT_NONACTIVATED;
  }

  get IsNonActive() {
    return Object.keys(this.DetailHeader).length && this.DetailHeader.status_enum === DistributorStatusEnum.NON_ACTIVATED;
  }

  constructor(
    private activatedRoute: ActivatedRoute,
    private pageInfoService: PageInfoService,
    public utils: UtilitiesService,
    private rolePrivilegeService: RolePrivilegeService
  ) {}

  ngOnInit(): void {
    this.snapshotParams = this.activatedRoute.snapshot.params;
    this.getDetailHeader();
    this.initTabList();
  }

  getDetailHeader() {
    this.detailHeaderSubject.subscribe((resp) => {
      if (!Object.keys(resp).length) return;
      this.initPageInfo();
    });
  }

  initTabList() {
    this.tabList.map((tab) => (tab.privilege = this.rolePrivilegeService.checkPrivilege('DISTRIBUTOR', 'DETAIL_DISTRIBUTOR', tab.enum)));
  }

  initPageInfo() {
    const id = this.detailHeaderSubject.value.distributor_detail_id ?? this.detailHeaderSubject.value.distributor_id;
    this.links = [
      {
        title: 'Distributor',
        path: '',
        isActive: false,
      },
      this.pageInfoService.BreadcrumbSeparator,
      {
        title: 'Teregistrasi',
        path: '',
        isActive: false,
      },
      this.pageInfoService.BreadcrumbSeparator,
      {
        title: this.utils.toCapitalize(this.detailHeaderSubject.value.business_name),
        path: `distributor/detail/teregistrasi/active/${id}`,
        isActive: false,
      },
      this.pageInfoService.BreadcrumbSeparator,
      {
        title: 'Log Detail',
        path: '',
        isActive: true,
      },
    ];

    this.pageInfoService.updateTitle('Detail Log', undefined, false);
    this.pageInfoService.updateBreadcrumbs(this.links);
  }

  handleTabChange = (e: number) => (this.tabIndex = e);

  showNoteViewUpdateData() {
    if (this.IsRejectNonActive) {
      return false;
    } else return !this.IsNonActive;
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
  protected readonly TabListLabelDetailDistributor = TabListLabelDetailDistributor;
  protected readonly TabLabelEnumDetailDistributor = TabLabelEnumDetailDistributor;
  protected readonly DistributorStatusEnum = DistributorStatusEnum;
  protected readonly Object = Object;
}
