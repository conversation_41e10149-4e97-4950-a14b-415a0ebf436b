<app-note-view
  [color]="'info'"
  [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION"
  [classNoteView]="'my-7'"
  [text]="
    'Data yang ditampilkan merupakan data riwayat pengajuan perubahan data pada ' +
    utils.formatEpochToDate(detailHeaderSubject.value.submit_date, 'dd-MM-yyyy HH:mm:ss') +
    '. Perubahan data yang diajukan ditandai dengan warna biru.'
  "
/>

<mat-tab-group (selectedIndexChange)="handleTabChange($event)" [(selectedIndex)]="tabIndex" animationDuration="0ms" class="line mt-8" mat-stretch-tabs="false">
  <ng-container *ngFor="let tab of tabList; index as i">
    <mat-tab *ngIf="tab['privilege']" [label]="utils.mapKeyToString(TabListLabelDetailDistributor, tab['labelKey'])">
      <ng-container *ngIf="i === tabIndex">
        <ng-container [ngSwitch]="tab.enum">
          <ng-container *ngSwitchCase="TabLabelEnumDetailDistributor.TAB_DISTRIBUTOR_INFORMATION">
            <app-distributor-data [isViewOnly]="true" />
          </ng-container>
          <ng-container *ngSwitchCase="TabLabelEnumDetailDistributor.TAB_PLAFON_CREDIT">
            <app-credit-ceiling [isViewOnly]="true" />
          </ng-container>
          <ng-container *ngSwitchCase="TabLabelEnumDetailDistributor.TAB_ALAMAT_PENGIRIMAN">
            <app-shipping-address [isViewOnly]="true" />
          </ng-container>
          <ng-container *ngSwitchCase="TabLabelEnumDetailDistributor.TAB_DOCUMENTS">
            <app-document [isViewOnly]="true" />
          </ng-container>
        </ng-container>
      </ng-container>
    </mat-tab>
  </ng-container>
</mat-tab-group>
