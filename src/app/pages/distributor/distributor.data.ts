import { TableColumn } from '@shared/interface/table.interface';

import {
  EnumBranchInformation,
  EnumBusinessInformation,
  EnumDeliveryAddressInformation,
  EnumDetailPlafonKredit_CreditBilling,
  EnumDetailPlafonKredit_CreditCeiling,
  EnumDistributorDetail,
  EnumManagerInformation,
  EnumOwnerInformation,
  EnumSupplierInformation,
} from './enum/label-information.enum';
import { UtilitiesService } from '@services/utilities.service';
import { DistributorPlafonKreditCardType } from '@config/enum/distributor.enum';
import { ICardDetailBody } from '@models/card.model';
import { ITabList } from '@shared/interface/tablist.interface';
import { IGenericDetailUpdateData, IGenericPositionMap, IGenericUpdateData } from '@shared/interface/generic';
import { EnumDataStatus } from './enum/data-status.enum';
import {
  IDetailInformasi_BranchInformation,
  IDetailInformasi_BusinessInformation,
  IDetailInformasi_distributor_detail,
  IDetailInformasi_ManagerInformation,
  IDetailInformasi_OwnerInformation,
  IDetailInformasi_PlafonKredit,
} from './interfaces/detail-informasi.interface';
import { DeliveryAddressList } from './interfaces/detail-delivery.interface';
import { IDetailPlafonKredit_CreditBilling, IDetailPlafonKredit_SellingTarget } from './interfaces/detail-plafon-kredit.interface';
import { IListReject } from './interfaces/distributor.interface';

const Utils = new UtilitiesService();

export interface IGenericFullAddress {
  full_address?: string;
  sub_district?: string;
  district?: string;
  regency?: string;
  province?: string;
  // some data use this:
  sub_district_name?: string;
  district_name?: string;
  regency_name?: string;
  province_name?: string;
}

export interface IGenericBusinessInformation {
  distributor_item_types: string[] | string;
  date_established: string;
  space_large: string | number;
  employee_qty: string | number;
  turnover_per_year: string | number;
}

export const ListRejectEnum: IListReject[] = [
  {
    enum_string: 'Informasi tidak sesuai',
    enum_value: 'INFORMATION_NOT_MATCH',
  },
  {
    enum_string: 'Dokumen yang diupload tidak sesuai',
    enum_value: 'DOCUMENT_NOT_MATCH',
  },
  {
    enum_string: 'Dokumen tidak jelas/blur',
    enum_value: 'DOCUMENT_BLUR',
  },
  {
    enum_string: 'Alasan lainnya',
    enum_value: 'OTHER',
  },
];

export const TabListPendingRequest: ITabList[] = [
  {
    tab: 'informasi_distributor',
    enum: 'TAB_DISTRIBUTOR_INFORMATION',
    labelKey: 'informasi_distributor',
    privilege: null,
  },
  {
    tab: 'plafon_kredit',
    enum: 'TAB_PLAFON_CREDIT',
    labelKey: 'plafon_kredit',
    privilege: null,
  },
  {
    tab: 'alamat_pengiriman',
    enum: 'TAB_ALAMAT_PENGIRIMAN',
    labelKey: 'alamat_pengiriman',
    privilege: null,
  },
  {
    tab: 'dokumen',
    enum: 'TAB_DOCUMENTS',
    labelKey: 'dokumen',
    privilege: null,
  },
];

export function getDistributorTableColumns(status: string = '') {
  const _columns = <TableColumn[]>[
    {
      key: status === 'reject' ? 'code' : 'distributor_code',
      title: 'ID Distributor',
      isSortable: false,
    },
    {
      key: status === 'reject' ? 'name' : 'distributor_name',
      title: 'Nama Distributor',
      isSortable: false,
    },
    {
      key: status === 'reject' ? 'type' : 'distributor_type_title',
      title: 'Tipe Distributor',
    },
    {
      key: status === 'reject' ? 'manager_name' : 'regional_head',
      title: 'REGIONAL HEAD',
      isSortable: false,
    },
    {
      key: 'area_name',
      title: 'Area',
      isSortable: false,
    },
    {
      key: 'status_title',
      title: 'Status',
      isSortable: false,
    },
  ];

  if (status === 'pending') {
    _columns.push({
      key: 'submit_by',
      title: 'Diajukan Oleh',
      isSortable: false,
    });
  }

  _columns.push({
    key: 'createdDate',
    title: status === 'reject' ? 'Tanggal Submit' : 'Tanggal Pengajuan',
    isSortable: true,
    dataKey: status === 'reject' ? 'created_date' : 'approval_date',
  });

  return _columns;
}

export const mapDetailDistributor = async (data: IDetailInformasi_distributor_detail) => {
  data.full_address = mapGenericFullAddress<IDetailInformasi_distributor_detail>(data);

  return (Object.keys(EnumDistributorDetail) as (keyof typeof EnumDistributorDetail)[]).map((key) => {
    return {
      label: EnumDistributorDetail[key],
      key: key === 'full_address' ? ['full_address', 'sub_district', 'district', 'regency', 'province'] : key,
      value: data[key] !== '' ? data[key] : '-',
    };
  });
};

export const mapDistributorOwner = async (data: IDetailInformasi_OwnerInformation) => {
  data.full_address = `${data.full_address}, ${data.sub_district}, ${data.district}, ${data.regency}, ${data.province}`;

  return (Object.keys(EnumOwnerInformation) as (keyof typeof EnumOwnerInformation)[]).map((key) => {
    return {
      label: EnumOwnerInformation[key],
      key: key === 'full_address' ? ['full_address', 'sub_district', 'district', 'regency', 'province'] : key,
      value: data[key] !== '' ? data[key] : '-',
    };
  });
};

export const mapDistributorManager = async (data: IDetailInformasi_ManagerInformation) => {
  data.full_address = mapGenericFullAddress<IDetailInformasi_ManagerInformation>(data);

  return (Object.keys(EnumManagerInformation) as (keyof typeof EnumManagerInformation)[]).map((key) => {
    return {
      label: EnumManagerInformation[key],
      key: key === 'full_address' ? ['full_address', 'sub_district', 'district', 'regency', 'province'] : key,
      value: data[key] !== '' ? data[key] : '-',
    };
  });
};

export const mapDistributorBusinessInformation = async (data: IDetailInformasi_BusinessInformation) => {
  return (Object.keys(EnumBusinessInformation) as (keyof typeof EnumBusinessInformation)[]).map((key) => {
    return {
      label: EnumBusinessInformation[key],
      key,
      value: renderGenericBusinessInformation<IDetailInformasi_BusinessInformation>(data, key),
    };
  });
};

function isObjectOfBranchInformation(data: IDetailInformasi_BranchInformation) {
  return Object.keys(data).length < 3 && 'full_address' in data && 'name' in data;
}

function isObjectOfDeliveryInformation(data: DeliveryAddressList) {
  return Object.keys(data).length > 3 && 'id' in data && 'longitude' in data && 'latitude' in data;
}

export async function mapDetailUpdateData<T>(data: IGenericUpdateData<T>[]) {
  return data.map((item, index) => {
    const { status, old_data, new_data } = item as IGenericUpdateData<ICardDetailBody>;

    let _isBranchInformation = false;
    let _isDeliveryInformation = false;

    const isStatusADD = status === EnumDataStatus.ADD;
    const isStatusUPDATED = status === EnumDataStatus.UPDATED;

    // status === EnumDataStatus.ADD use new_data else old_data
    const _typeToCheck = isStatusADD ? new_data : old_data;

    _isBranchInformation = isObjectOfBranchInformation(_typeToCheck as IDetailInformasi_BranchInformation);
    _isDeliveryInformation = isObjectOfDeliveryInformation(_typeToCheck as DeliveryAddressList);

    let _finalResult = {} as IGenericDetailUpdateData<ICardDetailBody>;
    let _itemMapPos: IGenericPositionMap = { lat: 0, lng: 0 };

    function generateFinalTitle(_seq: number) {
      const _title = _isBranchInformation ? 'Usaha' : _isDeliveryInformation ? 'Alamat Pengiriman' : 'Supplier';
      return `${_title} ${_seq}`;
    }

    // which data to use? old_data / new_data
    const _dataToMap = isStatusADD ? new_data : old_data;
    const _asSequence = _isBranchInformation || _isDeliveryInformation ? index + 1 : (_dataToMap['sequence' as keyof typeof _dataToMap] as number);
    const _enumData = _isBranchInformation ? EnumBranchInformation : _isDeliveryInformation ? EnumDeliveryAddressInformation : EnumSupplierInformation;

    const _result = generateMappedResult(_dataToMap, _enumData);
    _finalResult.title = generateFinalTitle(_asSequence);

    _finalResult = {
      ..._finalResult,
      data: _result,
      status: status,
    };

    // FOR MAP PIN POINT
    if (_isDeliveryInformation) {
      _itemMapPos = generateMapPos(_dataToMap); // status ADD will use new_data
      const _isMapUpdated = checkMapPosUpdated(item);

      // if new_data != old_data && new_data value is null get map pos from old_data
      if (isStatusUPDATED && _isMapUpdated) {
        if (!new_data['latitude' as keyof typeof new_data] && !new_data['longitude' as keyof typeof new_data]) {
          new_data['latitude' as keyof typeof new_data] = old_data['latitude' as keyof typeof old_data];
          new_data['longitude' as keyof typeof new_data] = old_data['longitude' as keyof typeof old_data];
        }

        _itemMapPos = generateMapPos(new_data);
        _finalResult = mapDeliveryAddressEditedValue(_finalResult, new_data);
      }

      _finalResult.map = _itemMapPos;
    }

    return _finalResult;
  });
}

function generateMappedResult<T>(_data: T, _enumData: typeof EnumBranchInformation | typeof EnumSupplierInformation | typeof EnumDeliveryAddressInformation) {
  let _result: ICardDetailBody = { label: '', key: '', value: '' };

  return (Object.keys(_enumData) as (keyof typeof _enumData)[]).map((key) => {
    _result = {
      label: _enumData[key],
      key,
      value: generateResult(_data, key),
    };

    return _result;
  });
}

function checkMapPosUpdated<ICardDetailBody>(data: IGenericUpdateData<ICardDetailBody>) {
  const { old_data, new_data } = data;
  const _oldDataMapPos = generateMapPos(old_data);
  const _newDataMapPos = generateMapPos(new_data);
  return !Utils.isArrayObjIdentical([_oldDataMapPos], [_newDataMapPos]);
}

function generateMapPos<T>(data: T) {
  const _map: IGenericPositionMap = { lat: 0, lng: 0 };
  if (!data) {
    return _map;
  }

  const _lat = 'latitude' as keyof typeof data;
  const _lng = 'longitude' as keyof typeof data;

  _map.lat = data[_lat] as number;
  _map.lng = data[_lng] as number;

  return _map;
}

function generateResult<T>(_data: T, _key: string) {
  const _k = _key as keyof typeof _data;
  const _result = '-';

  const _isBranchInformation = isObjectOfBranchInformation(_data as IDetailInformasi_BranchInformation);
  const _isDeliveryInformation = isObjectOfDeliveryInformation(_data as DeliveryAddressList);

  if (_isBranchInformation || _isDeliveryInformation) {
    return _data[_k] ? (_data[_k] as string) : _result;
  }

  // for supplier information data
  const _keyOfSellingItemList = 'selling_item_list' as keyof typeof _data;
  const _dataSellingItemList = _data[_keyOfSellingItemList] as T[];
  const _sellingItemList = Utils.arrayStringJoin(_dataSellingItemList, 'name', ', ');

  return _k === _keyOfSellingItemList ? _sellingItemList : _data[_k] && _data[_k] !== '' ? (_data[_k] as string) : _result;
}

function mapDeliveryAddressEditedValue<T>(result: IGenericDetailUpdateData<ICardDetailBody>, newData: T) {
  const { data } = result;
  result.data = Utils.generateUpdateValue(data, newData as ICardDetailBody[]);
  return result;
}

export async function mapDetailPlafonKredit(data: IDetailInformasi_PlafonKredit, card: 'DETAIL_PLAFON' | 'DETAIL_TARGET_PENJUALAN' | 'DETAIL_PENAGIHAN') {
  if (!card) {
    return [];
  }

  const { credit_ceiling, credit_billing, selling_target } = data;

  if (card === DistributorPlafonKreditCardType.DETAIL_PLAFON) {
    const { assurance, assurance_type, credit_limit } = credit_ceiling;

    credit_ceiling.credit_limit = +Utils.toRupiah(credit_limit);
    credit_ceiling.assurance = assurance && assurance_type ? `${assurance}, ${assurance_type}` : assurance;

    return (Object.keys(EnumDetailPlafonKredit_CreditCeiling) as (keyof typeof EnumDetailPlafonKredit_CreditCeiling)[]).map((key) => {
      let _val = credit_ceiling[key];

      if (key === 'credit_limit') {
        _val = Utils.toRupiah(credit_limit);
      }

      return {
        label: EnumDetailPlafonKredit_CreditCeiling[key],
        key: key === 'assurance' && assurance_type !== '' ? ['assurance', 'assurance_type'] : key,
        value: _val ? _val : '-',
      };
    });
  }

  if (card === DistributorPlafonKreditCardType.DETAIL_PENAGIHAN) {
    return mapCreditBilling(credit_billing);
  }

  if (card === DistributorPlafonKreditCardType.DETAIL_TARGET_PENJUALAN) {
    return mapSellingTarget(selling_target);
  }
}

export const mapCreditBilling = (_data: IDetailPlafonKredit_CreditBilling) => {
  const { billing_to_full_address, billing_to_sub_district, billing_to_district, billing_to_regency, billing_to_province } = _data;
  _data.billing_to_full_address = `${billing_to_full_address}, ${billing_to_sub_district}, ${billing_to_district}, ${billing_to_regency}, ${billing_to_province}`;

  let _result: ICardDetailBody = {};
  return (Object.keys(EnumDetailPlafonKredit_CreditBilling) as (keyof typeof EnumDetailPlafonKredit_CreditBilling)[]).map((key) => {
    if (key === 'title_separator') {
      _result = {
        title: EnumDetailPlafonKredit_CreditBilling[key],
        label: '',
        key,
        value: '',
      };
    } else {
      _result = {
        label: EnumDetailPlafonKredit_CreditBilling[key],
        key: key === 'billing_to_full_address' ? ['billing_to_full_address', 'billing_to_sub_district', 'billing_to_district', 'billing_to_regency', 'billing_to_province'] : key,
        value: _data[key] ? _data[key] : '-',
      };
    }
    return _result;
  });
};

export const mapSellingTarget = (_data: IDetailPlafonKredit_SellingTarget, _new = false): ICardDetailBody[] => {
  const { first_year_target, second_year_target, third_year_target, fourth_year_target, fifth_year_target, market_share } = _data;

  if (_new) {
    Object.keys(_data).forEach((key) => {
      const k = key as keyof typeof _data;
      _data[k] = k === 'market_share' ? Utils.toThousandConvert(+_data[k]) + '%' : Utils.toRupiah(+_data[k]) ?? 0;
    });
  }

  return [
    {
      label: 'Tahun Ke-1',
      key: 'first_year_target',
      value: Utils.toRupiah(+first_year_target) ?? 0,
    },
    {
      label: 'Tahun Ke-2',
      key: 'second_year_target',
      value: Utils.toRupiah(+second_year_target) ?? 0,
    },
    {
      label: 'Tahun Ke-3',
      key: 'third_year_target',
      value: Utils.toRupiah(+third_year_target) ?? 0,
    },
    {
      label: 'Tahun Ke-4',
      key: 'fourth_year_target',
      value: Utils.toRupiah(+fourth_year_target) ?? 0,
    },
    {
      label: 'Tahun Ke-5',
      key: 'fifth_year_target',
      value: Utils.toRupiah(+fifth_year_target) ?? 0,
    },
    {
      label: 'Persentase Pangsa Pasar',
      key: 'market_share',
      value: Utils.toThousandConvert(+market_share) + '%',
    },
  ];
};

export function mapGenericFullAddress<T extends IGenericFullAddress>(data: T): string {
  if (typeof data != 'object') {
    return '';
  }

  const _subDistrict = data.sub_district ? data.sub_district : data.sub_district_name;
  const _district = data.district ? data.district : data.district_name;
  const _regency = data.regency ? data.regency : data.regency_name;
  const _province = data.province ? data.province : data.province_name;

  return `${data.full_address}, ${_subDistrict}, ${_district}, ${_regency}, ${_province}`;
}

export function renderGenericBusinessInformation<T extends IGenericBusinessInformation>(data: T, key: string): string {
  const { distributor_item_types } = data;
  const _distributorItemTypes = typeof distributor_item_types !== 'string' && distributor_item_types.length > 0 ? Utils.arrayStringJoin(distributor_item_types, 'name', ',') : '-';

  let _val = data[key as keyof typeof data] as string;

  switch (key) {
    case 'date_established':
      _val = `${Utils.timeStampToDate(data[key], 'dd MMM Y')}`;
      break;
    case 'distributor_item_types':
      _val = _distributorItemTypes;
      break;
    case 'space_large':
      _val = `${Utils.toThousandConvert(data[key])} m2`;
      break;
    case 'employee_qty':
      _val = `${Utils.toThousandConvert(data[key])}`;
      break;
    case 'turnover_per_year':
      _val = `${Utils.toRupiah(data[key] as number)}`;
      break;
  }

  return _val;
}

export function stringIsIdentical(_oldVal: string, _newVal: string) {
  return _oldVal === _newVal;
}

export function checkHasNewValue(data: ICardDetailBody[]) {
  if (!data) {
    return false;
  }

  const _hasPropNew = data.filter((item) => item.new).length;
  return _hasPropNew > 0;
}
