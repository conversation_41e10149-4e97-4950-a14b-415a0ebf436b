import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { IListboxRegion } from '@shared/components/form/input-listbox-region/input-listbox-region.interface';
import { ModalFormComponent } from '@shared/components/modal/modal-form/modal-form.component';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { StaticText } from '../../product-exclusive-settings/product-exclusive.data';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { BehaviorSubject, Subscription } from 'rxjs';
import { DistributorService } from '../../distributor.service';
import { ActivatedRoute } from '@angular/router';
import { FormControl, FormControlName, FormGroup, FormGroupDirective } from '@angular/forms';

@Component({
  selector: 'app-input-listbox-cakupan-subarea',
  templateUrl: './input-listbox-cakupan-subarea.component.html',
  styleUrls: ['./input-listbox-cakupan-subarea.component.scss'],
})
export class InputListboxCakupanSubareaComponent implements OnInit, OnDestroy {
  @Input() dialogTitle = 'Sub Area';
  @Input() chipsListData: IListboxRegion[] = [];
  @Output() selectionOutput = new EventEmitter<IListboxRegion[]>();

  listItemsBank = <IListboxRegion[]>[];
  listSelectedItems = <IListboxRegion[]>[];

  // config:
  label = 'Sub Area';
  ctaLabelAdd = 'Tambahkan';
  ctaLabelOpenDialog = 'Pilih Sub Area';
  required = true;

  loading = new BehaviorSubject(false);

  @ViewChild('modalForm') modalFormComponent: ModalFormComponent;
  modalFormConfig: ModalConfig = {
    modalTitle: this.dialogTitle.toUpperCase(),
    onClose: () => this.handleOnClose(),
  };

  valueFormGroup: FormGroup;
  valueFormControl: FormControl;

  private unsubscribe: Subscription[] = [];

  constructor(
    private distributorService: DistributorService,
    private activatedRoute: ActivatedRoute,
    private formGroupDirective: FormGroupDirective,
    private formControlNameDirective: FormControlName
  ) {}

  ngOnInit(): void {
    this.valueFormGroup = this.formGroupDirective.form;
    this.valueFormControl = this.formGroupDirective.getControl(this.formControlNameDirective);

    if (this.valueFormControl.value.length) {
      this.listSelectedItems = this.valueFormControl.value;
      this.toggleShowSelectedList();
    }
  }

  handleOpenDialog() {
    this.getListBoxData();
    return this.modalFormComponent.open();
  }

  getListBoxData() {
    if (this.listItemsBank.length) return true;

    this.loading.next(true);
    const _dataSubs = this.getData().subscribe((resp) => {
      if (!resp) return;

      const { data } = resp;
      data.map((item) => (item.selected = false));

      this.listItemsBank = data;
      this.mapSelectedItems(this.listItemsBank);

      this.loading.next(false);
    });

    this.unsubscribe.push(_dataSubs);
  }

  getData() {
    const { id } = this.activatedRoute.snapshot.queryParams;
    return this.distributorService.getDistributorSubArea(id);
  }

  mapSelectedItems(listItems: IListboxRegion[]) {
    if (!this.chipsListData.length) return;

    this.listItemsBank = listItems.map((item) => {
      this.chipsListData.forEach((chip) => {
        if (chip.id === item.id) {
          item.selected = true;
        }
      });

      return item;
    });
  }

  handleChangeSelection() {
    this.listSelectedItems = this.listItemsBank.filter((item) => item.selected);
    this.selectionOutput.emit(this.listSelectedItems);
  }

  handleAddChips() {
    this.setFormControlValue();
    return this.modalFormComponent.close().then(() => this.toggleShowSelectedList());
  }

  handleOutputChips(items: IListboxRegion[]) {
    this.listSelectedItems = items;
    this.selectionOutput.emit(this.listSelectedItems);
    this.setFormControlValue();
    this.handleRemovedChips();
  }

  handleOnClose() {
    if (this.isAllListSelected()) {
      this.resetSelectedItems();
      this.handleChangeSelection();
    }
    return true;
  }

  resetSelectedItems() {
    this.listSelectedItems = [];
    this.listItemsBank.map((item) => (item.selected = false));
  }

  handleRemovedChips() {
    const _findRemovedChips = this.chipsListData.filter((chips) => !chips.selected);
    this.listItemsBank.map((item) => {
      _findRemovedChips.map((chip) => {
        if (item.id === chip.id) {
          item.selected = chip.selected;
        }
      });
    });

    this.toggleShowSelectedList();
  }

  isAllListSelected = () => this.listSelectedItems.length > 0 && this.listSelectedItems.length === this.listItemsBank.length;

  setFormControlValue = () => this.valueFormControl.setValue(this.listSelectedItems);

  toggleShowSelectedList = () => (this.chipsListData = this.listSelectedItems);

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  protected readonly StaticText = StaticText;
  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
