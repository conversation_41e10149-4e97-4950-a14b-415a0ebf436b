<ng-container *ngIf="(isLoading | async) === false; else loadingTpl">
  <app-card [cardBodyClasses]="'pt-0'" [cardClasses]="renderClassCard() + ' mb-8 animation animation-fade-in'" [header]="true">
    <ng-container cardHeader>
      <h5 class="fw-bolder d-flex w-100 align-items-center mb-0">
        <span [class]="IsSectionDeleted ? 'text-danger' : ''">{{ title + (IsSectionDeleted ? ' (Dihapus)' : '') }}</span>
        <app-section-verified-badge
          *ngIf="showBadge()"
          [isNotHaveRevision]="isNotHaveRevision"
          [isRequestRevision]="isSectionRequestRevision()"
          [isSectionDeleted]="IsSectionDeleted"
          [isViewOnly]="isViewOnly"
          [sectionVerificationStatus]="getVerificationStatus()"
        />
      </h5>
    </ng-container>
    <ng-container cardBody>
      <ng-container *ngTemplateOutlet="viewTemplate"></ng-container>

      <!-- custom cta modal trigger -->
      <div *ngIf="customModalButton.enable" class="position-absolute top-0 end-0 p-8">
        <button (click)="customModalButtonTrigger.emit(true)" class="btn btn-transparent p-0" type="button">
          <span class="text-info-text fw-bolder text-decoration-underline">{{ customModalButton.text }}</span>
        </button>
      </div>

      <!-- revision note -->
      <app-input-revision-note #inputRevision (noteChanged)="onNoteChanged($event)" *ngIf="isSectionRequestRevision()" />

      <!--
        CTA: verify, request revision, reset verification
        show only on status submitted - for admin/finance
      -->
      <app-button-group-verify
        (ctaVerification)="handleActionVerification($event)"
        *ngIf="showBtnGroupVerify()"
        [sectionStatusVerification]="getVerificationStatus()"
        [showBtnRequestRevision]="showBtnGroup()"
        [showBtnVerify]="showBtnGroup()"
      />
    </ng-container>
  </app-card>
</ng-container>

<ng-template #defaultTpl>
  <div *ngFor="let item of bodyData" class="row">
    <ng-container *ngIf="item.key !== 'map'; else mapTpl">
      <ng-container [ngTemplateOutletContext]="{ $implicit: item }" [ngTemplateOutlet]="bodyDataMap" />
    </ng-container>

    <ng-template #mapTpl>
      <ng-container *ngIf="item.map_revision; else mapOnceTpl">
        <div class="my-2">
          <span class="text-gray-600">Pintpoint Lama</span>
          <ng-container [ngTemplateOutletContext]="{ $implicit: item.map }" [ngTemplateOutlet]="defaultMapTpl" />
        </div>
        <div class="my-2">
          <span class="fw-bold text-info">Pintpoint Baru</span>
          <ng-container [ngTemplateOutletContext]="{ $implicit: item.map_revision }" [ngTemplateOutlet]="defaultMapTpl" />
        </div>
      </ng-container>

      <ng-template #mapOnceTpl>
        <ng-container [ngTemplateOutletContext]="{ $implicit: item.map }" [ngTemplateOutlet]="defaultMapTpl" />
      </ng-template>
    </ng-template>
  </div>
</ng-template>

<ng-template #ktpTpl>
  <div class="row">
    <div class="col-12 col-lg-8">
      <div *ngFor="let item of bodyData" class="row">
        <ng-container *ngIf="item.type !== EnumTypeBodyCardVerificationForm.IMG_URL">
          <ng-container [ngTemplateOutletContext]="{ $implicit: item }" [ngTemplateOutlet]="bodyDataMap" />
        </ng-container>
      </div>
    </div>
    <div class="col-12 col-lg-4 border-start border-gray-300">
      <ng-container *ngIf="getDocumentUrl(bodyData)?.revision_value; else onceKtpTpl">
        <app-card [cardBodyClasses]="'border-bottom-document'" [cardClasses]="''" [cardHeaderTitle]="getDocumentUrl(bodyData)?.label + ' (Lama)' ?? ''" [titleInBody]="true">
          <ng-container cardBody>
            <div [ngClass]="{ 'd-flex justify-content-center': (isLoading | async) }" class="card-document">
              <app-image-preview [height]="200" [images]="[getDocumentUrl(bodyData)?.value ?? '']" [openTextLabel]="false" [titleModal]="'KTP Pemilik Usaha'"></app-image-preview>
            </div>
            <ng-container *ngIf="!isViewOnly" [ngTemplateOutlet]="supportingTextDocument"></ng-container>
          </ng-container>
        </app-card>

        <app-card [cardClasses]="''" [cardHeaderTitle]="getDocumentUrl(bodyData)?.label + ' (Baru)' ?? ''" [titleInBody]="true">
          <ng-container cardBody>
            <div [ngClass]="{ 'd-flex justify-content-center': (isLoading | async) }" class="card-document">
              <app-image-preview
                [height]="200"
                [images]="[getDocumentUrl(bodyData)?.revision_value ?? '']"
                [openTextLabel]="false"
                [titleModal]="'KTP Pemilik Usaha'"
              ></app-image-preview>
            </div>
            <ng-container *ngIf="!isViewOnly" [ngTemplateOutlet]="supportingTextDocument"></ng-container>
          </ng-container>
        </app-card>
      </ng-container>
      <ng-template #onceKtpTpl>
        <app-card [cardClasses]="''" [cardHeaderTitle]="getDocumentUrl(bodyData)?.label ?? ''" [titleInBody]="true" *ngIf="getDocumentUrl(bodyData)?.value">
          <ng-container cardBody>
            <div [ngClass]="{ 'd-flex justify-content-center': (isLoading | async) }" class="card-document">
              <app-image-preview [height]="200" [images]="[getDocumentUrl(bodyData)?.value ?? '']" [openTextLabel]="false" [titleModal]="'KTP Pemilik Usaha'"></app-image-preview>
            </div>
            <div *ngIf="!isViewOnly" class="my-4 text-gray-600">Dokumen digunakan sebagai pembanding, untuk verifikasi dokumen dilakukan pada step dokumen.</div>
          </ng-container>
        </app-card>
      </ng-template>
    </div>
  </div>
</ng-template>

<ng-template #groupTpl>
  <div *ngFor="let branch of bodyDataBranch" class="row">
    <ng-container *ngIf="branch.data && branch.data.length > 0">
      <div class="col-12 my-2">
        <label class="fw-bold mb-4">{{ branch.label }}</label>
        <div *ngFor="let item of branch.data" class="row">
          <ng-container [ngTemplateOutletContext]="{ $implicit: item }" [ngTemplateOutlet]="bodyDataMap" />
        </div>
      </div>
    </ng-container>
  </div>
</ng-template>

<ng-template #modalMapTpl>
  <div *ngFor="let item of bodyData">
    <div class="row">
      <ng-container [ngTemplateOutletContext]="{ $implicit: item }" [ngTemplateOutlet]="bodyDataMap" />
    </div>

    <ng-container *ngIf="item.map">
      <div class="mt-5 d-flex justify-content-end position-absolute top-0 end-0 p-6">
        <button (click)="modalPinpoint.open()" class="btn btn-transparent">
          <span class="text-info fw-bolder text-decoration-underline">Lihat pinpoint</span>
        </button>
      </div>
      <app-modal #modalPinpoint [modalClass]="'modal-map'" [modalConfig]="modalPinpointConfig" [modalOptions]="modalPinpointOptions" [modalTitle]="title ?? ''">
        <div class="map-container">
          <google-map
            (mapInitialized)="handleMapInitialized($event, item.map)"
            [center]="{ lat: item.map.lat, lng: item.map.long }"
            [options]="mapOptions"
            [zoom]="16"
            width="100%"
          >
            <map-marker [options]="mapMarkerOptions" [position]="{ lat: item.map.lat, lng: item.map.long }" />
          </google-map>
        </div>
      </app-modal>
    </ng-container>
  </div>
</ng-template>

<ng-template #documentTpl>
  <ng-container *ngTemplateOutlet="noteViewDocument" />
  <div class="row">
    <ng-container *ngFor="let item of bodyData">
      <div class="col-12 col-lg-6 my-2">
        <div class="row mb-4">
          <div class="col-12 col-lg-6">
            <span class="text-gray-700">{{ item.label }} :</span>
          </div>
          <div class="col-12 col-lg-6 card-document">
            <app-image-preview [height]="200" [images]="[!!item.revision_value ? item.revision_value : item.value ?? '']" [openTextLabel]="false" [titleModal]="item.label" />
          </div>
        </div>
      </div>
    </ng-container>
  </div>
</ng-template>

<ng-template #bodyDataMap let-item>
  <div *ngIf="item.title" class="col-12 my-2 fw-bold">{{ item.title }}</div>
  <ng-container *ngIf="!item.title">
    <div *ngIf="item.label" class="col-12 col-lg-3 my-2">
      <label class="text-gray-600">{{ item.label }}</label>
    </div>

    <div *ngIf="!item.map" class="col-12 col-lg-9 my-2">
      <div *ngIf="!item.type" class="flex-column">
        <div class="text-gray-800">: {{ !!item.value ? item.value : '-' }}</div>
        <div *ngIf="item.revision_value" class="text-info">: {{ item.revision_value }}</div>
      </div>
      <div *ngIf="item.type && item.type === EnumTypeBodyCardVerificationForm.DATE_FORMAT" class="flex-column">
        <div class="text-gray-800">: {{ utilities.formatEpochToDate(item.value, 'dd MMM yyyy') }}</div>
        <div *ngIf="item.revision_value" class="text-info">: {{ utilities.formatEpochToDate(item.revision_value) }}</div>
      </div>
      <div *ngIf="item.type && item.type === EnumTypeBodyCardVerificationForm.DATE" class="flex-column">
        <div class="text-gray-800">: {{ utilities.formatEpochToDate(item.value) }}</div>
        <div *ngIf="item.revision_value" class="text-info">: {{ utilities.formatEpochToDate(item.revision_value) }}</div>
      </div>
      <div *ngIf="item.type && item.type === EnumTypeBodyCardVerificationForm.CURRENCY" class="flex-column">
        <div class="text-gray-800">: {{ utilities.toRupiah(item.value) ?? '-' }}</div>
        <div *ngIf="item.revision_value" class="text-info">: {{ utilities.toRupiah(item.revision_value) }}</div>
      </div>
    </div>
  </ng-container>
</ng-template>

<ng-template #defaultMapTpl let-map>
  <div *ngIf="map" class="map-container position-relative mt-8">
    <google-map [center]="{ lat: map.lat, lng: map.long }" [height]="'200px'" [options]="mapOptions" [width]="'100%'">
      <map-marker [options]="mapMarkerOptions" [position]="{ lat: map.lat, lng: map.long }" />
      <button (click)="viewMap({ lat: map.lat, lng: map.long })" class="btn btn-sm bg-white position-absolute top-0 end-0 mt-4 me-4" color="primary" mat-button type="button">
        Lihat Peta
      </button>
    </google-map>
    <div class="px-6 py-6">
      <h4 class="mb-2">{{ map.label }}</h4>
      <span>{{ map.description }}</span>
    </div>
  </div>
</ng-template>

<ng-template #updateDocumentTpl>
  <ng-container *ngTemplateOutlet="noteViewDocument" />
  <ng-container *ngIf="bodyDataUpdateDocument.document_changes.length; else noDocumentTPL">
    <div *ngFor="let doc of bodyDataUpdateDocument.document_changes; let first = first" class="row mt-7">
      <div class="col-12 col-lg-3 mb-4">{{ doc.uploaded_date }} :</div>
      <div class="col-12 col-lg-9">
        <div class="row">
          <div *ngFor="let url of doc.url" class="col-12 col-sm-6 col-lg-4 mb-4">
            <app-image-preview [height]="200" [images]="[url]" [openTextLabel]="false" [titleModal]="doc.uploaded_date"></app-image-preview>
          </div>
        </div>
      </div>
    </div>
  </ng-container>

  <ng-template #noDocumentTPL>
    <label class="text-gray-400">Dokumen Pembaruan Data tidak tersedia.</label>
  </ng-template>
</ng-template>

<ng-template #asideDocumentTpl>
  <div class="row">
    <div class="col-12 col-lg-8 border-right" [class.border-right]="getDocuments(bodyData).length > 0">
      <div *ngFor="let item of bodyData" class="row">
        <ng-container *ngIf="item.type !== EnumTypeBodyCardVerificationForm.IMG_URL">
          <ng-container [ngTemplateOutletContext]="{ $implicit: item }" [ngTemplateOutlet]="bodyDataMap" />
        </ng-container>
      </div>
    </div>
    <div class="col-12 col-lg-4">
      <ng-container *ngFor="let doc of getDocuments(bodyData)">
        <div *ngIf="doc.value">
          <app-card [cardHeaderTitle]="doc.label" [titleInBody]="true">
            <ng-container cardBody>
              <div [ngClass]="{ 'd-flex justify-content-center': (isLoading | async) }" class="card-document">
                <app-image-preview [height]="200" [images]="[doc.value ?? '']" [openTextLabel]="false" [titleModal]="doc.label" />
              </div>
            </ng-container>
          </app-card>
          <ng-container [ngTemplateOutlet]="supportingTextDocument"></ng-container>
        </div>
      </ng-container>
    </div>
  </div>
</ng-template>

<ng-template #loadingTpl>
  <app-card [cardClasses]="'mb-7'">
    <ng-container cardBody>
      <app-skeleton-text [height]="15" [width]="250"></app-skeleton-text>
      <div class="my-7">
        <app-skeleton-text [height]="15" [width]="200"></app-skeleton-text>
        <app-skeleton-text [height]="15" [width]="250"></app-skeleton-text>
        <app-skeleton-text [height]="15" [width]="300"></app-skeleton-text>
      </div>
      <div class="d-flex">
        <div class="me-7">
          <app-skeleton-text [height]="25" [width]="100"></app-skeleton-text>
        </div>
        <div class="mx-7">
          <app-skeleton-text [height]="25" [width]="100"></app-skeleton-text>
        </div>
      </div>
    </ng-container>
  </app-card>
</ng-template>

<ng-template #noteViewDocument>
  <app-note-view *ngIf="!isViewOnly" [color]="'info'" [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION" [text]="'Pastikan dokumen yang telah diupload sesuai, jelas dan tidak blur.'" />
</ng-template>

<ng-template #supportingTextDocument>
  <div class="my-4 text-gray-600">Dokumen digunakan sebagai pembanding, untuk verifikasi dokumen dilakukan pada step dokumen.</div>
</ng-template>
