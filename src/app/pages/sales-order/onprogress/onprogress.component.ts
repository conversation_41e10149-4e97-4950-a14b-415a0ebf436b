import { Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { MatSort, Sort } from '@angular/material/sort';
import { ActivatedRoute, Router } from '@angular/router';
import { PageInfoService, PageLink } from '@metronic/layout';
import { BaseTableService } from '@shared/base/base-table-service.service';
import { IListSO } from '@models/sales-order.model';
import { UrlUtilsService } from '@utils/url-utils.service';
import { UtilitiesService } from '@services/utilities.service';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { TableColumn } from '@shared/interface/table.interface';
import { BaseDatasource } from '@shared/base/base.datasource';
import { BehaviorSubject } from 'rxjs';
import { API } from '@config/constants/api.constant';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { FilterService } from '@services/filter.service';
import { getSalesOrderTableColumns } from '../sales-order.data';
import { SalesOrderLegacyService } from '../sales-order-legacy.service';

@Component({
  selector: 'app-onprogress',
  templateUrl: './onprogress.component.html',
  styleUrls: ['./onprogress.component.scss'],
})
export class OnprogressComponent implements OnInit {
  @ViewChild(MatSort, { static: false }) matSort: MatSort;
  @Output() totalCount = new EventEmitter();
  STRING_CONSTANTS = STRING_CONSTANTS;
  iconNone = STRING_CONSTANTS.ICON.IC_SALES_ORDER;
  isLoading = false;
  currentDate = new Date();
  // table
  baseTable: BaseTableService<IListSO> = this.base;
  salesOrderList: IListSO[];
  tableColumns: TableColumn[];
  displayedColumns: string[];
  baseDatasourcePending: BaseDatasource<IListSO>;
  string_filter?: string;
  checkActive: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  isOpenFilter: boolean = false;
  isActiveFilter: boolean = false;
  filterForm: FormGroup;
  privilegeDetailOnProgress: boolean;
  links: Array<PageLink> = [
    {
      title: 'Sales Order',
      path: '',
      isActive: false,
    },
    {
      title: '',
      path: '',
      isActive: false,
      isSeparator: true,
    },
    {
      title: 'Diproses',
      path: '',
      isActive: true,
    },
  ];

  constructor(
    private activeRoute: ActivatedRoute,
    private pageInfoService: PageInfoService,
    private router: Router,
    private base: BaseTableService<IListSO>,
    private paginationService: UrlUtilsService,
    public utilities: UtilitiesService,
    private formBuilder: FormBuilder,
    private rolePrivilegeService: RolePrivilegeService,
    private filterService: FilterService,
    public salesOrderService: SalesOrderLegacyService
  ) {}

  ngOnInit() {
    this.pageInfoService.updateTitle(`Sales Order Diproses`);
    this.privilegeDetailOnProgress = this.rolePrivilegeService.checkPrivilege('SALES_ORDER', 'LIST_SO_DIPROSES', 'CTA_VIEW_DETAIL');
    this.pageInfoService.updateBreadcrumbs(this.links);
    this.baseTable.responseDatabase.subscribe((response) => (this.baseDatasourcePending = response));
    this.setTableData();
    this.initFilter();
    this.queryHandler();
    this.initPageInfo();
  }

  initFilter() {
    this.filterForm = this.formBuilder.group({
      start_date: new FormControl({ value: '', disabled: true }),
      end_date: new FormControl({ value: '', disabled: true }),
    });
  }

  initPageInfo() {
    this.baseDatasourcePending.tableSubject.subscribe((data) => {
      if (data) {
        this.salesOrderList = data;
      }
    });
    this.updateCount();
  }

  updateCount() {
    this.baseDatasourcePending.totalItem$.subscribe((data) => {
      if (data) {
        this.pageInfoService.updateTitle(`Sales Order Diproses (` + data + `)`);
      } else {
        this.pageInfoService.updateTitle(`Sales Order Diproses (0)`);
      }
    });
  }

  setTableData() {
    this.tableColumns = getSalesOrderTableColumns();
    this.tableColumns = this.utilities.privilegeTableColumns(this.privilegeDetailOnProgress, this.tableColumns);
    this.displayedColumns = this.tableColumns.map((head) => head.key);
    this.baseDatasourcePending = new BaseDatasource();
    if (this.salesOrderList) {
      this.baseDatasourcePending.tableSubject.next(this.salesOrderList);
    }
  }

  sortTable(param: Sort) {
    const sortby = this.tableColumns.find((column) => column.key === param.active);
    param.active = sortby?.key ?? '';
    this.filterService.sortDataSource(param).then();
  }

  changePageEvent($event: BaseDatasource<any>) {
    this.filterService.changePageEvent($event, this.string_filter ?? '');
  }

  handleActionDetail(id: string) {
    return this.router.navigate([`/sales-order/onprogress/` + id]);
  }

  onSearch(event: string) {
    this.checkActive.next(!this.checkActive.value);
    this.filterService.onSearch(event);
    this.string_filter = '';
  }

  queryHandler() {
    this.activeRoute.queryParams.subscribe((data) => {
      this.string_filter = data.string_filter;
      this.filterForm.controls['start_date'].setValue(data.start_date);
      this.filterForm.controls['end_date'].setValue(data.end_date);
      this.isActiveFilter = !!(data.start_date || data.end_date);
      const param = this.paginationService.sliceQueryParams();
      if (param) {
        this.baseTable.loadDataTable(API.SALES_ORDER.LIST_SALES_ORDER_ONPROGRESS, param);
      } else {
        this.baseTable.loadDataTable(API.SALES_ORDER.LIST_SALES_ORDER_ONPROGRESS, '');
      }
      this.updateTotalCount();
      this.updateCount();
    });
  }

  updateTotalCount() {
    this.baseDatasourcePending.totalItem$.subscribe((data) => {
      if (data) {
        this.totalCount.emit(data);
      }
    });
  }

  handleOpenFilter = () => {
    this.isOpenFilter = !this.isOpenFilter;
  };

  handleSubmitFilter = () => {
    this.isActiveFilter = true;
    this.isOpenFilter = false;

    this.checkActive.next(!this.checkActive.value);
    const _extras = {
      queryParams: {
        string_filter: this.string_filter,
        start_date: this.utilities.timeStampToDate(this.filterForm.controls['start_date'].value, 'yyyy-MM-dd'),
        end_date: this.utilities.timeStampToDate(this.filterForm.controls['end_date'].value, 'yyyy-MM-dd'),
      },
    };
    this.filterService.submitFilter(this.filterForm, _extras);
  };

  handleResetFilter = () => {
    this.isActiveFilter = false;
    this.isOpenFilter = false;
    const _keyFilter = ['start_date', 'end_date'];
    this.filterService.resetFilter(this.filterForm, _keyFilter);
  };
}
