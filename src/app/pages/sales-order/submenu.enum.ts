export enum SubMenu {
  PENDING = 'pending',
  ON_PROGRESS = 'onprogress',
  HISTORY = 'history',
}

export enum SubMenuLabel {
  PENDING = 'Sedang Menunggu',
  ONPROGRESS = 'Diproses',
  HISTORY = 'History',
  LIST = 'List'
}

export enum StatusDetailPrivilage {
  CREATED = 'DETAIL_CREATED',                      // Waiting Sales
  PENDING = 'DETAIL_PENDING',                      // Pending Sales
  WAITING_ERP = 'DETAIL_WAITING_ERP',                    // Sales Approve
  CONFIRMED = 'DETAIL_CONFIRMED',              // Need Fulfill
  EXPIRED = 'DETAIL_EXPIRED',              // In Progress
  REJECTED = 'DETAIL_REJECTED',                      // Expired
  ON_PROGRESS_SCAN = 'DETAIL_ON_PROGRESS_SCAN',
  ON_PROGRESS = 'DETAIL_ON_PROGRESS',                  // Completed
  ON_DELIVERY = 'DETAIL_ON_DELIVERY',   // delivery
  COMPLETED = 'DETAIL_COMPLETED'
}

export enum StatusFilterEnumSalesOrder {
  WAITING_CENTRAL_ADMIN = "WAITING_CENTRAL_ADMIN",
  WAITING_FINANCE = "WAITING_FINANCE",
  OUTSTANDING = "OUTSTANDING",
  IN_PROGRESS = "IN_PROGRESS",
  PARTIAL_COMPLETED = "PARTIAL_COMPLETED",
  COMPLETED = "COMPLETED",
  EXPIRED = "EXPIRED",
  REJECTED = "REJECTED"
}
