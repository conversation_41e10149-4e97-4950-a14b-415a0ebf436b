import { Injectable } from '@angular/core';
import { SalesOrderLegacyService } from '../sales-order-legacy.service';
import { Router } from '@angular/router';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { BaseService } from '@services/base-service.service';
import { CreateSpmModel } from './create-spm/create-spm.model';
import { BehaviorSubject, tap } from 'rxjs';
import { API } from '@config/constants/api.constant';
import { IPostCreateSPM, ISPM_ResponseData } from '../../spm/spm.interface';
import { map } from 'rxjs/operators';
import { IProductOrder__ProductList } from '@shared/interface/product-order.interface';
import { ISODetailProductPromag, ISODetailResponse, ISOProductOrderResponse } from '../detail-sales-order/detail/detail-so.interface';
import { DetailSoModel } from '../detail-sales-order/detail/detail-so.model';
import { IProductNotFulFill } from '@models/spm.model';
import { IInitSalesOrderDetail } from '@models/sales-order.model';
import { FormBuilder } from '@angular/forms';
import { IPayloadProductQty } from '@models/product-order.model';
import { ICardProductPromag } from '@shared/components/card/card-product-promag/card-product-promag.interface';
import { IGenericResponseSuccess } from '@shared/interface/generic';

@Injectable({
  providedIn: 'root',
})
export class SalesOrderService extends SalesOrderLegacyService {
  private createSpmModel = new CreateSpmModel();
  private detailSoModel = new DetailSoModel();

  private initHeaderCreateSpmSubject = new BehaviorSubject(this.createSpmModel.HeaderData);
  initHeaderCreateSpm$ = this.initHeaderCreateSpmSubject.asObservable();

  private spmProductOrderListSubject = new BehaviorSubject(this.createSpmModel.ProductOrderList);

  private initSODetailSubject = new BehaviorSubject(this.detailSoModel.InitDetailSoData);
  private salesOrderDetailIDSubject = new BehaviorSubject('');

  private detailSoSubject!: BehaviorSubject<ISODetailResponse>;
  private productOrderSoSubject = new BehaviorSubject(this.detailSoModel.SoProductOrderData);
  private productNotFulFillSubject = new BehaviorSubject(this.detailSoModel.SoProductNotFulFillData);
  private productPromagListSubject = new BehaviorSubject(this.detailSoModel.SoDetailProductPromag); // promag list in detail so

  constructor(fb: FormBuilder, baseService: BaseService, router: Router, rolePrivilegeService: RolePrivilegeService) {
    super(fb, baseService, router, rolePrivilegeService);
    this.detailSoSubject = new BehaviorSubject(this.detailSoModel.DetailSoData);
  }

  // INITIAL DETAIL SO
  get SoDetailID() {
    return this.salesOrderDetailIDSubject.value;
  }

  set SoDetailID(id: string) {
    this.salesOrderDetailIDSubject.next(id);
  }

  get SoWarehouseID() {
    return this.DetailSo.warehouse.id;
  }

  get InitSoDetail() {
    return this.initSODetailSubject.value;
  }

  set InitSoDetail(data: IInitSalesOrderDetail) {
    this.initSODetailSubject.next(data);
  }

  getInitialDetailSO(id: string) {
    return this.baseService.getData<IInitSalesOrderDetail>(API.SALES_ORDER.GET_INIT_DETAIL_SALES_ORDER + id).pipe(tap((res) => (this.InitSoDetail = res && res.data)));
  }

  // DETAIL SO
  get DetailSo() {
    return this.detailSoSubject.value;
  }

  set DetailSo(val) {
    this.detailSoSubject.next(val);
  }

  get ProductNotFulFill() {
    return this.productNotFulFillSubject.value;
  }

  set ProductNotFulFill(val) {
    this.productNotFulFillSubject.next(val);
  }

  get ProductPromagList() {
    return this.productPromagListSubject.value;
  }

  set ProductPromagList(val) {
    this.productPromagListSubject.next(val);
  }

  getSoDetail(soID: string) {
    return this.baseService.getData<ISODetailResponse>(API.SALES_ORDER.GET_DETAIL + soID).pipe(tap((res) => (this.DetailSo = res && res.data)));
  }

  get ProductOrderSo() {
    return this.productOrderSoSubject.value;
  }

  set ProductOrderSo(val) {
    this.productOrderSoSubject.next(val);
  }

  getSoProductOrder(id = this.SoDetailID) {
    return this.baseService.getData<ISOProductOrderResponse>(API.SALES_ORDER.GET_DETAIL_LIST_PRODUCT + id).pipe(tap((res) => (this.ProductOrderSo = res && res.data)));
  }

  canCtaCloseSO() {
    const ctaPrivilege = this.detailPrivilegeCTA('CTA_CLOSE_SALES_ORDER');
    return ctaPrivilege && this.InitSoDetail.can_close_so;
  }

  detailPrivilegeCTA(enumString: string) {
    return this.rolePrivilegeService.checkPrivilege('SALES_ORDER', 'DETAIL_SALES_ORDER', enumString);
  }

  goToCreateSPM(id = this.SoDetailID) {
    const { so_code } = this.InitSoDetail;
    return this.router.navigate(['/sales-order/create-spm'], {
      queryParams: {
        so_id: id,
        so_code,
        so_status: 'outstanding',
      },
    });
  }

  // CREATE SPM
  get InitCreateSpm() {
    return this.initHeaderCreateSpmSubject.value;
  }

  set InitCreateSpm(data) {
    this.initHeaderCreateSpmSubject.next(data);
  }

  get SPMProductOrderList() {
    return this.spmProductOrderListSubject.value;
  }

  set SPMProductOrderList(data) {
    this.spmProductOrderListSubject.next(data);
  }

  getInitCreateSPM(id = this.SoDetailID) {
    return this.baseService.getData<ISPM_ResponseData>(API.SPM.INIT_CREATE_SPM + id).pipe(
      tap((res) => (this.InitCreateSpm = res && res.data)),
      map((resp) => resp && resp.data)
    );
  }

  getSpmProductOrderList(id = this.SoDetailID) {
    return this.baseService.getData<IProductOrder__ProductList[]>(API.SPM.GET_LIST_PRODUCT_ORDER + id).pipe(
      tap((res) => (this.SPMProductOrderList = res && res.data)),
      map((resp) => resp && resp.data)
    );
  }

  getProductNotFulFill(id = this.SoDetailID) {
    return this.baseService.getData<IProductNotFulFill>(API.SALES_ORDER.GET_SALES_ORDER_PRODUCT_NOT_FULFILL + id).pipe(tap((res) => (this.ProductNotFulFill = res && res.data)));
  }

  getProductListProgramMarketing(id = this.SoDetailID) {
    return this.baseService.getData<ISODetailProductPromag[]>(API.SALES_ORDER.GET_DETAIL_LIST_PROGRAM_MARKETING + id).pipe(
      tap((res) => {
        this.ProductPromagList = res && res.data;
      }),
      map((resp) => {
        return resp && resp.data;
      })
    );
  }

  getPatchProductPromag(id = this.SoDetailID, payload: IPayloadProductQty) {
    return this.baseService.patchDataWithBody<ICardProductPromag[]>(API.SALES_ORDER.CREATE_SPM_LIST_PRODUCT_PROGRAM_MARKETING + id, payload).pipe(map((res) => res && res.data));
  }

  postCreateSPM(id: string, payload: IPostCreateSPM) {
    return this.baseService.postData<IGenericResponseSuccess>(API.SALES_ORDER.POST_CREATE_SPM + id, payload);
  }
}
