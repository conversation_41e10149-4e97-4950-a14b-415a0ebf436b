<app-card>
  <ng-container *ngIf="baseDatasource.isTableLoaded && baseDatasource.totalItem$.getValue() === 0; else elseBlock" cardBody>
    <app-card-empty icon="{{ iconNone }}" text="Belum terdapat data delivery order."></app-card-empty>
  </ng-container>
</app-card>
<ng-template #elseBlock>
  <app-card [cardClasses]="'mt-7'" [header]="true" cardBodyClasses="pt-0" cardHeaderTitle="List Delivery Order">
    <ng-container cardBody>
      <div class="table-responsive">
        <table [dataSource]="baseDatasource" class="table w-100 gy-5 table-row-bordered align-middle" mat-table matSort>
          <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
            <!-- COLUMN HEADER -->
            <ng-container *ngIf="tableColumn.isSortable; else notSortable">
              <th *matHeaderCellDef [arrowPosition]="'after'" [mat-sort-header]="tableColumn.key" class="min-w-125px px-3" mat-header-cell>
                <div *ngIf="baseDatasource.isFinishLoadingSubject.value">
                  {{ tableColumn.title }}
                </div>
                <div *ngIf="!baseDatasource.isFinishLoadingSubject.value">
                  <app-table-content [width]="70"></app-table-content>
                </div>
              </th>
            </ng-container>
            <!--            COLUMN BODY-->
            <ng-template #notSortable>
              <th *matHeaderCellDef class="min-w-125px px-3" mat-header-cell>
                <div *ngIf="baseDatasource.isFinishLoadingSubject.value">
                  {{ tableColumn.title }}
                </div>
                <div *ngIf="!baseDatasource.isFinishLoadingSubject.value">
                  <app-table-content [width]="70"></app-table-content>
                </div>
              </th>
            </ng-template>

            <!-- COLUMN DATA -->
            <td *matCellDef="let element; let i = index" class="px-3" mat-cell>
              <ng-container [ngSwitch]="tableColumn.key">
                <div *ngSwitchCase="'total_package'">
                  <app-table-content [count]="2" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
                    <div>{{ element[tableColumn.key] + ' ' + element['package_unit'] }}</div>
                  </app-table-content>
                </div>

                <div *ngSwitchCase="'created_date'">
                  <app-table-content [count]="2" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
                    <div>{{ utilities.timeStampToDate(element[tableColumn.key], 'dd-MM-yyyy') }}</div>
                  </app-table-content>
                </div>

                <div *ngSwitchCase="'expedition_type'">
                  <app-table-content [count]="1" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
                    <div>{{ element['expedition_type'] ? element['expedition_type'] : '-' }}</div>
                  </app-table-content>
                </div>

                <div *ngSwitchCase="'action'" class="text-center">
                  <app-table-content [count]="1" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'icon'">
                    <div class="d-flex">
                      <span (click)="handleActionDetail(element['id'])" [inlineSVG]="'./assets/media/icons/ic_task.svg'" class="svg-icon svg-icon-2 cursor-pointer"></span>
                    </div>
                  </app-table-content>
                </div>

                <div *ngSwitchDefault>
                  <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    <span>{{ element[tableColumn.key] }} </span>
                  </app-table-content>
                </div>
              </ng-container>
            </td>
          </ng-container>
          <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
          <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
        </table>
      </div>
    </ng-container>
  </app-card>
</ng-template>
