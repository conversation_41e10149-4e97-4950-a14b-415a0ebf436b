<ng-container *ngIf="!isActiveFilter && !string_filter && baseDatasourcePending.isTableLoaded && baseDatasourcePending.totalItem$.getValue() === 0; else elseBlock" cardBody>
  <app-card-empty icon="{{ iconNone }}" text="Sales Order tidak ditemukan."></app-card-empty>
</ng-container>

<ng-template #elseBlock>
  <!-- begin::Card -->
  <app-card [cardBodyClasses]="'pt-2'" [header]="true">
    <!--begin::Card header-->
    <ng-container cardHeader>
      <app-input-search
        (actionFilter)="onSearch($event)"
        [isFinishLoadingSubject]="baseDatasourcePending.isFinishLoadingSubject"
        placeholder="Cari SO ID / Nama Distributor"
        value="{{ string_filter }}"
      ></app-input-search>

      <div class="ms-auto position-relative" data-kt-customer-table-toolbar="base">
        <!--begin::Filter-->
        <app-filter-table
          (actionClick)="handleOpenFilter()"
          (actionReset)="handleResetFilter()"
          [isActiveFilter]="isActiveFilter"
          [isFinishLoadingSubject]="baseDatasourcePending.isFinishLoadingSubject"
          [isOpenFilter]="isOpenFilter"
        >
          <div class="menu menu-sub menu-sub-dropdown w-300px w-md-350px show filter-body" id="kt-toolbar-filter">
            <!--begin::Header-->
            <div class="px-7 py-5">
              <div class="fs-4 text-dark fw-bold">Filter</div>
            </div>
            <div class="separator border-gray-200"></div>
            <form (ngSubmit)="handleSubmitFilter()" [formGroup]="filterForm" [ngClass]="'w-100'" class="form">
              <div class="px-7 py-5">
                <div class="mb-10">
                  <label class="form-label fs-5 mb-6">Tanggal Dibuat:</label>
                  <app-date-range-picker [formGroup]="filterForm" [nameEnd]="'end_date'" [maxDate]="currentDate"
                                         [nameStart]="'start_date'" [placeholder]="'Pilih range tanggal dibuat'">
                  </app-date-range-picker>
                </div>
                <!--begin::Actions-->
                <div class="d-flex justify-content-end">
                  <button (click)="handleResetFilter()" class="btn btn-light btn-active-light-primary me-4" type="reset">Reset</button>
                  <button class="btn btn-primary" type="submit">Terapkan</button>
                </div>
              </div>
            </form>
          </div>
        </app-filter-table>
      </div>
    </ng-container>
    <!--end::Card header-->
    <!--begin::Card body-->
    <ng-container cardBody>
      <ng-container
        *ngIf="(isActiveFilter || string_filter) && baseDatasourcePending.isTableLoaded && baseDatasourcePending.totalItem$.getValue() === 0; else elseFilterBlock"
        cardBody
      >
        <app-card-empty icon="{{ iconNone }}" text="Sales Order tidak ditemukan."></app-card-empty>
      </ng-container>
      <ng-template #elseFilterBlock>
        <div class="table-responsive">
          <table (matSortChange)="sortTable($event)" [dataSource]="baseDatasourcePending" class="table w-100 gy-5 table-row-bordered align-middle" mat-table matSort>
            <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
              <!-- COLUMN HEADER -->
              <ng-container *ngIf="tableColumn.isSortable; else notSortable">
                <th
                  *matHeaderCellDef
                  [arrowPosition]="'after'"
                  [class.min-w-200px]="tableColumn.key !== 'actions' && tableColumn.key !== 'code'"
                  [mat-sort-header]="tableColumn.key"
                  class="min-w-125px px-3"
                  mat-header-cell
                >
                  <div *ngIf="baseDatasourcePending.isFinishLoadingSubject.value">
                    {{ tableColumn.title }}
                  </div>
                  <div *ngIf="!baseDatasourcePending.isFinishLoadingSubject.value">
                    <app-table-content [width]="70"></app-table-content>
                  </div>
                </th>
              </ng-container>
              <ng-template #notSortable>
                <th
                  *matHeaderCellDef
                  [class.min-w-200px]="tableColumn.key !== 'actions' && tableColumn.key !== 'id'"
                  [ngClass]="{
                    'min-w-70px text-center': tableColumn.key === 'actions',
                    'w-500px': tableColumn.key === 'distributor_name'
                  }"
                  class="min-w-125px px-3"
                  mat-header-cell
                >
                  <div *ngIf="baseDatasourcePending.isFinishLoadingSubject.value">
                    {{ tableColumn.title }}
                  </div>
                  <div *ngIf="!baseDatasourcePending.isFinishLoadingSubject.value">
                    <app-table-content [width]="70"></app-table-content>
                  </div>
                </th>
              </ng-template>
              <!-- COLUMN DATA -->
              <td *matCellDef="let element" class="px-3" mat-cell>
                <!-- {{ element[tableColumn.key]}} -->
                <ng-container [ngSwitch]="tableColumn.key">
                  <div *ngSwitchCase="'createdDate'">
                    <app-table-content [count]="2" [isFinishLoadingSubject]="baseDatasourcePending.isFinishLoadingSubject" [type]="'text'">
                      <div>{{ element['created_date'] ? utilities.timeStampToDate(element['created_date'], 'dd-MM-yyyy') : '-' }}</div>
                      <div>{{ utilities.timeStampToDate(element['created_date'], 'HH:mm:ss') }}</div>
                    </app-table-content>
                  </div>
                  <div *ngSwitchCase="'status_enum_string'">
                    <app-table-content [height]="30" [isFinishLoadingSubject]="baseDatasourcePending.isFinishLoadingSubject" [width]="120">
                      <span class="badge badge__status {{ 'badge__status--' + element['status_enum'] }}">
                        {{ element[tableColumn.key] }}
                      </span>
                    </app-table-content>
                  </div>
                  <div *ngSwitchCase="'process_limit'">
                    <app-table-content [count]="1" [isFinishLoadingSubject]="baseDatasourcePending.isFinishLoadingSubject" [type]="'text'">
                      <div [class]="salesOrderService.generateClassProcessLimit(element['process_limit'])">{{ element['process_limit'] ?? '-' }}</div>
                    </app-table-content>
                  </div>
                  <div *ngSwitchCase="'actions'" class="text-center">
                    <app-table-content [count]="1" [isFinishLoadingSubject]="baseDatasourcePending.isFinishLoadingSubject" [type]="'icon'">
                      <span (click)="handleActionDetail(element['id'])" [inlineSVG]="'./assets/media/icons/ic_task.svg'" class="svg-icon svg-icon-2 cursor-pointer"></span>
                    </app-table-content>
                  </div>
                  <div *ngSwitchDefault>
                    <app-table-content [isFinishLoadingSubject]="baseDatasourcePending.isFinishLoadingSubject">
                      <span>{{ element[tableColumn.key] ?? '-' }}</span>
                    </app-table-content>
                  </div>
                </ng-container>
              </td>
            </ng-container>
            <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
            <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
          </table>
        </div>

        <div class="d-flex justify-content-between py-4">
          <app-mai-material-bottom-table
            (changePage)="changePageEvent($event)"
            [baseDataTableComponent]="baseDatasourcePending"
            [isFinishLoadingSubject]="baseDatasourcePending.isFinishLoadingSubject"
            class="w-100"
          ></app-mai-material-bottom-table>
        </div>
      </ng-template>
    </ng-container>
    <!--end::Card body-->
  </app-card>
  <!-- end::Card -->
</ng-template>
