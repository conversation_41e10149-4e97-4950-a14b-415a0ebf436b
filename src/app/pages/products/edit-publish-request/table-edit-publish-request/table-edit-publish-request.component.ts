import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { Sort } from '@angular/material/sort';
import { BaseDatasource } from '@shared/base/_base.datasource';
import { TableColumn } from '@shared/interface/table.interface';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { IListVariantNeedPublish } from '../../Products.interface';
import { API } from '@config/constants/api.constant';
import { ActivatedRoute } from '@angular/router';
import { BaseTableService } from '@shared/base/base-table-service.service';
import { UrlParamService } from '@services/urlParam.service';
import { UtilitiesService } from '@services/utilities.service';
import { BehaviorSubject, Subscription } from 'rxjs';
import { map } from 'rxjs/operators';

@Component({
  selector: 'app-table-edit-publish-request',
  templateUrl: './table-edit-publish-request.component.html',
  styleUrls: ['./table-edit-publish-request.component.scss'],
})
export class TableEditPublishRequestComponent implements OnInit, OnDestroy {
  @Input() brandName: string = '';
  @Output() dataVariantList = new EventEmitter();
  baseDatasource: BaseDatasource<any>;
  tableColumns: TableColumn[];
  displayedColumns: string[];
  STRING_CONSTANTS = STRING_CONSTANTS;
  id: any | null = null;
  loading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);

  variantList: IListVariantNeedPublish[];
  variantCount: number = 0;

  private unsubscribe: Subscription[] = [];

  constructor(
    private activeRoute: ActivatedRoute,
    private baseTableService: BaseTableService<IListVariantNeedPublish>,
    private urlParamService: UrlParamService,
    private utilities: UtilitiesService
  ) {}

  ngOnInit(): void {
    this.id = this.activeRoute.snapshot.params.id;
    this.setTableData();
    this.queryHandler();
  }

  setTableData() {
    const _responseSubs = this.baseTableService.responseDatabase$
      .pipe(
        map((result) => {
          result.tableSubject.value.map((_item) => {
            if (_item.detail_variant) {
              _item.price_unit_with_ppn = _item['detail_variant'].price_unit_with_ppn;
              _item.price_per_box_with_ppn = _item['detail_variant'].price_per_box_with_ppn;
            }
          });

          return result;
        })
      )
      .subscribe((res) => {
        this.baseDatasource = res;
        this.dataVariantList.emit(this.baseDatasource.tableSubject);
      });

    this.tableColumns = [
      { title: 'NAMA VARIANT', key: 'product_name' },
      { title: 'JUMLAH PER BOX', key: 'qty' },
      { title: 'HARGA SATUAN', key: 'price_unit' },
      { title: 'HARGA SATUAN (PPN)', key: 'price_unit_with_ppn' },
      { title: 'HARGA PER BOX', key: 'price_per_box' },
      { title: 'HARGA PER BOX (PPN)', key: 'price_per_box_with_ppn' },
    ];

    this.displayedColumns = this.tableColumns.map((head) => head.key);

    this.unsubscribe.push(_responseSubs);
  }

  queryHandler() {
    let param = this.urlParamService.sliceQueryParams();
    param = param ? param : '';
    this.baseTableService.loadDataTable(API.BRAND_DETAIL_VARIANT_LIST + this.id, param);
  }

  toRupiah = (value: number) => this.utilities.toRupiah(value);

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  sortTable($event: Sort) {}

  handleAction() {}

  changePageEvent($event: any) {}
}
