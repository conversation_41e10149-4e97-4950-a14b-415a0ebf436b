import { ChangeDetector<PERSON><PERSON>, Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { PageInfoService, PageLink } from '@metronic/layout';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { BehaviorSubject, Subscription } from 'rxjs';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { API } from '@config/constants/api.constant';
import { UtilitiesService } from '@services/utilities.service';
import { BaseService } from '@services/base-service.service';
import { ActivatedRoute, Router } from '@angular/router';
import {
  IBodyRequestNeedPublishProduct,
  IDetailProduct,
  IDetailProductNeedPublish,
  IDetailProductsEditResponse,
  IListVariantDetail,
  IVariantImages,
  IVariantProductImage,
} from '../Products.interface';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { SwallService } from '@services/swall.service';
import { STEPPER_GLOBAL_OPTIONS } from '@angular/cdk/stepper';
import { IChips } from '@shared/components/v1/chips/chips.interface';
import { InputSelectMaterialInterface } from '@shared/components/form/input-select-material/input-select-material.interface';
import { IGenericNameUrl } from '@shared/interface/generic';
import { RewardSettingService } from '../../reward-setting/reward-setting.service';
import { IPayloadAddProductReward } from '../../reward-setting/reward-setting.interface';
// @ts-ignore
import {  Editor, Toolbar  } from 'ngx-editor';

@Component({
  selector: 'app-edit-publish-request',
  templateUrl: './edit-publish-request.component.html',
  styleUrls: ['./edit-publish-request.component.scss'],
  providers: [
    {
      provide: STEPPER_GLOBAL_OPTIONS,
      useValue: { displayDefaultIndicatorType: false },
    },
  ],
})
export class EditPublishRequestComponent implements OnInit, OnDestroy {
  variantList: BehaviorSubject<IListVariantDetail[]> = new BehaviorSubject<IListVariantDetail[]>([]);
  STRING_CONSTANTS = STRING_CONSTANTS;
  listUrl: BehaviorSubject<string[]> = new BehaviorSubject<string[]>([]);
  desc: Editor;
  product: Editor;
  toolbar: Toolbar = [
    [{ heading: ['h1', 'h2', 'h3'] }],
    ['bold', 'italic'],
    ['ordered_list', 'bullet_list']
  ];

  API: any = API;
  ENDPOINT_PLANT_TYPE: string = API.GET_PLANT_TYPE;
  ENDPOINT_TARGET_LIST: string = API.LIST_TARGET_PEST;
  ENDPOINT_MATERIAL_ACTIVE: string = API.LIST_MATERIAL_ACTIVE;

  isNeedPublished: boolean = true;
  brandId: any | null = null;
  isLoading = new BehaviorSubject(false);
  DetailProduct = new BehaviorSubject<IDetailProduct | undefined>(undefined);
  DetailProductUpdateResponse = new BehaviorSubject<IDetailProductsEditResponse | undefined>(undefined);

  valuePlantType: IChips[] = [];
  valueTarget: IChips[] = [];
  valueMaterialActive: IChips[] = [];

  dataReqBodyPublishProduct: IBodyRequestNeedPublishProduct;
  isValidButton: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  isHaveFill: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  variantImageData: IVariantImages[] = [] as IVariantImages[];

  modalConfig: ModalConfig = {
    modalTitle: 'Publish Product',
    closeButtonLabel: 'Close',
    dismissButtonLabel: 'Lanjutkan',
  };

  modalConfigEdit: ModalConfig = {
    modalTitle: 'Edit Product',
    closeButtonLabel: 'Close',
    dismissButtonLabel: 'Lanjutkan',
  };

  config = {
    height: 430,
    toolbar: [
      { name: 'styles', items: ['Format', 'Styles'] },
      { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline'] },
      { name: 'links', items: ['heading'] },
      {
        name: 'paragraph',
        items: ['NumberedList', 'BulletedList', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'],
      },
    ],
    removePlugins: 'elementspath',
  };

  fbProductForm: FormGroup = this.formBuilder.group({
    plant_type: ['', Validators.required],
    pest_target: ['', Validators.required],
    material_active: ['', Validators.required],
    description: ['', Validators.required],
    product_usage: ['', Validators.required],
  });

  fbRewardForm: FormGroup = this.formBuilder.group({
    id_periode: ['', Validators.required],
    qty: [''],
    document: [''],
  });

  @ViewChild('modalConfirm') private modalConfirmComponent: ModalComponent;
  @ViewChild('modalConfirmEdit') private modalConfirmComponentEdit: ModalComponent;

  hasSetupReward = false;
  listPeriodeSubject = new BehaviorSubject<InputSelectMaterialInterface[]>([]);

  private unsubscribe: Subscription[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private pageInfoService: PageInfoService,
    private utilsService: UtilitiesService,
    private baseService: BaseService,
    private activeRoute: ActivatedRoute,
    private swall: SwallService,
    private router: Router,
    private ref: ChangeDetectorRef,
    private rewardSettingService: RewardSettingService
  ) {}

  get getPlantType() {
    return this.fbProductForm.controls['plant_type'];
  }

  get getTarget() {
    return this.fbProductForm.controls['pest_target'];
  }

  get getMaterialActive() {
    return this.fbProductForm.controls['material_active'];
  }

  get getProductDescription() {
    return this.fbProductForm.controls['description'];
  }

  get getProductUsage() {
    return this.fbProductForm.controls['product_usage'];
  }

  get rewardPeriode() {
    return <FormControl>this.fbRewardForm.get('id_periode');
  }

  get rewardCouponQty() {
    return <FormControl>this.fbRewardForm.get('qty');
  }

  get rewardDocument() {
    return <FormControl>this.fbRewardForm.get('document');
  }

  ngOnInit(): void {
    this.brandId = this.activeRoute.snapshot.params.id;

    this.checkIsEdit();
    this.setData();
    this.getData();
    this.handlerValidBtn();

    this.desc = new Editor();
    this.product = new Editor();


    setTimeout(() => {
      this.ref.detectChanges(); // for update the select with value from api
    }, 5000);
  }

  checkIsEdit() {
    const route: string[] = this.router.url.split('/');
    this.isNeedPublished = route[2] !== 'detail';
    if (this.isNeedPublished) {
      this.isHaveFill.next(false);

      const listPeriodeSubs = this.rewardSettingService.getListPeriode().subscribe((resp) => {
        if (resp && resp.success) return this.listPeriodeSubject.next(this.rewardSettingService.mapInputSelectPeriode(resp.data));
      });

      this.checkSetupReward();
      this.unsubscribe.push(listPeriodeSubs);
    }
  }

  checkSetupReward() {
    if (!this.brandId) return;
    this.isLoading.next(true);

    const _initSubs = this.rewardSettingService.getInitialRewardPeriode(this.brandId).subscribe((resp) => {
      if (!resp) return;

      const { is_allowed_setting, period_id } = resp.data;
      this.hasSetupReward = is_allowed_setting;

      if (this.hasSetupReward) {
        this.rewardPeriode.setValue(period_id);
        this.handleCouponQtyChanges();
      }

      this.isLoading.next(false);
    });

    this.unsubscribe.push(_initSubs);
  }

  handleCouponQtyChanges() {
    const clearValidator = () => {
      this.rewardCouponQty.clearValidators();
      this.rewardDocument.clearValidators();
    };

    this.rewardCouponQty.valueChanges.subscribe((res) => {
      if (res >= 0) {
        this.rewardCouponQty.setValidators(Validators.compose([Validators.required, Validators.min(1)]));
        this.rewardDocument.setValidators(Validators.required);

        if (res === 0 && !this.documentHasValue()) clearValidator();
      } else {
        clearValidator();
      }

      this.rewardDocument.updateValueAndValidity({ emitEvent: false });
      this.rewardCouponQty.updateValueAndValidity({ emitEvent: false });
    });
  }

  handleDocument = (e: IGenericNameUrl) => this.rewardDocument.patchValue(e);

  documentHasValue = () => !!this.rewardDocument.value;

  handleLoadingDocument = (e: boolean) => this.isLoading.next(e);

  setData() {
    const _respSubs = this.DetailProductUpdateResponse.subscribe((data) => {
      if (!data) return;

      const { plant_type, pest_target, material_active, description, product_usage } = data;

      this.fbProductForm.patchValue({
        plant_type: this.generateChips(plant_type, 'plantType'),
        pest_target: this.generateChips(pest_target, 'target'),
        material_active: this.generateChips(material_active, 'materialActive'),
        description,
        product_usage,
      });

      this.ref.detectChanges();
    });

    this.unsubscribe.push(_respSubs);
  }

  generateChips(datas: string[], selectType: string) {
    if (datas && datas.length > 0) {
      let _temp_id: string[] = [];
      let _selectedItems: IChips[] = [];

      datas.map((val: any) => {
        const { id, name } = val;
        _temp_id.push(id);
        _selectedItems.push({ id, name, selected: true });
      });

      if (selectType === 'plantType') {
        this.valuePlantType = _selectedItems;
      } else if (selectType === 'target') {
        this.valueTarget = _selectedItems;
      } else if (selectType === 'materialActive') {
        this.valueMaterialActive = _selectedItems;
      } else {
        return _temp_id;
      }

      return _temp_id;
    }
  }

  getData() {
    this.baseService.getData<IDetailProduct>(API.GET_DETAIL_BRAND + this.brandId).subscribe((res) => {
      if (!res) return;
      this.DetailProduct.next(res.data);
    });

    this.baseService.getData<IDetailProductsEditResponse>(API.GET_DETAIL_BRAND_DATA_FOR_UPDATED + this.brandId).subscribe((res) => {
      if (!res) return;
      this.DetailProductUpdateResponse.next(res.data);
      this.initPageInfo();
    });
  }

  handleSubmit() {
    return this.isNeedPublished ? this.modalConfirmComponent.open() : this.modalConfirmComponentEdit.open();
  }

  initPageInfo() {
    this.DetailProductUpdateResponse.subscribe((value) => {
      const links: Array<PageLink> = [
        { title: 'Product Catalog', path: '/products/list-published', isActive: false },
        { title: '', path: '', isActive: false, isSeparator: true },
        {
          title: this.isNeedPublished ? 'Publish Request' : 'Product List',
          path: this.isNeedPublished ? '/products/publish-request' : '/products/list-published',
          isActive: false,
        },
        { title: '', path: '', isActive: false, isSeparator: true },
        { title: value?.name ?? '', path: '', isActive: true },
      ];
      if (value) {
        this.pageInfoService.updateTitle(value.name ?? '', undefined, false);
        this.pageInfoService.updateBreadcrumbs(links);
      }
    });
  }

  submitForm = () => {
    this.dataReqBodyPublishProduct = {
      image_product: this.listUrl.value,
      plant_type_id: this.getPlantType.value,
      product_target_id: this.getTarget.value,
      material_active_id: this.getMaterialActive.value,
      description_product: this.getProductDescription.value,
      use_product: this.getProductUsage.value,
    };

    this.postEditPublishProduct(this.dataReqBodyPublishProduct);
  };

  checkVariantImageData(): boolean {
    // check for each empty variant image
    let _validToPublish = true;
    this.variantImageData.forEach((_v) => {
      if (_v.product_images.length < 1) {
        _validToPublish = false;
      }
    });

    return _validToPublish;
  }

  handlerValidBtn() {
    this.isValidButton.next(this.fbProductForm.invalid);
  }

  postEditPublishProduct(data: IBodyRequestNeedPublishProduct) {
    const endpoint = this.isNeedPublished ? API.PUBLISH_BRAND : API.UPDATED_BRAND;
    const _postSubs = this.baseService.putData<IBodyRequestNeedPublishProduct>(endpoint + this.brandId, data).subscribe((resp) => {
      if (!resp) return;
      if (this.hasSetupReward) return this.postRewardProduct();
      this.postVariantImageData();
    });

    this.unsubscribe.push(_postSubs);
  }

  postVariantImageData(data: IVariantImages[] = this.variantImageData) {
    const postVariantImage$ = this.baseService.putData<IVariantImages>(API.PUBLISH_BRAND_VARIANT_IMAGES + this.brandId, data);
    postVariantImage$.subscribe((resp) => {
      if (resp && resp.success) this.handlePostSuccess();
    });
  }

  handlePostSuccess() {
    const _msg = this.isNeedPublished ? 'di publish' : 'di update.';
    this.swall
      .GetAlert({
        confirmButtonText: 'OK',
        text: `Produk berhasil ${_msg}`,
      })
      .then(() => {
        return this.router.navigate(['/products/detail/' + this.brandId]);
      });
  }

  postRewardProduct() {
    const _payload: IPayloadAddProductReward = {
      brand_id: this.brandId,
      periode_id: this.rewardPeriode.value.value,
      qty: this.rewardCouponQty.value,
      document: this.rewardDocument.value,
    };

    // post add reward product
    const postRewardProduct$ = this.rewardSettingService.addProductReward(_payload);
    postRewardProduct$.subscribe((resp) => {
      if (resp && resp.success) return this.postVariantImageData();
    });
  };

  getVariantList(data: BehaviorSubject<IListVariantDetail[]>) {
    // set data model for variant list images
    const _variantSubs = data.subscribe((res) => {
      this.variantList.next(res);

      res.forEach((item, index) => {
        if (!Object.keys(item).length) return;

        this.variantImageData.push({
          product_id: item.product_id,
          product_images: item.product_images.length ? this.mapVariantListImages(item.product_images, index) : [],
        });
      });

      if (this.variantImageData.length > 0) {
        this.variantImageData = this.utilsService.removeDuplicateByKey(this.variantImageData, 'product_id');
      }
    });

    this.unsubscribe.push(_variantSubs);
  }

  mapVariantListImages(productImages: string[], idx: number) {
    let _variantImage = [] as IVariantProductImage[];

    productImages.forEach((item) => {
      _variantImage.push({
        sequence_image: idx + 1,
        images_url: item,
      });
    });

    return _variantImage;
  }

  handleFileUrl(evt: [{ id: string; url: string }], productId: string) {
    if (!productId) {
      return;
    }

    const _variantItem = this.variantImageData.filter((_v) => _v.product_id === productId)[0];

    for (let i = 0; i < evt.length; i++) {
      if (evt[i].id === productId) {
        _variantItem.product_images.push({
          sequence_image: i + 1,
          images_url: evt[i].url,
        });
      }
    }

    // remove duplicated items
    _variantItem.product_images = [...new Map(_variantItem.product_images.map((v) => [v.images_url, v])).values()];
    this.variantImageData = this.variantImageData.filter((_v) => _v.product_id !== undefined);

    this.checkVariantImageData();
  }

  handleRemoveFileUrl(evt: { id: string; url: string }) {
    const { id, url } = evt;
    const _thisVariant = this.variantImageData.filter((_v) => _v.product_id === id);
    _thisVariant[0].product_images = _thisVariant[0].product_images.filter((_item) => _item.images_url !== url);
  }

  variantImageToData(productId: string, productImages: any) {
    if (!productId || !productImages) {
      return undefined;
    }
    if (!productImages.length) {
      return undefined;
    }

    return {
      id: productId,
      images: productImages,
    };
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }
}

// Todo: Remove unused methods
