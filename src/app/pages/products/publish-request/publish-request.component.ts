import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild } from '@angular/core';
import { PageInfoService, PageLink } from '@metronic/layout';
import { BaseDatasource } from '@shared/base/base.datasource';
import { TableColumn } from '@shared/interface/table.interface';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { IListPublishRequest } from '../Products.interface';
import { BaseTableService } from '@shared/base/base-table-service.service';
import { API } from '@config/constants/api.constant';
import { ActivatedRoute, Router } from '@angular/router';
import { UrlUtilsService } from '@utils/url-utils.service';
import { UtilitiesService } from '@services/utilities.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import { BehaviorSubject, Subscription } from 'rxjs';
import { MultipleSelectChipComponent } from '@shared/components/multiple-select-chip/multiple-select-chip.component';
import { InputSelectInterface } from '@shared/components/form/input-select/input-select.interface';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { FilterService } from '@services/filter.service';
import { ProductBrandService } from '@services/product-brand.service';

@Component({
  selector: 'app-publish-request',
  templateUrl: './publish-request.component.html',
  styleUrls: ['./publish-request.component.scss'],
})
export class PublishRequestComponent implements OnInit, OnDestroy {
  @ViewChild('inputSelectMatChips') inputSelectMatChips: MultipleSelectChipComponent;

  baseDatasource: BaseDatasource<IListPublishRequest>;
  tableColumns: TableColumn[];
  displayedColumns: string[];
  productList: IListPublishRequest[];
  string_filter: string;
  isOpenFilter: boolean;
  isActiveFilter: boolean;
  filterForm: FormGroup;
  pestisidaList: InputSelectInterface[];
  checkActive: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  totalCount: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  STRING_CONSTANTS = STRING_CONSTANTS;
  iconNone = STRING_CONSTANTS.ICON.IC_PACKAGE_NONE;
  privilageUpdateNeedPublish: boolean;

  links: Array<PageLink> = [
    { title: 'Product Catalog', path: '/products', isActive: false },
    { title: '', path: '', isActive: false, isSeparator: true },
    { title: 'Publish Request', path: '', isActive: true },
  ];

  private unsubscribe: Subscription[] = [];

  constructor(
    private pageInfoService: PageInfoService,
    private baseTableService: BaseTableService<any>,
    private activeRoute: ActivatedRoute,
    private router: Router,
    private formBuilder: FormBuilder,
    private paginationService: UrlUtilsService,
    private utilities: UtilitiesService,
    private rolePrivilegeService: RolePrivilegeService,
    public filterService: FilterService,
    private productBrandService: ProductBrandService
  ) {}

  ngOnInit(): void {
    this.baseTableService.responseDatabase.subscribe((response) => (this.baseDatasource = response));
    this.privilageUpdateNeedPublish = this.rolePrivilegeService.checkPrivilege('PRODUCT_CATALOG', 'LIST_PRODUCT_PUBLISH_REQUEST', 'FORM_PRODUCT_PUBLISH_REQUEST');
    this.setTableData();
    this.getData();
    this.initFilter();
    this.initPageInfo();
    this.queryHandler();
  }

  getData() {
    // possibly not used
    // this.baseService.getData<IListPlantType[]>(API.GET_PLANT_TYPE).subscribe((value) => {
    //   if (value) {
    //     this.plantTypeList = value.data.map((res) => {
    //       let inputSelector: InputSelectInterface = new InputSelectInterface();
    //       inputSelector.value = res.id;
    //       inputSelector.display = res.name;
    //       return inputSelector;
    //     });
    //     this.behaviorSubjectPlantTypeList.next(this.plantTypeList);
    //   }
    // });

    this.productBrandService.getListPestisida().subscribe((resp) => {
      if (!resp) return;
      this.pestisidaList = this.productBrandService.mapInputSelectPesticide(resp.data);
    });
  }

  initPageInfo(): void {
    const dataSubs = this.baseDatasource.tableSubject.subscribe((data) => {
      if (data) {
        this.productList = data;
        this.pageInfoService.updateBreadcrumbs(this.links);
      }
    });

    this.unsubscribe.push(dataSubs);
  }

  updateTotalCount() {
    this.baseDatasource.totalItem$.subscribe((data) => {
      if (data) {
        this.totalCount.next(data);
      }
    });
  }

  updateCount() {
    this.baseDatasource.totalItem$.subscribe((data) => {
      if (data) {
        this.pageInfoService.updateTitle(`Publish Request (` + data + `)`);
      } else {
        this.pageInfoService.updateTitle(`Publish Request (0)`);
      }
    });
  }

  queryHandler() {
    this.activeRoute.queryParams.subscribe((data) => {
      this.string_filter = data.string_filter;
      this.filterForm.controls['pesticide_type'].setValue(data.pesticide_type);
      this.isActiveFilter = !!data.pesticide_type;
      const param = this.paginationService.sliceQueryParams();
      if (param) {
        this.baseTableService.loadDataTable(API.LIST_BRAND_PENDING, param);
      } else {
        this.baseTableService.loadDataTable(API.LIST_BRAND_PENDING, '');
      }
      this.updateTotalCount();
      this.updateCount();
    });
  }

  toRupiah = (value: number) => this.utilities.toRupiah(value);

  setTableData() {
    this.tableColumns = [
      { title: 'NAMA BRAND', key: 'name' },
      { title: 'JENIS PESTISIDA', key: 'pesticide_type' },
      { title: 'JUMLAH VARIANT', key: 'variant' },
      { title: 'STATUS', key: 'status' },
    ];
    this.tableColumns = this.utilities.privilegeTableColumns(this.privilageUpdateNeedPublish, this.tableColumns);
    this.displayedColumns = this.tableColumns.map((head) => head.key);
  }

  changePageEvent($event: BaseDatasource<any>) {
    this.filterService.changePageEvent($event, this.string_filter ?? '');
  }

  handleAction(id: string) {
    this.router.navigate([`products/publish-request/edit/` + id]).then(() => null);
  }

  onSearch(event: string) {
    this.checkActive.next(!this.checkActive.value);
    this.filterService.onSearch(event);
    this.string_filter = '';
  }

  initFilter() {
    this.filterForm = this.formBuilder.group({
      pesticide_type: [''],
    });
  }

  handleSubmitFilter() {
    this.isActiveFilter = true;
    this.isOpenFilter = false;
    this.filterService.submitFilter(this.filterForm);
  }

  handleResetFilter = () => {
    const _keyFilter = ['pesticide_type'];
    this.filterService.resetFilter(this.filterForm, _keyFilter);
  };

  onClickFilter() {
    this.isOpenFilter = !this.isOpenFilter;
  }

  ngOnDestroy(): void {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }
}
