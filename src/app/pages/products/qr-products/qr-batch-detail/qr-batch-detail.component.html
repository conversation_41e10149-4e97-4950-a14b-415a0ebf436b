<ng-container *ngIf="(detailBatch$ | async)?.data as data; else skeletonHeaderTpl">
  <app-card>
    <ng-container cardBody>
      <div class="heading d-flex align-items-center mb-7">
        <h2 class="mb-0">
          {{ data['batch_name'] }}
        </h2>
        <span class="ms-10 badge__status {{ 'badge__status--' + data['request_generate_qr_status'] }}"> {{ data.request_generate_qr_status_name }} </span>
      </div>

      <ng-container *ngIf="isBatchOnProgress(data['request_generate_qr_status'])">
        <app-note-view [color]="'info'" [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION" [text]="noteViewEstimationDate(data.available_date)"></app-note-view>
      </ng-container>

      <div class="row mb-4" *ngFor="let item of detailBatchInfo">
        <div class="col-12 col-md-2">
          <span class="text-gray-700">{{ item.label }}</span>
        </div>
        <div class="col-12 col-md-auto">
          <span>: {{ item.value }}</span>
        </div>
      </div>

      <div class="mt-7" *ngIf="PrivilegeDownloadBatch">
        <button mat-raised-button color="primary" class="btn btn-primary" (click)="handleActionDownload()" [disabled]="!isAbleToDownloadBatch(data['request_generate_qr_status'])">
          <span class="svg-icon svg-icon2 me-4" [inlineSVG]="STRING_CONSTANTS.ICON.IC_DOWNLOAD_WHITE"></span>
          <span [class.text-white]="!isAbleToDownloadBatch(data['request_generate_qr_status'])">Unduh Batch</span>
        </button>
      </div>
    </ng-container>
  </app-card>
</ng-container>

<app-card *ngIf="PrivilegeViewListBrand" [cardHeaderTitle]="'Produk'" [header]="true" [cardClasses]="'my-7'">
  <ng-container cardBody>
    <div class="table-responsive">
      <table mat-table class="table w-100 gy-5 table-row-bordered align-middle" [dataSource]="baseDatasource">
        <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
          <th *matHeaderCellDef class="px-3" mat-header-cell>
            <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [width]="70">
              {{ tableColumn.title }}
            </app-table-content>
          </th>

          <td *matCellDef="let element" class="px-3">
            <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
              <span>{{ element[tableColumn.key] ?? '-' }}</span>
            </app-table-content>
          </td>
        </ng-container>

        <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
        <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
      </table>

      <div class="d-flex justify-content-between py-4">
        <app-mai-material-bottom-table
          (changePage)="changePageEvent($event)"
          [baseDataTableComponent]="baseDatasource"
          [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
          class="w-100"
        ></app-mai-material-bottom-table>
      </div>
    </div>
  </ng-container>
</app-card>

<ng-template #skeletonHeaderTpl>
  <ng-template #defaultCardTpl>
    <app-card cardClasses="mb-7" [header]="!(!loadingGetDetail.value && !detailBatchInfo.length)">
      <ng-container cardHeader>
        <app-skeleton-text [isLoading]="loadingGetDetail"></app-skeleton-text>
      </ng-container>
      <ng-container cardBody>
        <app-skeleton-text [isLoading]="loadingGetDetail" [count]="3" [type]="'text-and-text'"></app-skeleton-text>
      </ng-container>
    </app-card>
  </ng-template>

  <ng-container *ngIf="!loadingGetDetail.value && !detailBatchInfo.length; else defaultCardTpl">
    <ng-template [ngTemplateOutlet]="errorFetchingTpl"></ng-template>
  </ng-container>
</ng-template>

<ng-template #errorFetchingTpl>
  <app-note-view color="danger" text="Fetching data failed!" [icon]="STRING_CONSTANTS.ICON.IC_DANGER" [extraContent]="true"></app-note-view>
</ng-template>

<app-modal-confirm-download #modalConfirmDownload [selectedBatch]="selectedQRBatchToDownload"></app-modal-confirm-download>
