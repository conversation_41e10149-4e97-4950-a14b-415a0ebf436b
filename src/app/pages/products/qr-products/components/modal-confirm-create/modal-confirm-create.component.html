<app-modal #modalConfirmCreate [modalConfig]="modalConfig">
  <!--  {{ selectedListBrand | async | json }}-->
  <ng-container *ngIf="(selectedListBrand | async)?.list as list">
    <p class="mb-0">QR yang akan digenerate:</p>
    <ul>
      <li *ngFor="let item of list">
        <span>{{ item.name }} - {{ renderQtyString(item.generate_qty) }}</span>
      </li>
    </ul>
    <p>Total QR: {{ renderQtyString(selectedListBrand.value.total_qty) }}</p>
  </ng-container>
</app-modal>

<app-modal #modalResponseCreate [modalConfig]="modalConfigResponse">
  <div class="d-flex flex-column justify-content-center align-items-center text-center">
    <ng-container *ngIf="loadingPost | async; else responseTpl">
      <div class="mt-8">
        <mat-spinner></mat-spinner>
      </div>
    </ng-container>

    <ng-template #responseTpl>
      <span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT"></span>
      <div class="mt-8" *ngIf="responsePost">
        Anda berhasil membuat Batch
        <span
          ><strong>{{ responsePost.batch_name }}.</strong></span
        ><br />
        QR akan terbuat dan estimasi dapat diunduh pada {{ responsePost.estimate_finish_date }}.
      </div>
    </ng-template>
  </div>
</app-modal>
