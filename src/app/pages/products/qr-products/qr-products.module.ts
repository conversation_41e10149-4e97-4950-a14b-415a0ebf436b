import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';

import { QrProductsListComponent } from './qr-products-list/qr-products-list.component';
import { FilterListQrProductComponent } from './components/filter-list-qr-product/filter-list-qr-product.component';
import { ComponentsModule } from '@shared/components/components.module';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { QrBatchDetailComponent } from './qr-batch-detail/qr-batch-detail.component';
import { ModalConfirmDownloadComponent } from './components/modal-confirm-download/modal-confirm-download.component';
import { QrBatchAddComponent } from './qr-batch-add/qr-batch-add.component';
import { InputListboxBrandComponent } from './components/input-listbox-brand/input-listbox-brand.component';
import { DirectiveModule } from '@directives/directive.module';
import { ModalConfirmCreateComponent } from './components/modal-confirm-create/modal-confirm-create.component';
import { ModalConfirmCancelComponent } from './components/modal-confirm-cancel/modal-confirm-cancel.component';
import { MatSortModule } from '@angular/material/sort';

@NgModule({
  declarations: [
    QrProductsListComponent,
    FilterListQrProductComponent,
    QrBatchDetailComponent,
    ModalConfirmDownloadComponent,
    QrBatchAddComponent,
    InputListboxBrandComponent,
    ModalConfirmCreateComponent,
    ModalConfirmCancelComponent,
  ],
  imports: [
    CommonModule,
    MatTableModule,
    MatButtonModule,
    FormsModule,
    ReactiveFormsModule,
    ComponentsModule,
    InlineSVGModule,
    MatChipsModule,
    MatIconModule,
    MatProgressSpinnerModule,
    DirectiveModule,
    MatSortModule,
  ],
})
export class QrProductsModule {}
