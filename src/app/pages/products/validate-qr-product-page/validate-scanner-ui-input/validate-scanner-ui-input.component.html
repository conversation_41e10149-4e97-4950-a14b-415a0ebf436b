<form [formGroup]="scannerInput">
  <div *ngIf="isLoading.value">Loading</div>
  <div *ngIf="!isLoading.value">
    <h1 class="mt-15">R1</h1>
    <label style="background-image: url('./assets/media/Group_7031.svg')" class="background-input-qr" [htmlFor]="'qrScanInput'">
      <input
        (focusin)="clear()"
        *ngIf="!isLoading.value"
        type="text"
        (ngModelChange)="onChangeR1Data($event)"
        #inputQrForm
        autofocus
        formControlName="qrCode"
        id="qrScanInput"
        class="input-text-scanner"
      />
    </label>

    <h2 class="mt-10">{{ r1Code }}</h2>
    <br />
    <h1 class="mt-15">R2</h1>

    <label *ngIf="isValidateView" style="background-image: url('./assets/media/Group_7031.svg')" class="background-input-qr" [htmlFor]="'qrScanInput'">
      <input
        *ngIf="!isLoading.value"
        type="text"
        (ngModelChange)="onChangeR2Data($event)"
        #r2QrCode
        autofocus
        formControlName="qrCodeR2"
        id="r2QrCode"
        class="input-text-scanner"
      />
    </label>
    <h2 class="mt-10">{{ r2Code }}</h2>
  </div>
</form>
