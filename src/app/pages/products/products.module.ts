import { NgModule } from '@angular/core';
import { CommonModule, NgOptimizedImage } from '@angular/common';
import { ProductsListComponent } from './products-list/products-list.component';
import { ProductsRoutingModule } from './products-routing.module';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { DetailProductsComponent } from './detail-products/detail-products.component';
import { SwiperModule } from 'swiper/angular';
import { MatTabsModule } from '@angular/material/tabs';
import { InfoProductsComponent } from './detail-products/info-products/info-products.component';
import { UsageDescriptionComponent } from './detail-products/usage-description/usage-description.component';
import { HistoryQrComponent } from './detail-products/history-qr/history-qr.component';
import { PublishRequestComponent } from './publish-request/publish-request.component';
import { EditPublishRequestComponent } from './edit-publish-request/edit-publish-request.component';
import { ImageUploadComponent } from '@shared/components/image-upload/image-upload.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InputListboxPlantTypeComponent } from '@shared/components/input-listbox-plant-type/input-listbox-plant-type.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { DirectiveModule } from '@directives/directive.module';
import { ComponentsModule } from '@shared/components/components.module';
import { CKEditorModule } from 'ckeditor4-angular';
import { ScannerUiInputComponent } from '@shared/components/scanner-ui-input/scanner-ui-input.component';

import { NgbPopoverModule } from '@ng-bootstrap/ng-bootstrap';
import { TableEditPublishRequestComponent } from './edit-publish-request/table-edit-publish-request/table-edit-publish-request.component';
import { VariantInformationComponent } from './detail-products/variant-information/variant-information.component';

import { MatMenuModule } from '@angular/material/menu';
import { DistributorModule } from '../distributor/distributor.module';
// import { NgxPrintElementModule } from 'ngx-print-element';
import { ScannerComponent } from './scanner/scanner.component';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatStepperModule } from '@angular/material/stepper';
import { ValidateQrProductPageComponent } from './validate-qr-product-page/validate-qr-product-page.component';
import { MultipleImageUploadComponent } from '@shared/components/multiple-image-upload/multiple-image-upload.component';
import { DistributorExclusiveComponent } from './detail-products/distributor-exclusive/distributor-exclusive.component';
import { HistoryComponent } from './detail-products/distributor-exclusive/history/history.component';
import { QrProductsModule } from './qr-products/qr-products.module';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NgxEditorComponent, NgxEditorMenuComponent } from 'ngx-editor';

@NgModule({
  declarations: [
    ProductsListComponent,
    DetailProductsComponent,
    InfoProductsComponent,
    PublishRequestComponent,
    UsageDescriptionComponent,
    HistoryQrComponent,
    EditPublishRequestComponent,
    InputListboxPlantTypeComponent,
    ScannerUiInputComponent,
    TableEditPublishRequestComponent,
    VariantInformationComponent,
    ScannerComponent,
    ValidateQrProductPageComponent,
    ImageUploadComponent,
    MultipleImageUploadComponent,
    DistributorExclusiveComponent,
    HistoryComponent,
  ],
  exports: [ScannerUiInputComponent, ImageUploadComponent, MultipleImageUploadComponent],
  imports: [
    CommonModule,
    ProductsRoutingModule,
    DirectiveModule,
    MatTableModule,
    MatSortModule,
    InlineSVGModule,
    NgxSkeletonLoaderModule,
    SwiperModule,
    MatTabsModule,
    ReactiveFormsModule,
    FormsModule,
    MatFormFieldModule,
    MatIconModule,
    ComponentsModule,
    CKEditorModule,
    NgbPopoverModule,
    MatMenuModule,
    DistributorModule,
    // NgxPrintElementModule,
    MatProgressSpinnerModule,
    MatStepperModule,
    QrProductsModule,
    MatTooltipModule,
    NgOptimizedImage,
    NgxEditorComponent,
    NgxEditorMenuComponent,
  ],
})
export class ProductsModule {}
