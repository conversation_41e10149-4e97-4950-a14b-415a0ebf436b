import {Component, Input, OnInit} from '@angular/core';
import {QrDataGenerator} from "../../Products.interface";
import {BehaviorSubject} from "rxjs";
import {UtilitiesService} from "@services/utilities.service";

@Component({
  selector: 'app-document-test',
  templateUrl: './document-test.component.html',
  styleUrls: ['./document-test.component.scss']
})
export class DocumentTestComponent implements OnInit {
  @Input() data: BehaviorSubject<QrDataGenerator[] | undefined> = new BehaviorSubject<QrDataGenerator[] | undefined>(undefined)

  browser: string

  constructor(
    public utilities: UtilitiesService,
  ) {
  }

  ngOnInit(): void {
    this.browser = this.utilities.getBrowserName()
  }
}
