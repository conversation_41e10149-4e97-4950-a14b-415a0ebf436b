import {Component, Input, OnInit} from '@angular/core';
import {IProductDesc,} from "../../Products.interface";
import {BehaviorSubject} from "rxjs";

@Component({
  selector: 'app-usage-description',
  templateUrl: './usage-description.component.html',
  styleUrls: ['./usage-description.component.scss']
})
export class UsageDescriptionComponent implements OnInit {
  @Input() descriptionProduct  ?: BehaviorSubject<IProductDesc>;

  data: BehaviorSubject<IProductDesc> = new BehaviorSubject<IProductDesc>({
    description: "",
    product_usage: ""
  })

  constructor() {
  }

  ngOnInit(): void {
    this.descriptionProduct?.subscribe(value => {
      if (value) {
        this.data.next({
          description: value.description,
          product_usage: value.product_usage
        })
      }
    })
  }
}
