<div style="display: flex; flex-direction: column; gap: 24px;">
  <app-card>
    <div cardBody class="d-flex w-100 justify-content-between  detail-card-products">
      <div class="right-section">
        <app-skeleton-text [height]="25" [isLoading]="isLoading" [width]="500">
          <h1>{{dataDetails.value?.name ?? "-"}}</h1>
        </app-skeleton-text>
        <app-skeleton-text [height]="25" [isLoading]="isLoading" [width]="500">
          <label class="text-gray-400">{{dataDetails.value?.material_actives ?? "-"}}</label>
        </app-skeleton-text>
        <div class="mt-7">
          <div *ngIf="bodyList.value.length > 0">
            <div *ngFor="let item of bodyList.value" class="row mb-3">
              <label class="col-lg-2 text-gray-400">{{item.label}}</label>
              <div class="col-lg-10">
                <span class="text-gray-800">: {{item.value ?? '-'}}</span>
              </div>
            </div>
          </div>
          <hr class="mt-7">
        </div>
        <div class="mt-7">
          <div *ngFor="let item of subBodyList" class="row mb-3">
            <label class="col-lg-4 text-gray-400">{{item.label}}</label>
            <div class="col-lg-8">
              <span class="text-gray-800">: {{item.value ?? '-'}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </app-card>

  <app-card *ngIf="readDetail" [header]="false">
    <ng-container cardBody>
      <ng-container *ngIf="!isLoading.value && baseDatasource.isTableLoaded && rewardSettingList.length === 0; else elseFilterBlock" cardBody>
        <app-card-empty icon="{{ ASSET_ILLUSTRATION.IL_PROMO }}" text="Belum terdapat reward produk."></app-card-empty>
      </ng-container>

      <ng-template #elseFilterBlock>
        <div class="mb-3">
          <p class="h2 mb-3">Informasi Reward</p>
        </div>

        <div class="table-responsive">
          <table (matSortChange)="sortTable($event)" [dataSource]="rewardSettingList" class="table w-100 gy-5 table-row-bordered align-middle" mat-table matSort>
            <ng-container *ngFor="let tableColumn of tableColumns; index as i" [matColumnDef]="tableColumn.key">
              <ng-container *ngIf="tableColumn.isSortable; else notSortTable">
                <th
                  *matHeaderCellDef
                  [arrowPosition]="'after'"
                  [class.min-w-200px]="tableColumn.key !== 'actions' && tableColumn.key !== 'id'"
                  [mat-sort-header]="tableColumn.key"
                  class="min-w-125px px-3"
                  mat-header-cell
                >
                  <div *ngIf="baseDatasource.isFinishLoadingSubject.value">
                    {{ tableColumn.title }}
                  </div>
                  <div *ngIf="!baseDatasource.isFinishLoadingSubject.value">
                    <app-table-content [width]="70"></app-table-content>
                  </div>
                </th>
              </ng-container>
              <ng-template #notSortTable>
                <th
                  *matHeaderCellDef
                  [class.min-w-200px]="tableColumn.key !== 'actions' && tableColumn.key !== 'id'"
                  [ngClass]="{
                    'min-w-70px text-center': tableColumn.key === 'actions',
                    'w-800px': tableColumn.key === 'name'
                  }"
                  class="min-w-125px px-3"
                  mat-header-cell
                >
                  <div *ngIf="tableColumn.key === 'qty' else headerElse"
                       class="d-flex info-header-detail-products">
                    <div *ngIf="baseDatasource.isFinishLoadingSubject.value">
                      {{ tableColumn.title }}
                    </div>
                    <div *ngIf="!baseDatasource.isFinishLoadingSubject.value">
                      <app-table-content [width]="180"></app-table-content>
                    </div>
                    <span
                      *ngIf="baseDatasource.isFinishLoadingSubject.value"
                      [inlineSVG]="ASSET_ICON.IC_INFO"
                      class="ms-2 cursor-pointer my-auto"
                      ngbPopover="Jumlah scan QR produk untuk mendapatkan 1 Maxxi Poin"
                      placement="bottom"
                      popoverClass="tooltip-info-product text-primary"
                    ></span>
                  </div>
                  <ng-template #headerElse>
                    <div *ngIf="baseDatasource.isFinishLoadingSubject.value">
                      {{ tableColumn.title }}
                    </div>
                    <div *ngIf="!baseDatasource.isFinishLoadingSubject.value">
                      <app-table-content [width]="180"></app-table-content>
                    </div>
                  </ng-template>
                </th>
              </ng-template>

              <td *matCellDef="let element" class="px-3" mat-cell>
                <ng-container [ngSwitch]="tableColumn.key">
                  <div *ngSwitchCase="'actions'" class="text-center">
                    <button
                      [ngClass]="{
                        'is-download': element['is_download']
                      }"
                      class="btn btn-table-actions-download"
                    >
                      <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'icon'">
                        <span class="mx-3" (click)="downloadDocument(element['document'])" [inlineSVG]="ASSET_ICON.IC_DOWNLOAD"></span>
                      </app-table-content>
                    </button>
                  </div>

                  <div *ngSwitchCase="'name'">
                    <app-table-content [count]="1" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
                      {{ element['name'] ?? '-' }}
                    </app-table-content>
                  </div>

                  <div *ngSwitchCase="'qty'">
                    <app-table-content [count]="1" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
                      <div>
                        {{ element['qty'] ?? 0 }}
                      </div>
                    </app-table-content>
                  </div>

                  <div *ngSwitchCase="'status'">
                    <app-table-content [height]="30" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
                                       [width]="120">
                      <span class="badge badge__status"
                            [ngClass]="element['status_enum'] !== RewardSettingStatus.FINISH ? 'badge__status--' + element['status_enum'] : 'text-gray-600' ">
                        {{ element['status_string'] }}
                      </span>
                    </app-table-content>
                  </div>

                  <div *ngSwitchCase="'createdDate'">
                    <app-table-content [height]="30" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
                                       [type]="'text'"
                                       [width]="120">
                      <div>{{ utilities.formatEpochToDate(element['created_date']) }}</div>
                      <div>{{ utilities.formatEpochToDate(element['created_date'], 'HH:mm:ss') }}</div>
                    </app-table-content>
                  </div>

                  <div *ngSwitchDefault>
                    <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                      <span>{{ element[tableColumn.key] ?? '-' }} </span>
                    </app-table-content>
                  </div>
                </ng-container>
              </td>
            </ng-container>

            <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
            <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
          </table>
        </div>
      </ng-template>
    </ng-container>
  </app-card>

  <app-card *ngIf="readDetail">
    <div cardBody>
      <app-skeleton-text [height]="25" [isLoading]="isLoading" [width]="500">
        <h2>Deskripsi Produk</h2>
      </app-skeleton-text>

      <app-skeleton-text [height]="25" [isLoading]="isLoading" [width]="500">
        <div *ngIf="dataDetails.value?.description; else NoDescription"
             [innerHTML]="dataDetails.value?.description"></div>
      </app-skeleton-text>

      <ng-template #NoDescription>
        <app-skeleton-text [height]="25" [isLoading]="isLoading" [width]="500">
          <label class="mt-3 text-gray-400">Tidak Ada Deskripsi Produk</label>
        </app-skeleton-text>
      </ng-template>
    </div>
  </app-card>

  <app-card *ngIf="readDetail">
    <div cardBody>
      <app-skeleton-text [height]="25" [isLoading]="isLoading" [width]="500">
        <h2>Penggunaan Produk</h2>
      </app-skeleton-text>

      <app-skeleton-text [height]="25" [isLoading]="isLoading" [width]="500">
        <div *ngIf="dataDetails.value?.product_usage; else NoProductUsage"
             [innerHTML]="dataDetails.value?.product_usage"></div>
      </app-skeleton-text>

      <ng-template #NoProductUsage>
        <app-skeleton-text [height]="25" [isLoading]="isLoading" [width]="500">
          <label class="mt-3 text-gray-400">Tidak Ada Penggunaan Produk</label>
        </app-skeleton-text>
      </ng-template>
    </div>
  </app-card>

</div>
