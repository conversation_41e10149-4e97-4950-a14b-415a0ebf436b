import { ChangeDetector<PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { BehaviorSubject, Subscription } from 'rxjs';
import { IApprovalDatePurchaseOrder, IApprovalPurchaseOrder, IPurchaseOrderDetail, IReasonNotePurchaseOrder } from '../../purchase.order.interface';
import { ActivatedRoute, Router } from '@angular/router';
import { StatusPurchaseOrder } from '@config/enum/status.enum';
import { BaseService } from '@services/base-service.service';
import { API } from '@config/constants/api.constant';
import { DatePipe, registerLocaleData } from '@angular/common';
import localeId from '@angular/common/locales/id';
import { UtilitiesService } from '@services/utilities.service';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { AuthService } from '@services/auth.service';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { CardProductOrderListComponent } from '@shared/components/card-product-order-list/card-product-order-list.component';
// import { Modal } from 'bootstrap';

registerLocaleData(localeId);

// Tab Detail PO
@Component({
  selector: 'app-detail',
  templateUrl: './detail.component.html',
  styleUrls: ['./detail.component.scss'],
})
export class DetailComponent implements OnInit, OnDestroy {
  STRING_CONSTANTS = STRING_CONSTANTS;
  iconSuccess = STRING_CONSTANTS.ICON.SUCCESS_ALERT;

  statusPurchaseOrder: string;
  statusPurchaseOrderEnum = StatusPurchaseOrder;

  expiredDate: string;
  expiredDays: number;

  resumeApprovalDate: BehaviorSubject<IApprovalDatePurchaseOrder[]> = new BehaviorSubject<IApprovalDatePurchaseOrder[]>([]);

  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  reasonNote: BehaviorSubject<IReasonNotePurchaseOrder> = new BehaviorSubject<IReasonNotePurchaseOrder>({} as IReasonNotePurchaseOrder);

  // params
  id: any | null = null;
  dataDetail = new BehaviorSubject<IPurchaseOrderDetail>({} as IPurchaseOrderDetail);

  @ViewChild('cardProductOrder') cardProductOrder: CardProductOrderListComponent;
  // approval modal
  @ViewChild('modalApprove') modalApprove: ModalComponent;
  modalApproveConfig: ModalConfig = {
    modalTitle: 'Approve Purchase Order',
    closeButtonLabel: 'Cancel',
    dismissButtonLabel: 'Approve',
    onDismiss: () => this.handleApproval(),
  };

  @ViewChild('modalReject') modalReject: ModalComponent;
  @ViewChild('modalApproveResponse') modalApproveResponse: ModalComponent;
  approvalStatus: string = '';
  approvalResponseMessage: string = '';
  isLoadingApproval = new BehaviorSubject<boolean>(false);
  modalApproveResponseConfig: ModalConfig = {
    dismissButtonLabel: 'Oke',
    disableDismissButton: () => this.isLoadingApproval.value,
    hideCloseButton: () => true,
  };
  rejectReason: string = '';
  modalRejectConfig: ModalConfig = {
    modalTitle: 'Reject Purchase Order',
    closeButtonLabel: 'Cancel',
    dismissButtonLabel: 'Reject',
    disableDismissButton: () => !this.rejectReason,
    onDismiss: () => this.handleApproval(),
  };
  // dummyReasonNote: IReasonNotePurchaseOrder = {
  //   title: 'Catatan dari Regional Head',
  //   suggestion: 'Item Order, Jenis Pembayaran', // null if finance have value note and status "Butuh Perbaikan RH"
  //   description: 'Mohon ditambahkan produk X sebanyak X box dan mengganti pembayaran ke CBD agar mendapatkan discount 15%, terima kasih',
  // };
  private unsubscribe: Subscription[] = [];

  constructor(
    private activatedRoute: ActivatedRoute,
    private baseService: BaseService,
    private router: Router,
    public utilities: UtilitiesService,
    private authService: AuthService,
    private rolePrivilegeService: RolePrivilegeService,
    private ref: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.id = this.activatedRoute.snapshot.params.id;
    this.getData();
    this.setData();
  }

  // Get Data from API
  getData() {
    const data = this.baseService.getData<IPurchaseOrderDetail>(API.PURCHASE_ORDER.GET_DETAIL_PO + this.id).subscribe((resp) => {
      if (resp && resp.success) {
        this.dataDetail.next(resp.data);
        this.isLoading.next(false);
        this.ref.detectChanges();
      }
    });

    this.unsubscribe.push(data);
  }

  setData() {
    this.dataDetail.subscribe((value) => {
      if (Object.keys(value).length === 0) return;

      this.statusPurchaseOrder = value.status_enum;
      const pipe = new DatePipe('id-ID');
      const dateId = pipe.transform(value.expired_date, 'dd MMMM yyyy');
      this.expiredDate = dateId ? dateId : '';
      const oneDay = 24 * 60 * 60 * 1000;
      const firstDate = new Date(value.expired_date).valueOf();
      const secondDate = new Date().valueOf();
      this.expiredDays = Math.round(Math.abs((firstDate - secondDate) / oneDay));

      // this.resumeCreatedDate = this.getDateFormat(value.created_date);
      this.reasonNote.next(value.reason_note ?? null);
    });
  }

  isExpired(type: string, time: number) {
    if (type === 'progress') {
      return this.statusPurchaseOrder !== this.statusPurchaseOrderEnum.EXPIRED && this.statusPurchaseOrder !== this.statusPurchaseOrderEnum.COMPLETED && this.expiredDays > time;
    } else if (type === 'expired') {
      return this.statusPurchaseOrder === this.statusPurchaseOrderEnum.EXPIRED && this.expiredDays > time;
    } else {
      return false;
    }
  }

  handleApprove = () => {
    this.approvalStatus = 'APPROVED';
    return this.modalApprove.open();
  };
  handleReject = () => {
    this.approvalStatus = 'REJECTED';
    return this.modalReject.open();
  };

  async handleApproval() {
    this.approvalResponseMessage = this.approvalStatus === 'APPROVED' ? 'Purchase Order berhasil disetujui.' : 'Purchase Order berhasil ditolak.';
    const closeModal = () => (this.approvalStatus !== 'APPROVED' ? this.modalReject.close() : this.modalApprove.close());
    await closeModal();
    return await this.submitApproval();
  }

  async submitApproval() {
    this.isLoadingApproval.next(true);

    const data: IApprovalPurchaseOrder = {
      purchase_order_approve_enum: this.approvalStatus,
      reason: this.rejectReason,
    };

    this.baseService.putData<IApprovalPurchaseOrder>(`${API.APPROVAL_PO_PENDING}${this.id}`, data).subscribe((resp) => {
      // console.log('submit approval resp ', resp);
      if (resp) {
        if (!resp.success) {
          return this.modalApproveResponse.close();
        }

        this.isLoadingApproval.next(false);
      }
    });

    // remove after test integration
    // const _tempSetLoading = () => this.isLoadingApproval.next(false);
    // setTimeout(() => _tempSetLoading(), 2000);

    await this.modalApproveResponse.open();
    return await this.router.navigate(['purchase-order/waiting']);
  }

  shouldShowApprovalSection(status: string | undefined) {
    if (!status) {
      return;
    }
    const allowedStatus = [
      // this.statusPurchaseOrderEnum.NEED_APPROVE_REGIONAL_DIRECTOR,
      this.statusPurchaseOrderEnum.NEED_APPROVE_REGIONAL_FINANCE,
    ].toString();

    const checkAction = this.rolePrivilegeService.checkPrivilege('PURCHASE_ORDER', 'DETAIL_PURCHASE_ORDER', 'CTA_APPROVAL_PO_FINANCE');
    return allowedStatus.includes(status) && (this.authService.currentUserRole === 'FINANCE' || this.authService.currentUserRole === 'SUPER_ADMIN') && checkAction;
  }

  showReason() {
    const enumStatusReason = [
      StatusPurchaseOrder.NEED_CHANGE_ORDER_BY_RH,
      StatusPurchaseOrder.NEED_CHANGE_DISCOUNT,
      StatusPurchaseOrder.NEED_CHANGE_ORDER_BY_DISTRIBUTOR,
      StatusPurchaseOrder.CANCEL_BY_DISTRIBUTOR,
      StatusPurchaseOrder.CANCEL_BY_RH,
      StatusPurchaseOrder.CANCEL_BY_MARKETING,
    ];
    return enumStatusReason.find((val) => val === this.statusPurchaseOrder);
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  protected readonly API = API;
}
