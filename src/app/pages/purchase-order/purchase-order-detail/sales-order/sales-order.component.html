<app-card [cardClasses]="'mt-7'">
  <ng-container cardBody class="d-flex flex-wrap flex-sm-nowrap">
    <div *ngIf="isHasRecord" class="table-responsive">
      <table [dataSource]="baseDatasource" class="table w-100 gy-5 table-row-bordered align-middle" mat-table>
        <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
          <!-- COLUMN HEADER -->
          <ng-container>
            <th
              *matHeaderCellDef
              [class.min-w-200px]="tableColumn.key !== 'actions' && tableColumn.key !== 'id'"
              [ngClass]="{
                'min-w-70px text-center': tableColumn.key === 'actions'
              }"
              class="min-w-125px px-3"
              mat-header-cell
            >
              <ng-container>
                {{ tableColumn.title }}
              </ng-container>
            </th>
          </ng-container>
          <!-- COLUMN DATA -->
          <td *matCellDef="let element" class="px-3">
            <ng-container [ngSwitch]="tableColumn.key">
              <div *ngSwitchCase="'name'">
                <app-table-content [height]="30" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [width]="120">
                  <div class="d-flex gap-3">
                    <div>
                      <img
                        class="img-thumbnail img-product-card shadow border-0"
                        alt="product"
                        src="{{ setProductItem(element['product_item_list'])?.image_url }}"
                        (error)="handleNoImage($event)"
                      />
                    </div>
                    <div>
                      <span class="product-list">{{ setProductItem(element['product_item_list'])?.name }}</span
                      ><br />
                      <span *ngIf="setProductItem(element['product_item_list'])?.count !== 0" class="desc"
                        >+{{ setProductItem(element['product_item_list'])?.count }} Produk Lainnya</span
                      >
                    </div>
                  </div>
                </app-table-content>
              </div>
              <div *ngSwitchCase="'createdDate'">
                <app-table-content [height]="30" [width]="120" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                  <div>{{ utilities.timeStampToDate(element['created_date'], 'dd-MM-yyyy') }}</div>
                  <div>{{ utilities.timeStampToDate(element['created_date'], 'HH:mm:ss') }}</div>
                </app-table-content>
              </div>
              <div *ngSwitchCase="'status'">
                <app-table-content [height]="30" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [width]="120">
                  <span class="badge badge__status {{ 'badge__status--' + element['status_enum'] }}">
                    {{ element['status_enum_string'] }}
                  </span>
                </app-table-content>
              </div>
              <div *ngSwitchCase="'actions'" class="text-center">
                <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" type="icon">
                  <span
                    (click)="handleAction(element['id'], element['status_enum'])"
                    [inlineSVG]="'./assets/media/icons/ic_task.svg'"
                    class="svg-icon svg-icon-2 cursor-pointer"
                  ></span>
                </app-table-content>
              </div>
              <div *ngSwitchDefault>
                <app-table-content [height]="30" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [width]="120">
                  <span>{{ element[tableColumn.key] }}</span>
                </app-table-content>
              </div>
            </ng-container>
          </td>
        </ng-container>
        <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
        <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
      </table>
    </div>
    <ng-container *ngIf="!isHasRecord">
      <app-card-empty icon="{{ iconNone }}" text="Belum terdapat Sales Order."></app-card-empty>
    </ng-container>
  </ng-container>
</app-card>
