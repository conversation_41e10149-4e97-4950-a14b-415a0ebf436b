<ng-container *ngIf="!isActiveFilter && !string_filter && baseDatasource.isTableLoaded && baseDatasource.totalItem$.getValue() === 0; else elseBlock" cardBody>
  <app-card-empty icon="{{ iconNone }}" text="Belum terdapat Purchase Order."></app-card-empty>
</ng-container>

<ng-template #elseBlock>
  <app-card [cardBodyClasses]="'pt-2'" [header]="true">
    <ng-container cardHeader>
      <!-- Search Input -->
      <app-input-search
        (actionFilter)="onSearch($event)"
        [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
        [placeholder]="'Cari PO ID / nama distributor'"
        [value]="string_filter"
      ></app-input-search>
      <!-- Filter -->
      <div class="ms-auto position-relative" data-kt-customer-table-toolbar="base">
        <app-filter-table
          (actionClick)="onClickFilter()"
          (actionReset)="handleResetFilter()"
          [isActiveFilter]="isActiveFilter"
          [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
          [isOpenFilter]="isOpenFilter"
        >
          <div class="menu menu-sub menu-sub-dropdown w-300px w-md-350px show filter-body" id="kt-toolbar-filter">
            <div class="px-7 py-5">
              <div class="fs-4 text-dark fw-bold">Filter</div>
            </div>
            <div class="separator border-gray-200"></div>

            <form (ngSubmit)="handleSubmitFilter()" [formGroup]="filterForm" [ngClass]="'w-100'" class="form">
              <div class="px-7 py-5">
                <div *ngIf="showFilterStatus" class="mb-10">
                  <label class="form-label fs-5 mb-6">Status:</label>
                  <div class="filter-pills">
                    <mat-chip-listbox #filterPillsBox (change)="handlePOStatusChange($event)" multiple>
                      <ng-container formArrayName="status">
                        <mat-chip-option *ngFor="let item of filterPills" [value]="item" class="chips">
                          <div class="d-flex align-items-center justify-content-between">
                            <span>{{ item }}</span>
                            <span class="custom-x-icon"></span>
                          </div>
                        </mat-chip-option>
                      </ng-container>
                    </mat-chip-listbox>
                  </div>
                </div>
                <div class="mb-10">
                  <label class="form-label fs-5 mb-6">Tanggal Expired:</label>
                  <app-date-range-picker [formGroup]="filterForm" [nameEnd]="'exp_end_date'" [nameStart]="'exp_start_date'"></app-date-range-picker>
                </div>
                <div class="d-flex justify-content-end">
                  <button #btnReset (click)="handleResetFilter()" class="btn btn-light btn-active-light-primary me-4" type="reset">Reset</button>
                  <button class="btn btn-primary" type="submit">Terapkan</button>
                </div>
              </div>
            </form>
          </div>
        </app-filter-table>
      </div>
    </ng-container>

    <ng-container cardBody>
      <ng-container *ngIf="(isActiveFilter || string_filter) && baseDatasource.isTableLoaded && baseDatasource.totalItem$.getValue() === 0; else elseFilterBlock" cardBody>
        <app-card-empty icon="{{ iconNone }}" text="Belum terdapat Purchase Order."></app-card-empty>
      </ng-container>

      <ng-template #elseFilterBlock>
        <div class="table-responsive">
          <table (matSortChange)="sortTable($event)" [dataSource]="baseDatasource" class="table w-100 gy-5 table-row-bordered align-middle" mat-table matSort>
            <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
              <!-- COLUMN HEADER -->
              <ng-container *ngIf="tableColumn.isSortable; else notSortable">
                <th *matHeaderCellDef [arrowPosition]="'after'" [mat-sort-header]="tableColumn.key" class="min-w-125px px-3" mat-header-cell style>
                  <ng-container>
                    {{ tableColumn.title }}
                  </ng-container>
                </th>
              </ng-container>
              <ng-template #notSortable>
                <th
                  *matHeaderCellDef
                  [class.min-w-200px]="tableColumn.key !== 'actions' && tableColumn.key !== 'id'"
                  [ngClass]="{
                    'min-w-70px text-center': tableColumn.key === 'actions'
                  }"
                  class="min-w-125px px-3"
                  mat-header-cell
                >
                  <ng-container>
                    {{ tableColumn.title }}
                  </ng-container>
                </th>
              </ng-template>
              <!-- COLUMN DATA -->
              <td *matCellDef="let element" class="px-3">
                <ng-container [ngSwitch]="tableColumn.key">
                  <div *ngSwitchCase="'expirationDate'">
                    <app-table-content [height]="30" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [width]="120">
                      <div>{{ utilities.timeStampToDate(element['expiration_date'], 'dd-MM-yyyy') }}</div>
                      <div>{{ utilities.timeStampToDate(element['expiration_date'], 'HH:mm:ss') }}</div>
                    </app-table-content>
                  </div>
                  <div *ngSwitchCase="'status'">
                    <app-table-content [height]="30" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [width]="120">
                      <span class="badge badge__status {{ 'badge__status--' + element['status_enum'] }}">
                        {{ element[tableColumn.key] }}
                      </span>
                    </app-table-content>
                  </div>
                  <div *ngSwitchCase="'actions'" class="text-center">
                    <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'icon'">
                      <span (click)="handleAction(element)" [inlineSVG]="'./assets/media/icons/ic_task.svg'" class="svg-icon svg-icon-2 cursor-pointer"></span>
                    </app-table-content>
                  </div>
                  <div *ngSwitchDefault>
                    <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                      <span>{{ element[tableColumn.key] }}</span>
                    </app-table-content>
                  </div>
                </ng-container>
              </td>
            </ng-container>
            <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
            <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
          </table>
        </div>
        <div class="d-flex justify-content-between py-4">
          <app-mai-material-bottom-table
            (changePage)="changePageEvent($event)"
            [baseDataTableComponent]="baseDatasource"
            [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
            class="w-100"
          ></app-mai-material-bottom-table>
        </div>
      </ng-template>
    </ng-container>
  </app-card>
</ng-template>
