export const purchaseOrderApi = {
  INIT_CREATE_SO: 'v1/create-sales-order/backoffice/init/create-sales-order/', //:poID
  GET_DETAIL_PO: 'v1/order-detail/back-office/purchase-order/header/', //:poID
  GET_PRODUCT_LIST_DETAIL_PO: 'v1/order-detail/back-office/purchase-order/list-product/', //:poID
  GET_PRICE_SUMMARY_DETAIL_PO: 'v1/order-detail/back-office/purchase-order/price-summary/', //:poID
  GET_PRODUCT_NEED_FULL_FILL: 'v1/order-detail/back-office/purchase-order/list-product/need-full-fill/', //:poID
  GET_LIST_PRODUCT_ORDER: 'v1/create-sales-order/backoffice/init/list-product/', //:poID
  GET_LIST_PRODUCT_PROGRAM_MARKETING: 'v1/create-sales-order/backoffice/list-program-marketing/', //:poID
  GET_INIT_PRICE_SUMMARY: 'v1/create-sales-order/backoffice/initial-price/', //:poID
  CONFIRM_CREATE_SO: 'v1/create-sales-order/backoffice/confirmation/', //:poID
  POST_CREATE_SO: 'v1/create-sales-order/backoffice/', //:poID
};
