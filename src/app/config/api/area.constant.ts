export const areaApi = {
  GET_LIST: 'v1/area/list',
  GET_LIST_SELECT_SCOPE: 'v2/area/backoffice/area/list',
  GET_LIST_ALL: 'v1/area/list-all',
  GET_LIST_SUB_AREA: 'v1/sub-area/',
  GET_DETAIL_HEADER: 'v1/area/detail/header',
  GET_DETAIL_SUBAREA: 'v1/area/detail/sub-areas',

  AREA_UPDATE: 'v1/area',
  POST_AREA_CREATE: 'v2/area/backoffice/create',

  GET_TEAM_LIST: 'v2/area/backoffice/member/list/',

  // get list in create area
  GET_RD_LIST: 'v2/area/backoffice/regional-directors',
  GET_RH_LIST: 'v2/area/backoffice/regional-heads',

  // detail in edit area
  GET_DETAIL_EDIT: 'v1/area/backoffice/detail/update-area',

  // add team to area
  GET_ROLE_BY_AREA: 'v2/area/backoffice/role/', //:areaId
  GET_UNASSINED_USER_BY: 'v2/area/backoffice/user-not-assign/list',
  GET_LIST_SCOPE: 'v2/area/backoffice/scope', // for get subarea - warehouse
  GET_USER_WAREHOUSE_LIST: 'v2/area/backoffice/warehouse',
  ADD_TEAM: 'v2/area/backoffice/add-team/', //:areaId

  //ekspor import
  EKSPOR_IMPORT_TARGET: 'v1/master-target-and-forecast/backoffice/',
};
