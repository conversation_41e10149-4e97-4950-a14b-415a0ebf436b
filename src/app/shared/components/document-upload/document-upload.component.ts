import { AfterViewInit, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { BaseService } from '@services/base-service.service';
import { UtilitiesService } from '@services/utilities.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { IGenericNameUrl } from '@shared/interface/generic';
import { PdfService } from '@services/pdf.service';

@Component({
  selector: 'app-document-upload',
  templateUrl: './document-upload.component.html',
  styleUrls: ['./document-upload.component.scss'],
})
export class DocumentUploadComponent implements OnInit, AfterViewInit {
  @Input() label = 'Dokumen Persetujuan';
  @Input() type?: string | 'FILE' | 'IMAGE';
  @Input() isDisabled = false;

  @Input() required = false;
  @Input() showLabel = true;
  @Input() idx = 0;

  @Input() maxUploadSize = 5;
  @Input() enableDelete = true;
  @Input() data!: IGenericNameUrl | undefined;
  @Input() customColClass = { label: 'col-lg-4', content: 'col-lg-8' };

  @Output() documentOutput = new EventEmitter<IGenericNameUrl>();
  @Output() loadingState = new EventEmitter();

  ALLOWED_EXTENSIONS: string[] = [''];
  EXT_IMAGE = ['image/jpg', 'image/jpeg', 'image/png'];
  EXT_FILE = ['application/pdf'];

  STRING_CONSTANTS = STRING_CONSTANTS;

  fileUrlSubject = new BehaviorSubject<string>('');
  isLoadingSubject = new BehaviorSubject(false);
  isLoading$ = this.isLoadingSubject.asObservable();

  ctaLabelFile = 'Pilih File';
  hasError = false;
  errorMessage = '';

  style = 'Theme1';

  formData: FormData;
  fileData: { file: File; sizeMb: string } | undefined;
  dataSubject = new BehaviorSubject(this.data);

  constructor(private baseService: BaseService, private utils: UtilitiesService, private pdfService: PdfService) {}

  ngOnInit(): void {
    this.initAllowedExtension();
  }

  get UniqueLabelID() {
    const _label = this.label.split(' ').join('');
    return _label + '__' + this.idx;
  }

  onFileChange(e: any) {
    if (!e) return;
    this.hasError = false;
    return this.handleFileChange(e.target.files);
  }

  handleFileChange(e: File[]) {
    const file = e[0];

    if (!file) return;
    if (!this.isAllowedExtension(file) || !file.type) return this.setError('INVALID_FILE_EXTENSION');

    // check file type and set type state
    this.type = this.EXT_IMAGE.includes(file.type) ? 'IMAGE' : this.EXT_FILE.includes(file.type) ? 'FILE' : undefined;

    this.setLoading(true);
    const fileSizeMb = this.fileSizeToMb(file.size);
    if (fileSizeMb > this.maxUploadSize) return this.setError('MAX_FILE_SIZE');

    this.formData = new FormData();
    this.formData.append('file', file, file.name);
    this.formData.append('category', this.type as string);

    this.fileData = { file, sizeMb: `${fileSizeMb} Mb` };
    this.dataReader(file).then(() => this.handleUpload(this.formData));
  }

  async dataReader(e: File) {
    if (!e) return;
    const reader = new FileReader();
    reader.onload = (e) => {
      const target = e.target && e.target.result;
      this.fileUrlSubject.next(target as string);
    };

    reader.readAsDataURL(e);
  }

  handleUpload(formData: FormData) {
    this.baseService.uploadData<any>(formData, { disableModalPopupError: true }).subscribe((res) => {
      this.ctaLabelFile = '';
      if (!res) return;

      if (res.success) {
        const fileName = this.fileData?.file.name as string;
        this.documentOutput.emit({ name: fileName, url: res.data.url });
        this.ctaLabelFile = 'Ganti File';
        this.setFileData(res.data.url);
      }

      if (res.errors) {
        this.handleSwitchErrors(res.errors);
      }

      this.setLoading(false);
    });
  }

  handleSwitchErrors(res: [{ code: number; title: string; message: string }]) {
    switch (res[0].code) {
      case 0:
        this.setError('ERR_INTERNET_DISCONNECTED');
        break;
      default:
        this.setError('ERR_FAILED');
        break;
    }
  }

  handleDownload() {
    this.isLoadingSubject.next(true);

    const fileUrl = this.fileUrlSubject.value ? this.fileUrlSubject.value : URL.createObjectURL(this.fileData?.file as File);
    const fileName = this.fileData?.file.name as string;

    const _download$ = this.pdfService.downloadPdf({ name: fileName, url: fileUrl });
    _download$.subscribe((resp) => {
      if (!resp) return;
      setTimeout(() => this.isLoadingSubject.next(false), 250);
    });
  }

  handleRemoveFile() {
    this.setLoading(true);
    setTimeout(() => {
      this.fileUrlSubject.next('');
      this.documentOutput.emit();

      this.hasError = false;
      this.fileData = undefined;
      this.type = undefined;

      this.dataSubject.next(undefined);
      this.initAllowedExtension();
      this.setLoading(false);
    }, 250);
  }

  setLoading(state: boolean) {
    this.isLoadingSubject.next(state);
    this.loadingState.emit(state);
  }

  setFileData(url: string) {
    this.ctaLabelFile = 'Ganti File';
    return this.fileUrlSubject.next(url);
  }

  setError(type: string) {
    if (!type) return;

    this.hasError = true;
    const errMsg = 'File yang diupload ';
    const errUploadFailed = 'File gagal diupload';

    switch (type) {
      case 'MAX_FILE_SIZE':
        this.errorMessage = `${errMsg} lebih dari ${this.maxUploadSize}Mb`;
        this.ctaLabelFile = 'Ganti File';
        break;

      case 'INVALID_FILE_EXTENSION':
        this.errorMessage = `Format ${errMsg} tidak didukung.`;

        // get extension name only
        let _allowedExtensions = this.ALLOWED_EXTENSIONS.map((item) => {
          return item.split('/')[1];
        });

        this.errorMessage += `<br>Format file yang didukung: ${this.utils.arrayStringJoin(_allowedExtensions, null, ',', true)}`;
        this.ctaLabelFile = 'Ganti File';
        break;

      case 'ERR_INTERNET_DISCONNECTED':
        this.errorMessage = `${errUploadFailed} karena koneksi internet.`;
        break;

      case 'ERR_FAILED':
        this.errorMessage = `${errUploadFailed} karena kesalahan sistem.`;
        break;
    }

    this.setLoading(false);
  }

  initAllowedExtension() {
    if (!this.type) return (this.ALLOWED_EXTENSIONS = [...this.EXT_IMAGE, ...this.EXT_FILE]);
    return (this.ALLOWED_EXTENSIONS = this.type === 'IMAGE' ? ['image/jpg', 'image/jpeg', 'image/png'] : this.type === 'FILE' ? ['application/pdf'] : []);
  }

  isAllowedExtension(file: File) {
    return this.ALLOWED_EXTENSIONS.includes(file.type);
  }

  fileSizeToMb(size: number) {
    return +(size / 1024 ** 2).toFixed(2);
  }

  hasFileData() {
    return !!(this.fileData && !this.utils.isObjectEmpty(this.fileData));
  }

  hasInputData() {
    return !this.utils.isObjectEmpty(this.dataSubject.value as IGenericNameUrl);
  }

  ngAfterViewInit(): void {
    this.dataSubject.next(this.data);
    if (this.hasInputData()) {
      this.setFileData(this.data?.url as string);
    }
  }
}
