<div class="chips d-flex flex-wrap align-items-center animation animation-fade-in" [class.chips--click-select]="config?.clickToSelect">
  <ng-container *ngFor="let chips of chipsList; index as i">
    <ng-container [ngTemplateOutlet]="config?.clickToSelect ? selectToClickTpl : defaultTpl" [ngTemplateOutletContext]="{ data: { chips: chips, index: i } }"></ng-container>
  </ng-container>

  <ng-container *ngIf="isShowToggleMore()">
    <div class="d-flex no-chip justify-content-between cursor-pointer">
      <span (click)="toggleShowMore()" class="fw-bold text-decoration-underline">{{ showMore ? 'Hide' : '+' + getLengthNum() + ' More' }}</span>
    </div>
  </ng-container>

  <ng-template #defaultTpl let-data="data">
    <div
      *ngIf="showMore || data.index < (config?.limit ?? 0)"
      class="rounded-pill chip-{{ data.chips.id }} chip"
      [ngClass]="{ 'chip--disabled chip--unselected': data.chips.disabled }"
      matRipple
    >
      <span class="text-{{ config?.labelTextStyle }}">
        {{ data.chips.name | lowercase
        }}<ng-container *ngIf="data.chips.area || data.chips.sub_area"
          ><span>{{ ', ' + (data.chips.area || data.chips.sub_area | lowercase) }}</span></ng-container
        >
      </span>
      <span
        *ngIf="!data.chips.disabled"
        (click)="handleRemoveChip(data.chips)"
        [inlineSVG]="STRING_CONSTANTS.ICON.IC_CLOSE_MODAL"
        class="svg-icon svg-icon-6 cursor-pointer ms-2"
      ></span>
    </div>
  </ng-template>

  <!-- if using select to click behavior -->
  <ng-template #selectToClickTpl let-data="data">
    <!--    !isSelectedChips(data.chips)-->
    <div
      *ngIf="showMore || data.index < (config?.limit ?? 0)"
      (click)="handleToggleSelectChips(data.chips)"
      class="rounded-pill chip-{{ data.chips.id }} chip cursor-pointer"
      [ngClass]="{ 'chip--disabled': data.chips.disabled, 'chip--unselected': !isSelectedChips(data.chips) }"
      matRipple
    >
      <span class="text-{{ config?.labelTextStyle }}">
        {{ data.chips.name | lowercase
        }}<ng-container *ngIf="data.chips.area || data.chips.sub_area"
          ><span>{{ ', ' + (data.chips.area || data.chips.sub_area | lowercase) }}</span></ng-container
        >
      </span>

      <ng-container *ngIf="!data.chips.disabled">
        <span *ngIf="data.chips.selected" [inlineSVG]="STRING_CONSTANTS.ICON.IC_CLOSE_MODAL" class="svg-icon svg-icon-6 cursor-pointer ms-2"></span>
      </ng-container>
    </div>
  </ng-template>
</div>
