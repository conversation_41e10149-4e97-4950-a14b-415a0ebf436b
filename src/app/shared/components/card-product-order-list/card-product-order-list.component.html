<app-card [cardClasses]="'mt-7'" [header]="true" cardHeaderTitle="{{ title }}" [cardBodyClasses]="'pt-0'">
  <ng-container cardBody class="d-flex flex-wrap flex-sm-nowrap">
    <!--    <ng-content select="[noteViewTpl]"></ng-content>-->

    <ng-container *ngIf="hasProductNotFulfilled()">
      <div class="mb-8">
        <app-note-view-with-detail [data]="productNotFulfilledSubject" />
      </div>
    </ng-container>

    <div class="table-responsive">
      <ng-container [ngTemplateOutlet]="isNoProduct() ? noDataTpl : tableDataTpl"></ng-container>
    </div>
  </ng-container>
</app-card>

<ng-template #tableDataTpl>
  <table [dataSource]="listProduct" class="table w-100 gy-5 table-row-bordered align-middle" mat-table>
    <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
      <!-- COLUMN HEADER -->
      <th *matHeaderCellDef class="min-w-125px px-3" mat-header-cell style>
        {{ tableColumn.title }}
      </th>

      <!-- COLUMN DATA -->
      <td *matCellDef="let element" class="px-3">
        <ng-container [ngSwitch]="tableColumn.key">
          <div *ngSwitchCase="'product_name'">
            <app-table-content [count]="1" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
              <div class="d-flex gap-3">
                <img
                  alt=""
                  class="img-thumbnail shadow-sm border-0"
                  src="{{ element['image_url'] }}"
                  (error)="handleNoImage($event)"
                  style="min-width: 48px; max-width: 48px; max-height: 48px; object-fit: cover; object-position: center"
                />

                <div class="my-auto product-name">
                  <span class="product-list"> {{ element[tableColumn.key] }}</span>
                </div>

                <div *ngIf="element['is_bonus']" class="product-bonus gap-2 my-auto">
                  <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_PROMO" class="svg-icon"></span>
                  <div class="text-primary fw-bold">Produk Hadiah</div>
                </div>
              </div>
            </app-table-content>
          </div>

          <div *ngSwitchCase="'qty_order'">
            <app-table-content [count]="1" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
              <div class="d-flex">
                <ng-container *ngIf="!element['qty_retail_unit']; else elseBlock">
                  <span class="text-gray-600"> {{ element['qty_sale_unit'] }}</span>
                </ng-container>
                <ng-template #elseBlock>
                  <span class="text-gray-600">{{ element['qty_sale_unit'] + ' (' + element['qty_retail_unit'] + ')' }}</span>
                </ng-template>
              </div>
            </app-table-content>
          </div>

          <div *ngSwitchCase="'qty_process'">
            <app-table-content [count]="1" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
              <span class="text-gray-600"> {{ element['qty_process_sale_unit'] }}</span>
            </app-table-content>
          </div>

          <div *ngSwitchDefault>
            <app-table-content [count]="1" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
              <span>{{ element[tableColumn.key] }}</span>
            </app-table-content>
          </div>
        </ng-container>
      </td>
    </ng-container>

    <ng-container matColumnDef="total_text">
      <td mat-footer-cell *matFooterCellDef class="px-3 d-flex gap-3 align-items-center">
        <app-table-content [count]="1" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
          <span class="fw-bolder fs-5 space-total">TOTAL</span>
        </app-table-content>
      </td>
    </ng-container>

    <ng-container matColumnDef="total_order">
      <td mat-footer-cell *matFooterCellDef class="px-3">
        <app-table-content [count]="1" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
          <span class="text-gray-600">{{ (productOrder | async)?.summary?.total_sale_unit ?? '-' }}</span>
        </app-table-content>
      </td>
    </ng-container>

    <ng-container matColumnDef="total_weight">
      <td mat-footer-cell *matFooterCellDef class="px-3">
        <app-table-content [count]="1" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
          <span class="text-gray-600">{{ (productOrder | async)?.summary?.total_delivery_unit ?? '-' }}</span>
        </app-table-content>
      </td>
    </ng-container>

    <ng-container *ngIf="isTypeSO" matColumnDef="total_process">
      <td mat-footer-cell *matFooterCellDef class="px-3">
        <app-table-content [count]="1" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
          <span class="text-gray-600">{{ (productOrder | async)?.summary?.total_process_sale_unit ?? '-' }} </span>
        </app-table-content>
      </td>
    </ng-container>

    <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
    <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
    <tr *matFooterRowDef="tableFooter" mat-footer-row></tr>
  </table>
</ng-template>

<ng-template #noDataTpl><span>Tidak ada data</span></ng-template>
