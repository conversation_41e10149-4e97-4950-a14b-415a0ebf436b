import {Component, Input, OnInit, ViewChild} from '@angular/core';
import {FormControl, FormControlName, FormGroup, FormGroupDirective} from "@angular/forms";
import {ModalConfig} from "@shared/components/modal/modal.interface";
import {STRING_CONSTANTS} from '@config/constants/string.constants';
import {ModalComponent} from "@shared/components/modal/modal.component";

@Component({
  selector: 'app-input-multiple-chips-legacy',
  templateUrl: './input-multiple-chips.component.html',
  styleUrls: ['./input-multiple-chips.component.scss']
})
export class InputMultipleChipsComponent implements OnInit {
  @Input() required: boolean = false;
  @Input() label: string;

  @Input() chipsListData: any[] = [];
  @Input() ctaLabelAddItems: string;
  @Input() ctaLabelOpenModalForm: string;

  @Input() btnStyleOutline: boolean = false;
  @Input() btnStyleDisabled: boolean = false;

  modalFormConfig: ModalConfig;
  @ViewChild('modalForm') private modalFormComponent: ModalComponent;

  valueFormGroup: FormGroup;
  valueFormControl?: FormControl;



  STRING_CONSTANTS = STRING_CONSTANTS;

  constructor(private formGroupDirective: FormGroupDirective,
              private formControlNameDirective: FormControlName) {
  }

  ngOnInit(): void {
    this.valueFormGroup = this.formGroupDirective.form;
    this.valueFormControl = this.formGroupDirective.getControl(this.formControlNameDirective);
  }

  handleOpenModalForm = () => this.modalFormComponent.open();

  handleRemoveSelectData($event: any) {
    let selectValue: any[] = this.valueFormGroup.value;
    selectValue.forEach((value, index) => {
      if (value.id === $event) {
        selectValue.splice(index, 1);
      }
    });
    this.valueFormGroup.setValue(selectValue);
  }
}
