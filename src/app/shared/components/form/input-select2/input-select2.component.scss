label {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  letter-spacing: 0.015em;
  color: #353535;
}

:host {
  ::ng-deep .select2-selection {
    cursor: pointer !important;
    background-color: #F8F8F8 !important;
    font-weight: 400 !important;
    font-size: 12px !important;
    line-height: 18px !important;
    height: 50px !important;
    color: #353535 !important;
    display: flex !important;
    align-items: center !important;
    padding: 0 16px !important;
    border-radius: 6px !important;
    border: 0px !important;
  }

  ::ng-deep .select2-selection__arrow {
    width: 16px !important;
    height: 16px !important;
    position: absolute !important;
    right: 24px !important;
    background-image: url(/assets/media/icons/ic_mini_arrow_down.svg);
    background-position: center !important;

    &:before {
      border-style: none !important;
      border-width: 0px !important;
    }
  }

  ::ng-deep .select2-dropdown {
    transform-origin: 50% bottom 0px !important;
    font-size: 14px !important;
    opacity: 1 !important;
    transform: scaleY(1) !important;
    max-width: 280px !important;
    overflow: auto !important;
    -webkit-overflow-scrolling: touch !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    max-height: 256px !important;
    min-width: 100% !important;
    border-radius: 4px !important;
    outline: 0 !important;
    background: white !important;
    box-shadow: 0px 2px 4px -1px rgb(0 0 0 / 20%), 0px 4px 5px 0px rgb(0 0 0 / 14%), 0px 1px 10px 0px rgb(0 0 0 / 12%) !important;
    border: 0px !important;

    .select2-search .select2-search__field {
      border: 1px solid #d1d1d1 !important;
      outline: none !important;
      border-radius: 5px !important;
    }

    .select2-results__option {
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      line-height: 48px !important;
      height: 48px !important;
      padding: 0 16px !important;
      text-align: left !important;
      text-decoration: none !important;
      position: relative !important;
      cursor: pointer !important;
      outline: none !important;
      display: flex !important;
      font-family: inherit !important;
      flex-direction: row !important;
      max-width: 100% !important;
      box-sizing: border-box !important;
      align-items: center !important;
      -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;
    }

    .select2-results__option--highlighted {
      background: #f4f4f4 !important;
      color: #000 !important;
    }
  }

  .list-gudang ::ng-deep {
    .select2-dropdown {
      min-height: 300px;
    }

    .select2-dropdown .select2-results__option {
      flex-direction: initial !important;
      line-height: normal !important;
      height: auto !important;
    }
  }
}
