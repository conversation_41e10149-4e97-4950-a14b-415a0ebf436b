<div *ngIf="valueFormGroup && valueFormControl" class="d-flex flex-wrap justify-content-between align-items-center mb-6">
  <label *ngIf="label" class="col-12 col-lg-4 col-form-label {{ required ? 'required' : '' }}" for="{{ controlName }}">{{ label }}</label>
  <div [formGroup]="valueFormGroup" class="col-12 col-lg-8 {{ class }}">
    <select2
      (update)="handleChangeValue($event)"
      [data]="options"
      [disabled]="disableSelect"
      [templates]="optionTemplate"
      [resultMaxHeight]="maxHeight"
      class="h-48"
      displaySearchStatus="default"
      minCountForSearch="3"
      noResultMessage="Tidak Ada Data"
      placeholder="{{ placeholder }}"
      [value]="valueFormControl.value"
    >
      <ng-template #optionTemplate let-label="label" let-labelExtra="labelExtra">
        <div class="d-flex flex-wrap flex-column py-6">
          <span [class.fw-bold]="!!labelExtra">{{ label }}</span>
          <span *ngIf="labelExtra" class="mt-4 text-capitalize text-gray-700">{{ labelExtra.toLowerCase() }}</span>
        </div>
      </ng-template>
    </select2>
    <mat-error *ngIf="isNotValidAndDirty && hasErrors"> {{ controlName }} is required</mat-error>
  </div>
</div>
