import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { IGenericNameUrl } from '@shared/interface/generic';
import { BehaviorSubject, forkJoin } from 'rxjs';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { ProgramMarketingService } from '../../../pages/sales-marketing/program-marketing/program-marketing.service';
import { FileService } from '@services/file.service';

interface IDocumentData extends IGenericNameUrl {
  size?: number | null;
}

@Component({
  selector: 'app-document-preview',
  templateUrl: './document-preview.component.html',
  styleUrls: ['./document-preview.component.scss'],
})
export class DocumentPreviewComponent implements OnInit, OnChanges {
  @Input() data: IDocumentData[];
  @Input() customClass?: any;
  isLoadingSubject = new BehaviorSubject(false);

  constructor(private fileService: FileService, private promagService: ProgramMarketingService) {}

  ngOnInit() {
    this.initMapData();
  }

  initMapData() {
    this.getFileSize();
  }

  getFileSize() {
    if (!this.data.length) return;
    this.isLoadingSubject.next(true);
    const _requests = this.data.map((item) => this.fileService.getFileSize(item.url));
    forkJoin(_requests).subscribe((res) => {
      this.data = this.data.map((item, index) => ({
        ...item,
        size: res[index] ? this.fileService.convertBytesToMb(res[index] as number) : null,
      }));
      this.isLoadingSubject.next(false);
    });
  }

  handleDownload(doc: IDocumentData) {
    this.isLoadingSubject.next(true);
    this.fileService.downloadPdfFile({ name: doc.name, url: doc.url }).subscribe((resp) => {
      if (!resp) return;
      this.isLoadingSubject.next(false);
    });
  }

  getType = (doc: IGenericNameUrl) => this.promagService.getTypeDoc(doc.url);

  ngOnChanges(changes: SimpleChanges) {
    if (changes && changes.data && !changes.data.firstChange) this.initMapData();
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
