import { Component } from '@angular/core';

@Component({
  selector: 'app-skeleton-card-checklist',
  styleUrls: ['./skeleton-card-checklist.component.scss'],
  template: `
    <div class="card shadow-xs mb-7">
      <div class="card-header">
        <div class="d-flex align-items-center w-100 min-h-40px">
          <div class="w-75">
            <app-skeleton-text [type]="'text'" [fluidSize]="true" [height]="20"></app-skeleton-text>
          </div>
        </div>
      </div>
      <div class="card-body">
        <div class="d-flex flex-wrap w-100 align-items-center">
          <div class="w-60">
            <app-skeleton-text [type]="'text'" [fluidSize]="true"></app-skeleton-text>
          </div>
          <div class="ms-auto min-w-40px">
            <app-skeleton-text [type]="'icon'" [width]="28" [height]="28"></app-skeleton-text>
          </div>
        </div>
        <div class="d-flex flex-wrap w-100 align-items-center my-5">
          <div class="w-50">
            <app-skeleton-text [type]="'text'" [fluidSize]="true"></app-skeleton-text>
          </div>
          <div class="ms-auto min-w-40px">
            <app-skeleton-text [type]="'icon'" [width]="28" [height]="28"></app-skeleton-text>
          </div>
        </div>
        <div class="d-flex flex-wrap w-100 align-items-center">
          <div class="w-25">
            <app-skeleton-text [type]="'text'" [fluidSize]="true"></app-skeleton-text>
          </div>
          <div class="ms-auto min-w-40px">
            <app-skeleton-text [type]="'icon'" [width]="28" [height]="28"></app-skeleton-text>
          </div>
        </div>
      </div>
    </div>
  `,
})
export class SkeletonCardChecklistComponent {}
