<ng-container *ngIf="openTextLabel; else imageOpenLabel">
  <span (click)="handleAction()" class="text-primary cursor-pointer fw-bold text-decoration-underline">{{ actionLabel }}</span>
</ng-container>

<ng-template #imageOpenLabel>
  <div class="use-img-preview">
    <div [class]="'img-container cursor-pointer h-' + height?.toString() + 'px w-auto'" [class.justify-content-start]="!centerImage" (click)="handleAction()">
      <img src="{{ images?.length ? (images ? images[0] : null) : '' }}" alt="..." class="mw-100" (error)="handleNoImage($event)" />
    </div>
  </div>
</ng-template>

<app-modal #modalImage [modalConfig]="modalImageConfig" [modalOptions]="modalImageOptions" [modalClass]="'modal-image-preview'">
  <div cardBody class="d-flex w-100 justify-content-between flex-column">
    <h1 class="text-white">{{ titleModal }}</h1>
    <div class="my-4">
      <swiper [config]="config" class="swiper-main" #swiperCarousel>
        <ng-template swiperSlide *ngFor="let img of images; index as i">
          <div class="image-preview">
            <div cdkDrag class="image-preview-draggable" [style]="currentZoom < 2 ? 'pointer-events: none' : ''" id="imagePreview_{{ i }}">
              <img
                src="{{ img }}"
                class="img-fluid"
                alt="img-preview-{{ i }}"
                (error)="handleNoImage($event)"
                style="{{ activeSlideIndex === i ? ' transform: scale(' + currentZoom + ') rotate(' + currentRotation + 'deg) ' : '' }}"
              />
            </div>
          </div>
        </ng-template>
      </swiper>

      <!--  ACTIONS: -->
      <div class="image-preview-actions d-flex align-items-center justify-content-center gap-5 my-4">
        <ng-container *ngFor="let act of imagePreviewActions">
          <span class="cursor-pointer" [inlineSVG]="act.icon" (click)="act.action()"></span>
        </ng-container>
      </div>
    </div>
  </div>
</app-modal>
