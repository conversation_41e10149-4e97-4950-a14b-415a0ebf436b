import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-card',
  templateUrl: './card.component.html',
  styleUrls: ['./card.component.scss'],
})
export class CardComponent implements OnInit {
  @Input() header = false;
  @Input() border = false;
  @Input() borderHeader = false;
  @Input() borderHeaderNotFull = false;
  @Input() cardClasses: string;
  @Input() cardHeaderClasses: string;
  @Input() cardBodyClasses: string;
  @Input() cardHeaderTitle: string;
  @Input() titleInBody = false;

  constructor() {}

  ngOnInit(): void {}
}
