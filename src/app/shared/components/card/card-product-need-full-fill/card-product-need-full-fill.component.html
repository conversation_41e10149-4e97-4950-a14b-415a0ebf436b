<app-card>
  <ng-container cardBody class="d-flex flex-wrap flex-sm-nowrap">
    <div *ngIf="tableColumns.length" class="table-responsive">
      <table [dataSource]="listProduct" class="table w-100 gy-5 table-row-bordered align-middle" mat-table>
        <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
          <!-- COLUMN HEADER -->
          <ng-container>
            <th *matHeaderCellDef class="min-w-125px px-3" mat-header-cell style>
              {{ tableColumn.title }}
            </th>
          </ng-container>

          <!-- COLUMN DATA -->
          <td *matCellDef="let element" class="px-3">
            <ng-container [ngSwitch]="tableColumn.key">
              <div *ngSwitchCase="'product_name'">
                <app-skeleton-text [isLoading]="isLoading" [width]="180">
                  <div class="d-flex gap-3">
                    <img (error)="handleNoImage($event)" alt="" class="img-product-card img-thumbnail shadow border-0" src="{{ element['image_url'] }}" />

                    <div class="my-auto product-name">
                      <span class="product-list"> {{ element[tableColumn.key] }}</span>
                    </div>

                    <div *ngIf="element['is_bonus']" class="product-bonus gap-2 my-auto">
                      <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_PROMO" class="svg-icon"></span>
                      <div class="text-primary fw-bold">Produk Hadiah</div>
                    </div>
                  </div>
                </app-skeleton-text>
              </div>

              <div *ngSwitchCase="'qty_sale_unit'">
                <app-skeleton-text [isLoading]="isLoading" [width]="180">
                  <div class="d-flex">
                    <span class="text-gray-600"> {{ element['qty_sale_unit'] + ' (' + element['qty_retail_unit'] + ')' }}</span>
                  </div>
                </app-skeleton-text>
              </div>

              <div *ngSwitchCase="'total_price'">
                <app-skeleton-text [isLoading]="isLoading" [width]="180">
                  <span>{{ utilities.toRupiah(element[tableColumn.key]) }}</span>
                </app-skeleton-text>
              </div>

              <div *ngSwitchDefault>
                <app-skeleton-text [isLoading]="isLoading" [width]="180">
                  <span>{{ element[tableColumn.key] }}</span>
                </app-skeleton-text>
              </div>
            </ng-container>
            <!--Note for product-->
            <ng-container *ngIf="element['note'] && !element['description_bonus']" [ngSwitch]="tableColumn.key">
              <div *ngSwitchCase="'product_name'">
                <div class="text-primary fs-8 fw-bold mt-6">Catatan:</div>
                <!--                <div class="fs-8">Estimasi akan tersedia dalam {{ utilities.formatEpochToDate(element['date_estimation'], 'dd MMMM yyyy') }}</div>-->
                <div class="fs-8">{{ element['note'] }}</div>
              </div>
              <div *ngSwitchDefault>
                <div class="text-primary fs-8 fw-bold mt-6">
                  <br />
                </div>
                <div class="fs-8">
                  <br />
                </div>
              </div>
            </ng-container>

            <!--Note for product-->
            <ng-container *ngIf="element['description_bonus']" [ngSwitch]="tableColumn.key">
              <div *ngSwitchCase="'product_name'">
                <div class="text-primary fs-8 fw-bold mt-6">Informasi hadiah belum dikirim:</div>
                <div class="fs-8">
                  {{ element['description_bonus'] }}
                  <span (click)="handleModalDetail(element)" class="text-info cursor-pointer fw-bold text-decoration-underline">Lihat detail SO</span>
                </div>
              </div>
              <div *ngSwitchDefault>
                <div class="text-primary fs-8 fw-bold mt-6">
                  <br />
                </div>
                <div class="fs-8">
                  <br />
                </div>
              </div>
            </ng-container>
          </td>
        </ng-container>
        <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
        <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
      </table>
    </div>
  </ng-container>
</app-card>

<app-modal #modalDetailSO [modalConfig]="modalConfigSO" [modalOptions]="{ size: 'lg' }">
  <div *ngFor="let item of salesOrderCode" class="mb-3">
    <div>{{ item.so_code }}<span class="mx-3">:</span>{{ item.value }}</div>
  </div>
</app-modal>
