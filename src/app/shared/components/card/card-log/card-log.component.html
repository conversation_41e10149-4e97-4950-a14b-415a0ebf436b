<ng-container *ngIf="baseDatasourceLog.isTableLoaded && baseDatasourceLog.totalItem$.getValue() === 0; else elseBlock"
              cardBody>
  <app-card-empty [text]="emptyStateText" icon="{{ iconNone }}"></app-card-empty>
</ng-container>

<!--'Tidak terdapat data log retailer.'-->

<ng-template #elseBlock>
  <app-card>
    <div cardBody>
      <ng-container *ngFor="let record of processedDataLog; let first = first; let last = last">
        <div [class.last]="last" [class.latest]="first" class="step">
          <div class="min-w-150px align-self-start">
            <span class="date">{{ utilities.timeStampToDate(record['submission_date'], 'dd-MM-yyyy HH:mm:ss') }}</span>
          </div>

          <div *ngIf="baseDatasourceLog.isFinishLoadingSubject.value; else loadingStepper" class="v-stepper">
            <div class="circle"></div>
            <div class="line"></div>
          </div>

          <ng-template #loadingStepper>
            <app-table-content></app-table-content>
          </ng-template>

          <!--          {{record|json}}-->
          <div class="content row">
            <div class="container">
              <div class="row">
                <div class="col align-self-start">
                  <span class="title">{{ record['title'] }}</span>

                  <!--                  <ng-container *ngTemplateOutlet="record['desc_link'] ? descriptionWithLink : defaultDescription; context: { data: record }"></ng-container>-->
                  <ng-container [ngTemplateOutletContext]="{ data: record }"
                                [ngTemplateOutlet]="record['desc_link'] ? descriptionWithLink : defaultDescription"></ng-container>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
      <!-- end loop data from variable list -->

      <ng-template #descriptionWithLink let-data="data">
        <span class="desc d-flex">
          <span class="me-2">{{ data.desc }}</span>
          <ng-container *ngIf="data['link_param']; else defaultLink">
            <a
              (click)="navigateToDetail(data['desc_link'], data['link_param'])"
              class="text-info cursor-pointer fw-bold text-decoration-underline">
              {{ data['desc_link_label'] }}
            </a>
            <!--            <a [queryParams]="data['link_param']" [routerLink]="data['desc_link']" class="text-info cursor-pointer fw-bold text-decoration-underline" queryParamsHandling="merge">{{-->
            <!--              data['desc_link_label']-->
            <!--            }}</a>-->
            <!--                        <span class="text-info cursor-pointer fw-bold text-decoration-underline"-->
            <!--                              (click)="goToDetail(data['desc_link']); $event.stopPropagation()"-->
            <!--                        >{{data['desc_link_label']}}</span>-->
          </ng-container>
          <ng-template #defaultLink>
            <span [routerLink]="data['desc_link']"
                  class="text-info cursor-pointer fw-bold text-decoration-underline">{{ data['desc_link_label'] }}</span>
          </ng-template>
        </span>
      </ng-template>

      <ng-template #defaultDescription let-data="data">
        <span class="desc d-flex">{{ data.desc }}</span>
        <ng-container *ngIf="data.reasons && data.reasons.length">
          <span class="desc-reason">Alasan Penolakan: {{ generateReason(data.reasons) }}</span>
        </ng-container>
      </ng-template>

      <div class="d-flex justify-content-between py-4">
        <app-mai-material-bottom-table
          (changePage)="changePageEvent($event)"
          [baseDataTableComponent]="baseDatasourceLog"
          [isFinishLoadingSubject]="baseDatasourceLog.isFinishLoadingSubject"
          class="w-100"
        ></app-mai-material-bottom-table>
      </div>
    </div>
  </app-card>
</ng-template>
