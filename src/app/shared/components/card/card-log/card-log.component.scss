.step {
  padding: 10px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}

.v-stepper {
  position: relative;
  top: 3px;
}

.step .circle {
  border-radius: 100%;
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #D1D1D1;
}

.step.latest {
  .circle {
    border-width: 3px;
    background-color: #688238;
    border-color: #D9E5BB;
  }
}

.step.last:not(.latest) .line {
  display: none;
}

.step .line {
  position: absolute;
  border-left: 2px dashed #D1D1D1;
  height: calc(100% + 2px);
  top: 16px;
  left: calc(50% - 1px);
}

.step:last-child .line {
  border-left: 2px solid white;
  z-index: -1;
}

.content {
  margin-left: 16px;
  width: 100%;
}

.title {
  font-weight: 700;
  font-size: 14px;

  color: #353535;
}

.desc,
.desc-reason {
  font-size: 12px;
  color: #808080;
}

.desc { margin-top: 8px; }
.desc-reason { margin-top: 4px; }

.date {
  font-weight: 400;
  font-size: 12px;
  color: #353535;
}
