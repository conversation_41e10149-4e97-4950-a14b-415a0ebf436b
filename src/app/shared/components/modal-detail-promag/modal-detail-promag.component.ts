import {AfterContentChecked, ChangeDetectorRef, Component, Input, OnInit, ViewChild} from '@angular/core';
import {ModalComponent} from '@shared/components/modal/modal.component';
import {ModalConfig} from '@shared/components/modal/modal.interface';
import {BehaviorSubject} from 'rxjs';
import {BaseService} from '@services/base-service.service';
import {UtilitiesService} from '@services/utilities.service';
import {
  EnumLegacyProgramMarketingDiscount,
  EnumLegacyProgramMarketingDiscountString,
  EnumProgramMarketingDiscountCategory,
  EnumProgramMarketingDiscountCategoryString,
  EnumProgramMarketingDiscountType,
  EnumProgramMarketingDiscountTypeString,
  EnumProgramMarketingPOType,
  EnumProgramMarketingPOTypeString,
  EnumProgramMarketingRewardType,
  EnumProgramMarketingType,
} from '../../../pages/sales-marketing/program-marketing/program-marketing.enum';
import {
  DiscountTerm__DiscountProduct,
  ILegacyProgramMarketingDetail__OrderTerm,
  IProgramMarketingDetail,
  IProgramMarketingDetail__RewardTerm
} from "../../../pages/sales-marketing/program-marketing/program-marketing.interface";
import {DomSanitizer} from "@angular/platform-browser";
import {ProgramMarketingService} from "../../../pages/sales-marketing/program-marketing/program-marketing.service";
import {TableColumn} from "@shared/interface/table.interface";

@Component({
  selector: 'app-modal-detail-promag',
  templateUrl: './modal-detail-promag.component.html',
  styleUrls: ['./modal-detail-promag.component.scss'],
})
export class ModalDetailPromagComponent implements OnInit, AfterContentChecked {
  @Input() haveValue: boolean = false;
  @Input() modalTitle!: string;

  data: BehaviorSubject<IProgramMarketingDetail> = new BehaviorSubject<IProgramMarketingDetail>({} as IProgramMarketingDetail);

  tableColumns!: TableColumn[];
  tableDiscountProducts!: DiscountTerm__DiscountProduct[];
  displayedColumns!: string[];

  @ViewChild('modalDetail') private modalDetail: ModalComponent;
  modalDetailConfig: ModalConfig = {
    modalTitle: 'Program Marketing',
    showFooter: false,
    showHeader: false,
    hideCloseButton: () => true,
  };

  get DataValue() {
    return this.data.value;
  }

  set DataValue(value: IProgramMarketingDetail) {
    this.data.next(value);
  }

  get InformationProgram() {
    return this.DataValue.information;
  }

  get ProgramTerm() {
    return this.DataValue.program_term;
  }

  get OrderTermProgram() {
    return this.DataValue.order_term;
  }

  get DiscountTerm() {
    return this.DataValue.discount_term;
  }

  get RewardProgram() {
    return this.DataValue.reward;
  }

  constructor(
    private baseService: BaseService, public utils: UtilitiesService, private ref: ChangeDetectorRef,
    private programMarketingService: ProgramMarketingService, private sanitizer: DomSanitizer
  ) {
  }

  ngOnInit() {
  }

  ngAfterContentChecked() {
    this.initDiscountProductTable();
  }

  initDiscountProductTable() {
    if (!this.InformationProgram) return;
    const _type = this.InformationProgram.program_type_enum;
    if (_type !== EnumProgramMarketingType.DISCOUNT_PRODUCT) return;

    let {discount_type_enum, discount_category, discount_products} = this.DiscountTerm;
    this.tableDiscountProducts = discount_products;

    const renderDiscountKey = () => {
      switch (discount_category) {
        case EnumProgramMarketingDiscountCategory.PERCENTAGE:
          return discount_type_enum === EnumProgramMarketingDiscountType.SET_MAXIMUM_SALES_DISCOUNT ? 'Maksimal Diskon' : 'Diskon';
        default:
          return 'Nominal';
      }
    };

    this.tableColumns = [
      {key: 'variant_name', title: 'PRODUK', isSortable: false},
      {key: 'discount', title: renderDiscountKey(), isSortable: false},
      {key: 'minimum_order', title: 'MINIMAL PEMBELIAN', isSortable: false},
    ];

    this.displayedColumns = this.tableColumns.map((col) => col.key);
    // this.dataDiscountProducts.emit({ tableColumns: this.tableColumns, data: this.tableDiscountProducts });
  }

  getData(url: string) {
    if (this.haveValue) {
      // this.DataValue = this.value;
      return;
    } else {
      this.baseService.getData(url).subscribe((resp: any) => {
        if (resp && resp.success) {
          this.DataValue = resp.data;
        }
      });
    }
  }

  async openModal(value: IProgramMarketingDetail, title: string) {
    this.modalDetailConfig = {
      ...this.modalDetailConfig,
      modalTitle: title,
    };
    this.DataValue = value;
    this.ref.detectChanges();
    return this.modalDetail.open();
  }

  displayRewardItem(reward: IProgramMarketingDetail__RewardTerm, isTotal: boolean = false) {
    if (!reward) return;

    if (reward.mai_product) {
      return reward.mai_product.name + ' (' + reward.mai_product.qty + ' ' + reward.mai_product.unit + ')';
    } else {

    }
    // const reward = orderTerm.reward;
    // if (reward.bonus_product?.length > 0) {
    //   const bonus: string[] = [];
    //   reward.bonus_product.map((value) => {
    //     const qty = isTotal ? value.total_qty_bonus ?? '0 Box' : value.product_qty ?? '0 Box';
    //     bonus.push(value.product_name + ' (' + qty + ')');
    //   });
    //
    //   return bonus.join(', ');
    // } else {
    //   return reward.other_reward;
    // }
    return '';
  }

  renderPOProduct(type: EnumProgramMarketingPOType) {
    let items = '';

    if (type === EnumProgramMarketingPOType.BUNDLING) {
      const _variants = this.OrderTermProgram.bundling.variants;
      _variants.forEach((item) => {
        items += `<span class="d-block mb-1">${item.name} (${item.qty} <span class="text-capitalize">${item.sale_unit}</span>)</span>`;
      });
    }

    if (type === EnumProgramMarketingPOType.ACCUMULATION) {
      const _variants = this.OrderTermProgram.accumulation.variants;
      _variants.forEach((item) => {
        items += `<span class="d-block mb-1">${item.name} </span>`;
      });
    }

    return items;
  }

  renderMinimumBuy(orderTerm: ILegacyProgramMarketingDetail__OrderTerm) {
    let val = '-';

    const {accumulation} = orderTerm;
    Object.keys(accumulation).forEach((item) => {
      const k = item as keyof typeof accumulation;
      if (accumulation[k] || accumulation[k] === 0) {
        const variant = accumulation.variants[0];
        if (item === 'minimum_price') val = this.utils.toRupiah(Number(accumulation[k]));
        else if (item === 'minimum_qty') val = `${this.utils.toThousandConvert(Number(accumulation[k]))} ${variant.sale_unit}`;
        else val = `${this.utils.toThousandConvert(Number(accumulation[k]))} ${variant.delivery_unit}`;
      }
    });

    this.programMarketingService.MinimumBuy = val;
    return val;
  }

  discountPurchaseProducts(data: string[]) {
    if (!data || (data && !data.length)) return '-';
    const _products = data.map((item) => `<span>${item}</span>`).join('');
    // this.dataDiscountPurchase.emit(_products);
    return this.sanitizer.bypassSecurityTrustHtml(_products);
  }

  renderIstMultiplicationType = (val: string | boolean) => {
    if (typeof val === 'boolean') {
      return val ? 'Berlaku Kelipatan' : 'Tidak Berlaku Kelipatan';
    } else return val;
  };


  protected readonly EnumProgramMarketingPOType = EnumProgramMarketingPOType;
  protected readonly EnumProgramMarketingDiscountString = EnumLegacyProgramMarketingDiscountString;
  protected readonly EnumProgramMarketingDiscount = EnumLegacyProgramMarketingDiscount;
  protected readonly EnumProgramMarketingType = EnumProgramMarketingType;
  protected readonly EnumProgramMarketingPOTypeString = EnumProgramMarketingPOTypeString;
  protected readonly EnumProgramMarketingDiscountCategoryString = EnumProgramMarketingDiscountCategoryString;
  protected readonly EnumProgramMarketingDiscountTypeString = EnumProgramMarketingDiscountTypeString;
  protected readonly EnumProgramMarketingRewardType = EnumProgramMarketingRewardType;
}
