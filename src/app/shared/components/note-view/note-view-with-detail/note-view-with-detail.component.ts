import {Component, Input, OnInit, ViewChild} from '@angular/core';
import {STRING_CONSTANTS} from '@config/constants/string.constants';
import {ModalComponent} from "@shared/components/modal/modal.component";
import {ModalConfig} from "@shared/components/modal/modal.interface";
import {UtilitiesService} from "@services/utilities.service";
import {BehaviorSubject} from "rxjs";
import {IUnfinishedProduct, IUnfinishedProductSpm} from "@models/spm.model";

@Component({
	selector: 'app-note-view-with-detail',
	templateUrl: './note-view-with-detail.component.html',
	styleUrls: ['./note-view-with-detail.component.scss']
})
export class NoteViewWithDetailComponent implements OnInit {
	@Input() data: BehaviorSubject<IUnfinishedProduct | IUnfinishedProductSpm | any>;
	@Input() isSpm: boolean = false;

	ASSETS_ICON = STRING_CONSTANTS.ICON;
	modalConfigRejectProduct: ModalConfig = {
		modalTitle: 'Produk Tidak Diproses',
		showFooter: false,
		showHeader: false,
		hideCloseButton: () => false,
	};
	@ViewChild('modalRejectProduct') private modalRejectProduct: ModalComponent;

	constructor(
		public utilities: UtilitiesService,
	) {
	}

	ngOnInit(): void {
	}

	detailRejectProduct() {
		this.modalRejectProduct.open().then();
	}
}
