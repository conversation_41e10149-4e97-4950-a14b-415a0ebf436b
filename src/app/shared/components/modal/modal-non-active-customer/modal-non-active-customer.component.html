<app-modal-form #modalNonActive [modalConfig]="modalConfig" [modalOptions]="modalOptions">
  <ng-container [ngTemplateOutlet]="isLoading.value ? loaderSectionTpl : formTpl"></ng-container>

  <ng-template #loaderSectionTpl>
    <div class="d-flex flex-column justify-content-center align-items-center my-5">
      <mat-spinner></mat-spinner>
    </div>
  </ng-template>

  <ng-template #formTpl>
    <div [formGroup]="form" class="form-group">
      <div class="d-flex align-items-center gap-3">
        <div [inlineSVG]="STRING_CONSTANTS.ICON.IC_INFORMATION_PRIMARY" class="svg-icon svg-icon-24"></div>
        <div class="fw-bold">Alasan Penonaktifan</div>
      </div>
      <div class="my-4">
        <div><PERSON><PERSON><PERSON> pilih alasan penonaktifan Retailer:</div>
        <div *ngFor="let option of ReasonForm.controls; index as i; let last = last" [formArrayName]="'reasons'">
          <div [formGroupName]="i" class="form-check my-6 text-capitalize">
            <fieldset>
              <input
                (click)="onReasonChange(option.value)"
                [formControlName]="'checked'"
                [value]="option.get('checked')?.value"
                class="form-check-input cursor-pointer"
                id="flexCheckDefault-{{ option.get('value')?.value }}"
                type="checkbox"
              />
              <label class="form-check-label cursor-pointer ms-2 text-gray-900 text-capitalize" for="flexCheckDefault-{{ option.get('value')?.value }}">
                {{ option.get('label')?.value | lowercase }}
              </label>
            </fieldset>
          </div>
        </div>
        <div *ngIf="isOtherReason" class="d-flex align-items-center gap-3">
          <textarea
            [formControlName]="'other_reason'"
            [required]="true"
            class="form-control form-control-solid"
            ngDefaultControl
            placeholder="Silakan input alasan penonaktifan"
            rows="4"
          ></textarea>
        </div>
      </div>
      <hr class="my-4" />
      <div class="d-flex align-items-center gap-3">
        <div [inlineSVG]="STRING_CONSTANTS.ICON.IC_DOCUMENT_ORDER" class="svg-icon svg-icon-24"></div>
        <div class="fw-bold">Dokumen Penonaktifan</div>
      </div>
      <div class="mt-4">
        <app-document-upload
          (documentOutput)="handleDocument($event)"
          (loadingState)="handleUploadState($event)"
          [data]="DocumentForm.value"
          [formControlName]="'document'"
          [required]="true"
          [showLabel]="false"
          ngDefaultControl
        ></app-document-upload>
      </div>
    </div>
  </ng-template>

  <app-note-view
    [classNoteView]="'mb-0'"
    [color]="'info'"
    [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION"
    [text]="'Akses Distributor ke platform Maxxi Agri akan dicabut setelah dinonaktifkan.'"
  ></app-note-view>

  <div class="d-flex justify-content-end gap-3" data-modalFooter>
    <button (click)="handleCancel()" class="btn btn-outline min-w-btn btn-outline-gray text-primary">Batal</button>
    <button (click)="onSubmitForm()" [disabled]="validateForm()" class="btn btn-primary px-10 min-w-btn" color="primary" mat-raised-button>
      <span class="text-white">Nonaktifkan Distributor</span>
    </button>
  </div>
</app-modal-form>

<app-modal #modalNonActiveConfirm [modalConfig]="modalConfigConfirm" [modalOptions]="{ size: 'md' }">
  <ng-container [ngTemplateOutlet]="soActiveTpl"></ng-container>

  <ng-template #programCompensation>
    <div class="text-center my-5">Distributor masih memiliki <span class="fw-bold">Program Marketing Kompensasi</span>. Apakah anda yakin menonaktifkan Distributor?</div>
  </ng-template>

  <ng-template #receivableTpl>
    <div class="text-center my-5">Distributor masih memiliki <span class="fw-bold">total piutang sebesar Rp58.000.000.</span> Apakah anda yakin menonaktifkan Distributor?</div>
  </ng-template>

  <ng-template #soActiveTpl>
    <div class="text-center my-5">
      Distributor masih memiliki <span class="fw-bold">total piutang sebesar Rp58.000.000 & Sales Order Aktif.</span>
      Apakah anda yakin menonaktifkan Distributor?
    </div>
    <app-note-view [classNoteView]="'mb-0'" [color]="'info'" [extraContent]="true" [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION">
      <p class="mb-2">List Sales Order Aktif Distributor:</p>
      <ul class="ps-6">
        <li>SO-00100-00001-001 (50 box, Rp 118.200.001,00)</li>
        <li>SO-00100-00006-005 (100 box, Rp 504.083.501,00)</li>
      </ul>
    </app-note-view>
  </ng-template>
</app-modal>

<app-modal #modalResponse [modalConfig]="modalResponseConfig" [modalOptions]="{ size: 'md' }">
  <ng-container>
    <div class="d-flex flex-column justify-content-center align-items-center">
      <span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT"></span>
      <!--      Penonaktifan Distributor diajukan ke Finance karena Distributor memiliki piutang. Distributor dinonaktifkan setelah disetujui Finance.-->
      <!--      Retailer berhasil dinonaktifkan.-->
      <div class="my-5">{{ 'Distributor berhasil dinonaktifkan.' }}</div>
    </div>
    <app-note-view [classNoteView]="'mb-0'" [color]="'info'" [extraContent]="true" [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION">
      <ul>
        <li *ngFor="let title of generateRetailerNonActive()">{{ title }}</li>
      </ul>
    </app-note-view>

    <app-note-view
      *ngIf="isSuccessNonActive()"
      [classNoteView]="'mb-0'"
      [color]="'info'"
      [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION"
      [text]="'Distributor tidak lagi memiliki akses ke Website Distributor'"
    />
  </ng-container>
</app-modal>
