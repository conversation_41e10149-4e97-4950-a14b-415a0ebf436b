<app-modal-form #modalNonActive [modalConfig]="modalConfig" [modalOptions]="modalOptions">
  <ng-container [ngTemplateOutlet]="isLoading.value ? loaderSectionTpl : formTpl"></ng-container>

  <ng-template #loaderSectionTpl>
    <div class="d-flex flex-column justify-content-center align-items-center my-5">
      <mat-spinner></mat-spinner>
    </div>
  </ng-template>

  <ng-template #formTpl>
    <div [formGroup]="form" class="form-group">
      <div class="d-flex align-items-center gap-3">
        <div [inlineSVG]="STRING_CONSTANTS.ICON.IC_INFORMATION_PRIMARY" class="svg-icon svg-icon-24"></div>
        <div class="fw-bold">Alasan Penonaktifan</div>
      </div>
      <div class="my-4">
        <div><PERSON><PERSON><PERSON> pilih alasan penonaktifan {{ customerType }}:</div>
        <div *ngFor="let option of ReasonForm.controls; index as i; let last = last" [formArrayName]="'reasons'">
          <div [formGroupName]="i" class="form-check my-6 text-capitalize">
            <fieldset>
              <input
                (click)="onReasonChange(option.value)"
                [formControlName]="'checked'"
                [value]="option.get('checked')?.value"
                class="form-check-input cursor-pointer"
                id="flexCheckDefault-{{ option.get('value')?.value }}"
                type="checkbox"
              />
              <label class="form-check-label cursor-pointer ms-2 text-gray-900 text-capitalize" for="flexCheckDefault-{{ option.get('value')?.value }}">
                {{ option.get('label')?.value | lowercase }}
              </label>
            </fieldset>
          </div>
        </div>
        <div *ngIf="isOtherReason" class="d-flex align-items-center gap-3">
          <textarea
            [formControlName]="'other_reason'"
            [required]="true"
            class="form-control form-control-solid"
            ngDefaultControl
            placeholder="Silakan input alasan penonaktifan"
            rows="4"
          ></textarea>
        </div>
      </div>
      <hr class="my-4" />
      <div class="d-flex align-items-center gap-3">
        <div [inlineSVG]="STRING_CONSTANTS.ICON.IC_DOCUMENT_ORDER" class="svg-icon svg-icon-24"></div>
        <div class="fw-bold">Dokumen Penonaktifan</div>
      </div>
      <div class="mt-4">
        <app-document-upload
          (documentOutput)="handleDocument($event)"
          (loadingState)="handleUploadState($event)"
          [data]="DocumentForm.value"
          [formControlName]="'document'"
          [required]="true"
          [showLabel]="false"
          ngDefaultControl
        ></app-document-upload>
      </div>
    </div>
  </ng-template>

  <app-note-view
    [classNoteView]="'mb-0'"
    [color]="'info'"
    [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION"
    [text]="'Akses ' + CustomerTitle + ' ke platform Maxxi Agri akan dicabut setelah dinonaktifkan.'"
  ></app-note-view>

  <div class="d-flex justify-content-end gap-3" data-modalFooter>
    <button (click)="handleCancel()" class="btn btn-outline min-w-btn btn-outline-gray text-primary">Batal</button>
    <button (click)="onSubmitForm()" [disabled]="validateForm()" class="btn btn-primary px-10 min-w-btn" color="primary" mat-raised-button>
      <span class="text-white">Nonaktifkan {{ CustomerTitle }}</span>
    </button>
  </div>
</app-modal-form>

<app-modal #modalNonActiveConfirm [modalConfig]="modalConfigConfirm" [modalOptions]="{ size: 'md' }">
  <ng-container>
    <div class="text-center my-5">
      Distributor masih memiliki <span class="fw-bold">{{ textConfirm() }}</span> Apakah anda yakin menonaktifkan Distributor?
    </div>
    <ng-container *ngIf="!!ImpactedDistributor.list_sales_order?.length">
      <app-note-view [classNoteView]="'mb-0'" [color]="'info'" [extraContent]="true" [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION">
        <p class="mb-2">List Sales Order Aktif Distributor:</p>
        <ul class="ps-6 note-view-so">
          <li *ngFor="let so of ImpactedDistributor.list_sales_order">{{ so.code }} ({{ so.qty }} {{ so.sale_unit }} , {{ utilities.toRupiah(so.total_invoice, false) }})</li>
        </ul>
      </app-note-view>
    </ng-container>
  </ng-container>
</app-modal>

<app-modal #modalResponse [modalConfig]="modalResponseConfig" [modalOptions]="{ size: 'md' }">
  <div *ngIf="!responseSubmission" class="d-flex flex-column justify-content-center align-items-center gap-8">
    <mat-spinner></mat-spinner>
    <div>Tunggu Sebentar</div>
  </div>

  <ng-container *ngIf="responseSubmission">
    <div class="d-flex flex-column justify-content-center align-items-center text-center">
      <span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT"></span>
      <div class="my-5">{{ responseSubmission ? responseSubmission?.message : '' }}</div>
    </div>
    <app-note-view *ngIf="!IsDistributor" [classNoteView]="'mb-0'" [color]="'info'" [extraContent]="true" [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION">
      <ul>
        <li *ngFor="let title of generateRetailerNonActive()">{{ title }}</li>
      </ul>
    </app-note-view>

    <app-note-view
      *ngIf="IsDistributor && !isNoImpacted"
      [classNoteView]="'mb-0'"
      [color]="'info'"
      [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION"
      [text]="'Distributor tidak lagi memiliki akses ke Website Distributor'"
    />
  </ng-container>
</app-modal>
