import { Component, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { NgbModal, NgbModalOptions, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';

import { ModalConfig } from './modal.interface';

@Component({
  selector: 'app-modal',
  templateUrl: './modal.component.html',
  styleUrls: ['./modal.component.scss'],
})
/* It's a component that can be used to display a modal */
export class ModalComponent implements OnInit {
  @ViewChild('modal') private modalContent: TemplateRef<ModalComponent>;
  private modalRef: NgbModalRef;

  @Input() public modalConfig: ModalConfig;
  @Input() public modalOptions: NgbModalOptions;
  @Input() modalClass: string;
  @Input() modalTitle: string;

  // @Input() callbackSubmit: () => void;
  @Input() callbackSubmit?: () => Promise<void> | void;
  @Input() callbackCancel?: () => Promise<void> | void;
  @Input() callbackDetail?: () => Promise<void> | void;

  defaultOptions: NgbModalOptions = {
    centered: true,
    backdrop: 'static',
    keyboard: false,
  };

  defaultConfig: ModalConfig = {
    showHeader: true,
    showFooter: true,
  };

  constructor(private modalService: NgbModal) {}

  ngOnInit(): void {
    this.modalConfig = {
      ...this.modalConfig,
      showHeader: this.modalConfig.showHeader ?? this.defaultConfig.showHeader,
      showFooter: this.modalConfig.showFooter ?? this.defaultConfig.showFooter,
    };

    this.modalOptions = {
      ...this.modalOptions,
      centered: this.modalOptions?.centered ?? this.defaultOptions.centered,
      backdrop: this.modalOptions?.backdrop ?? this.defaultOptions.backdrop,
      keyboard: this.modalOptions?.keyboard ?? this.defaultOptions.keyboard,
    };

    // this.modalOptions = typeof this.modalOptions === 'undefined' ? this.defaultOptions : { ...this.modalOptions, ...this.defaultOptions };
  }

  open(): Promise<boolean> {
    return new Promise<boolean>((resolve) => {
      this.modalRef = this.modalService.open(this.modalContent, this.modalOptions);
      this.modalRef.result.then(resolve, resolve);
    });
  }

  async close(): Promise<void> {
    if (this.modalConfig.shouldClose === undefined || (await this.modalConfig.shouldClose())) {
      const result = this.modalConfig.onClose === undefined || (await this.modalConfig.onClose());
      this.modalRef.close(result);
    }
  }

  async dismiss(): Promise<void> {
    if (this.modalConfig.shouldDismiss === undefined || (await this.modalConfig.shouldDismiss())) {
      const result = this.modalConfig.onDismiss === undefined || (await this.modalConfig.onDismiss());
      this.modalRef.dismiss(result);
    }
  }

  async handleSubmit(): Promise<void> {
    const result = this.callbackSubmit === undefined ? await this.dismiss() : await this.callbackSubmit();
    this.modalRef.dismiss(result);
  }

  async handleDetail(): Promise<void> {
    const result = this.callbackDetail === undefined ? await this.dismiss() : await this.callbackDetail();
    this.modalRef.dismiss(result);
  }
}
