<div class="snackbar-components" [style.background-color]="data.color">
  <span [inlineSVG]="data.icon"></span>
  <div class="text-snackbar">{{ data.text }}</div>

  <ng-container *ngIf="data.ctaButton">
    <button class="btn btn-transparent p-0 ms-auto" (click)="snackBarRef.dismissWithAction()">
      <span [style.color]="data.ctaLabelColor">{{ data.ctaLabel }}</span>
    </button>
  </ng-container>
</div>
