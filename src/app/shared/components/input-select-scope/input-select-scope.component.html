<!--<pre>options: {{options|json}}</pre>-->
<div class="d-flex flex-wrap align-items-start">
  <label class="col-12">
    <span [inlineSVG]="icon" class="svg-icon svg-icon-2 me-2"></span>
    <span class="fw-bolder">{{ label }}</span>
  </label>

  <div class="col-12 mt-3">
    <!--   <pre>loading: {{ loadingSubject|async }}</pre>-->
    <div class="position-relative">
      <input
        #trigger="matAutocompleteTrigger"
        [formControl]="searchControl"
        [matAutocomplete]="auto"
        [placeholder]="placeholder"
        [readonly]="disabledInput()"
        class="form-control form-control-solid"
        matInput
        type="text"
      />
      <mat-autocomplete #auto="matAutocomplete" (optionSelected)="onOptionSelected($event)" [displayWith]="displayFn">
        <mat-option *ngFor="let option of optionsSubject | async" [disabled]="option.disabled" [value]="option">
          <span [class]="isEdit.value && boldOption(option.value) ? 'fw-bolder' : ''">{{ option.label }}</span>
        </mat-option>
      </mat-autocomplete>
      <span *ngIf="loadingSubject | async" class="spinner-border spinner-border-sm align-middle m-auto" style="position: absolute; top: 0; bottom: 0; right: 20px"></span>
      <span (click)="trigger.openPanel(); $event.stopPropagation()" [inlineSVG]="STRING_CONSTANTS.ICON.IC_ARROW_MINI_DOWN" class="svg-icon svg-icon-1 cursor-pointer"></span>
    </div>

    <!--  <pre>searchnotfound:  {{ searchNotFound() }}</pre>-->

    <div *ngIf="!loadingSubject.value && searchNotFound()" class="px-2 mt-2">
      <span class="fs-8">Tidak ada data</span>
    </div>

    <div *ngIf="isMultiple" class="mt-4">
      <mat-chip-listbox class="chips">
        <mat-chip *ngFor="let option of selectedOptions" [removable]="false" [value]="option" class="chip">
          <span>{{ option.label }}</span>
          <span
            (click)="removeChip(option)"
            *ngIf="!isEdit.value || (isEdit.value && selectedValue.length > 1)"
            [inlineSVG]="STRING_CONSTANTS.ICON.IC_X"
            class="svg-icon svg-icon-2 cursor-pointer ms-2 cdk-focused"
          ></span>
        </mat-chip>
      </mat-chip-listbox>
    </div>
  </div>
</div>
