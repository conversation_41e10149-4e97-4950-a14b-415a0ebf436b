import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { InputSelectMaterialInterface, ISelectGenericIdName } from '@shared/components/form/input-select-material/input-select-material.interface';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { BaseService } from '@services/base-service.service';
import { BehaviorSubject, debounceTime, of, switchMap, tap } from 'rxjs';
import { MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, MatAutocompleteSelectedEvent, MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { IGenericIdName, IGenericLabelValue } from '@shared/interface/generic';
import { IAreaList } from '@models/area.model';
import { ISubAreaRoot } from '../../../pages/adminsettings/area/area.interface';
import { EnumRole } from '@models/role.model';

@Component({
  selector: 'app-input-select-scope',
  templateUrl: './input-select-scope.component.html',
  styleUrls: ['./input-select-scope.component.scss'],
  providers: [
    {
      provide: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS,
      useValue: {
        overlayPanelClass: 'modal-autocomplete-panel',
      },
    },
  ],
})
export class InputSelectScopeComponent implements OnInit {
  @Input() label = '';
  @Input() icon = '';

  @Input() placeholder = '';
  @Input() endpointUrl = '';
  @Input() isMultiple = true;
  @Input() enumRole: EnumRole;
  @Input() selectedValue: string[] = [];
  @Input() isEdit: BehaviorSubject<boolean> = new BehaviorSubject(false);

  @Input() usePagination = true;
  @Input() disabled = false;

  @Input() type: 'AREA' | 'SUBAREA' | 'WAREHOUSE'; //might need for interface
  @Input() areaId: BehaviorSubject<string> = new BehaviorSubject(''); // NOTE: need for subarea or warehouse for get api
  @Output() selectedValues = new EventEmitter<{ type: 'AREA' | 'SUBAREA' | 'WAREHOUSE'; data: IGenericLabelValue[] }>();

  @ViewChild(MatAutocompleteTrigger) autocomplete: MatAutocompleteTrigger;

  optionsSubject = new BehaviorSubject([] as InputSelectMaterialInterface[]);
  initOptionsSubject = new BehaviorSubject([] as InputSelectMaterialInterface[]);
  selectedOptions: IGenericLabelValue[] = [];

  searchControl = new FormControl();
  loadingSubject = new BehaviorSubject(false);

  currentPage = 0;
  pageSize = 20;
  hasMoreData = true;

  constructor(private baseService: BaseService, private cdr: ChangeDetectorRef) {}

  ngOnInit() {
    if (this.type === 'AREA') {
      this.fetchInitialData();
      this.initSearchControl();
    } else {
      this.handleDataExArea(); // Handle Data Except Data Area
    }
  }

  resetStateSelect() {
    this.optionsSubject.next([]);
    this.initOptionsSubject.next([]);
    this.selectedOptions = [];
    !this.isEdit.value && this.selectedValues.emit({ type: this.type, data: this.selectedOptions });
  }

  handleDataExArea() {
    this.areaId.subscribe((value) => {
      if (!value) return;
      this.resetStateSelect();
      this.fetchInitialData();
      this.initSearchControl();
      this.cdr.detectChanges();
    });
  }

  fetchInitialData() {
    this.loadingSubject.next(true);
    this.fetchSearchOptions().subscribe((data: InputSelectMaterialInterface[]) => {
      if (!this.initOptionsSubject.value.length) {
        this.initOptionsSubject.next(data);
      }
      this.optionsSubject.next(data);

      if (!!this.selectedValue.length) {
        this.selectedOptions = data.filter((val) => this.selectedValue.includes(val.value));
        if (!this.isMultiple && this.selectedValue.length === 1) this.searchControl.setValue(this.selectedOptions[0]);
        this.loadingSubject.next(data && !data.length);
      } else {
        this.loadingSubject.next(data && !data.length);
      }
      this.cdr.detectChanges();
    });

    this.optionsSubject.subscribe((data) => {
      const _roleRdMe = [EnumRole.REGIONAL_DIRECTOR, EnumRole.SALES_STAFF];
      if (this.isEdit.value && _roleRdMe.includes(this.enumRole)) {
        const filteredData = data.flatMap((value) => (!this.selectedValue.includes(value.value) ? [value] : []));
        filteredData.length && filteredData.flatMap((value) => (value.disabled = true));
      }
    });
  }

  initSearchControl() {
    if (this.disabled) return this.searchControl.disable();
    this.searchControl.valueChanges
      .pipe(
        debounceTime(250),
        tap(() => this.loadingSubject.next(true)),
        switchMap((query: string) => {
          if (query && query.length >= 3) {
            // Fetch filtered data based on query
            return this.fetchSearchOptions(query, this.currentPage);
          } else if (!query) {
            // Reset to default data if query is empty
            this.resetState();
            return of(this.initOptionsSubject.value);
          } else {
            if (this.isMultiple) return of([]);
            return this.fetchSearchOptions(query, this.currentPage);
          }
        })
      )
      .subscribe((data) => {
        this.optionsSubject.next(data);
        this.loadingSubject.next(false);
      });
  }

  fetchSearchOptions = (query = '', page = this.currentPage) => this.fetchData(query, page);

  fetchData(query: string, page: number) {
    let url = this.endpointUrl;

    if (this.type === 'SUBAREA' || this.type === 'WAREHOUSE') url += `${this.areaId.value}`;
    if (this.hasSearchKeyword(query)) url += `?string_filter=${query.trim()}`;
    if (this.usePagination && this.currentPage > 0) url += this.hasSearchKeyword(query) ? `&page=${page}` : `?page=${page}`;

    return this.baseService.getData<IAreaList[] | ISubAreaRoot[]>(url).pipe(
      switchMap((resp) => {
        this.hasMoreData = this.usePagination && !!resp?.data.length;

        if (!!resp?.data.length) {
          const data = resp?.data as IGenericIdName[];
          const mappedData = this.type !== 'WAREHOUSE' ? this.mapDataToOptions(data) : (data as unknown as InputSelectMaterialInterface[]);
          this.optionsSubject.next(this.usePagination ? [...this.optionsSubject.value, ...mappedData] : mappedData);
        }

        return of(this.optionsSubject.value);
      })
    );
  }

  mapDataToOptions(data: IGenericIdName[]) {
    return data.map((item) => {
      let inputSelector = new ISelectGenericIdName();
      inputSelector.setOptions(item);
      return inputSelector;
    });
  }

  onOptionSelected(e: MatAutocompleteSelectedEvent) {
    if (this.disabled) return;

    const selectedOption = e.option.value as IGenericLabelValue;
    if (!this.selectedOptions.some((option) => option.value === selectedOption.value)) {
      if (this.isMultiple) {
        this.selectedOptions.push(selectedOption);
      } else {
        this.selectedOptions = [selectedOption];
      }
    }

    // emit selected values
    this.selectedValues.emit({ type: this.type, data: this.selectedOptions });

    // clear input after selection
    this.isMultiple && this.searchControl.setValue('');
    // this.fetchInitialData();
  }

  removeChip(option: InputSelectMaterialInterface): void {
    this.selectedOptions = this.selectedOptions.filter((opt) => opt.value !== option.value);
    this.selectedValues.emit({ type: this.type, data: this.selectedOptions });
  }

  resetState() {
    this.currentPage = 0;
    this.hasMoreData = true;
  }

  displayFn(option: InputSelectMaterialInterface) {
    // console.log('displayFn ', option);
    // return option?.label;
    return option?.label ?? '';
  }

  searchNotFound = () => !this.optionsSubject.value.length && this.hasSearchKeyword(this.searchControl.value) && this.searchControl.value.length >= 3;

  hasSearchKeyword = (q: string | InputSelectMaterialInterface) => {
    // return this.isMultiple && !!(q && q.trim());
    return !!(q && typeof q === 'string' && q.trim());
  };

  boldOption = (id: string): boolean => this.selectedOptions.flatMap((val) => val.value).includes(id);

  disabledInput() {
    if (this.type === 'SUBAREA' || this.type === 'WAREHOUSE') {
      return this.disabled || !this.areaId.value;
    } else {
      return this.disabled;
    }
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
  protected readonly length = length;
}
