import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { STRING_CONSTANTS } from "@config/constants/string.constants";
import { BehaviorSubject } from "rxjs";
import { UploadService } from "@services/upload.service";
import { FormArray, FormBuilder, FormControl, FormGroup } from "@angular/forms";

@Component({
  selector: "app-multiple-image-upload",
  templateUrl: "./multiple-image-upload.component.html",
  styleUrls: ["./multiple-image-upload.component.scss"]
})
export class MultipleImageUploadComponent implements OnInit {
  @Input() idx: string | number = "";
  @Input() maxLimit: number = 5;
  @Input() data?: { id: string, images: string[] };

  @Output() fileUrl = new EventEmitter();
  @Output() removeFile = new EventEmitter();

  STRING_CONSTANTS = STRING_CONSTANTS;

  selectedFiles: FileList | any;
  // previewSubject: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);
  // preview$?: Observable<string[]>;

  uploadForm: FormGroup;
  isLoading = new BehaviorSubject<boolean>(false);

  constructor(
    private uploadService: UploadService,
    private fb: FormBuilder
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  initForm(): void {
    this.uploadForm = this.fb.group({
      fileImages: this.fb.array([])
    });

    if (this.data) {
      // data to fileImages format
      this.data.images.forEach( (_item: any) => {
        this.createImage({
          id: this.data?.id as string,
          url: _item
        });
      });

      this.fileUrl.emit( this.fileImages.value );
    }
  }

  get fileImages() {
    return this.uploadForm.get("fileImages") as FormArray;
  }

  createImage(img: { id: string|number, url: string }) {
    const _image = new FormControl(img);
    (<FormArray>this.uploadForm.get("fileImages")).push(_image);
  }

  handleDropFiles(evt: any) {
    const _currentFileImagesNum = this.fileImages.length;

    if (evt.length > this.maxLimit) {
      return setTimeout(() =>
        this.uploadService.errorMaxFileUpload("Upload Maksimal hanya 5 file!"), 500
      );
    }

    if ((evt.length + _currentFileImagesNum) > this.maxLimit) {
      return setTimeout(() =>
        this.uploadService.errorMaxFileUpload("Upload Maksimal hanya 5 file!"), 500
      );
    }

    return this.uploadFiles(evt);
  }

  onFileSelected(evt: any) {
    const _currentFileImagesNum = this.fileImages.length;
    this.selectedFiles = evt.target.files;

    if (this.selectedFiles.length > this.maxLimit) {
      return setTimeout(() =>
        this.uploadService.errorMaxFileUpload("Upload Maksimal hanya 5 file!"), 500
      );
    }

    if ((this.selectedFiles.length + _currentFileImagesNum) > this.maxLimit) {
      return setTimeout(() =>
        this.uploadService.errorMaxFileUpload("Upload Maksimal hanya 5 file!"), 500
      );
    }

    return this.uploadFiles(this.selectedFiles);
  }

  uploadFiles(files: FileList) {
    if (!files) { return; }

    const _numFiles = files.length;
    if (_numFiles > this.maxLimit) {
      return this.uploadService.errorMaxFileUpload("Upload Maksimal hanya 5 file!");
    }

    for (let i = 0; i < _numFiles; i++) {
      this.handleUpload(this.idx, files[i]);
    }
  }

  handleUpload(id: number|string, file: File) {
    this.isLoading.next(true);
    this.uploadService.upload(file).subscribe(
      resp => {
        if (resp && resp.success) {
          const { url } = resp.data;
          this.createImage({ id, url } );
          this.fileUrl.emit( this.fileImages.value );
          this.isLoading.next(false);
        }
      }
    );
  }

  onDeleteFileImage( index: number, img: { id: string, url: string } ) {
    this.fileImages.removeAt( index );
    this.removeFile.emit( img );
  }
}
