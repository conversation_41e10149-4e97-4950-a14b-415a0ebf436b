import {Injectable} from '@angular/core';
import {BaseService} from "@services/base-service.service";
import {SwallService} from "@services/swall.service";

@Injectable({
  providedIn: 'root'
})
export class UploadService {

  constructor(
    private baseService: BaseService,
    private swallService: SwallService
  ) {
  }

  upload(file: File) {
    const formData = new FormData();

    formData.append('file', file, file?.name);
    formData.append('category', 'IMAGE');

    return this.baseService.uploadData<any>(formData);
  }

  errorMaxFileUpload(msg: string) {
    return this.swallService.GetAlert({text: msg});
  }
}
