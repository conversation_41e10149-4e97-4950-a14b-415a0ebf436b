import { Injectable } from '@angular/core';
import { BaseService } from '@services/base-service.service';
import { API } from '@config/constants/api.constant';
import { map } from 'rxjs/operators';
import { IListPesticide, IListPlantType } from '../pages/products/Products.interface';
import { ISelectPesticide, ISelectPlantType } from '@shared/components/form/input-select/input-select.interface';
import { shareReplay } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ProductBrandService {
  constructor(private baseService: BaseService) {}

  syncBrand() {
    return this.baseService.getData<boolean>(API.SYNC_BRAND).pipe(map((resp) => resp));
  }

  getListPlantType() {
    return this.baseService.getData<IListPlantType[]>(API.GET_PLANT_TYPE).pipe(map((res) => res));
  }

  mapInputSelectPlantType(data: IListPlantType[]) {
    return data.map((item) => {
      let inputSelector = new ISelectPlantType();
      inputSelector.setPlantTypeOptions(item);
      return inputSelector;
    });
  }

  getListPestisida() {
    return this.baseService.getData<IListPesticide[]>(API.PESTICIDE_TYPE_LIST).pipe(
      shareReplay(),
      map((res) => res)
    );
  }

  mapInputSelectPesticide(data: IListPesticide[]) {
    return data.map((item) => {
      let inputSelector = new ISelectPesticide();
      inputSelector.setPesticideOptions(item);
      return inputSelector;
    });
  }
}
