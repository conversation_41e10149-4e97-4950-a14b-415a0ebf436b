import { Injectable } from '@angular/core';
import { format } from 'date-fns';
import { TableColumn } from '@shared/interface/table.interface';
import { ICardDetailBody } from '@models/card.model';
import { STRING_CONSTANTS } from '@config/constants/string.constants';

@Injectable({
  providedIn: 'root',
})
export class UtilitiesService {
  constructor() {}

  arrayStringJoin(arr: any[], key: string | any, separator: string = '', spaceBefore = false): string {
    if (!arr) {
      return '';
    }

    separator = !separator ? ',' : separator;

    const space = spaceBefore ? ' ' : '';
    let arrayList = !key ? arr : [];
    let list;

    if (key) {
      arrayList = arr.map((item: any) => item[key]);
    }

    list = arrayList.join(`${separator}${space}`);
    return list;
  }

  toCapitalize(text: string) {
    if (!text) {
      return;
    }
    const arr = text.split(' ');
    for (let i = 0; i < arr.length; i++) {
      arr[i] = arr[i].charAt(0).toUpperCase() + arr[i].slice(1);
    }
    return arr.join(' ');
  }

  timeStampToDate(date: string | undefined | Date, formatTime: string) {
    if (!date) {
      return;
    }

    return format(new Date(date.toString()), formatTime);
  }

  toRupiah(value?: number, decimal?: boolean, symbol: boolean = true): string {
    let validate_value = value ?? 0;

    if (typeof decimal === 'boolean') {
      validate_value = value ? Math.trunc(value) : 0;
    }

    const options = symbol
      ? {
          style: 'currency',
          currency: 'IDR',
          minimumFractionDigits: 2,
        }
      : { minimumFractionDigits: 2 };

    const parse = new Intl.NumberFormat('id-ID', options).format(validate_value);

    return parse.toString();
  }

  convertTwoDigits(value: number): string {
    return parseFloat(String(value)).toFixed(2);
  }

  toThousandConvert = (value: number | string): string => {
    let result: number | string;
    if (typeof value === 'string') {
      const data = parseFloat(value);
      const main: string = this.convertTwoDigits(data);
      result = new Intl.NumberFormat('id-ID').format(Number(main));
    } else {
      const main = this.convertTwoDigits(value);
      result = new Intl.NumberFormat('id-ID').format(Number(main));
    }
    return result;
  };

  downloadFile<T>(data: T[], filename = 'data', headerList: string[]) {
    let csvData = this.ConvertToCSV(data, headerList);
    let blob = new Blob(['\ufeff' + csvData], { type: 'text/csv;charset=utf-8;' });

    let dwldLink = document.createElement('a');
    let url = URL.createObjectURL(blob);

    let isSafariBrowser = navigator.userAgent.indexOf('Safari') != -1 && navigator.userAgent.indexOf('Chrome') == -1;
    if (isSafariBrowser) {
      //if Safari open in new window to save file with random filename.
      dwldLink.setAttribute('target', '_blank');
    }

    dwldLink.setAttribute('href', url);
    dwldLink.setAttribute('download', filename + '.csv');
    dwldLink.style.visibility = 'hidden';

    document.body.appendChild(dwldLink);
    dwldLink.click();
    document.body.removeChild(dwldLink);
  }

  ConvertToCSV<T>(objArray: T[], headerList: string[]) {
    let array = typeof objArray != 'object' ? JSON.parse(objArray) : objArray;
    const mappedData = [];

    mappedData.push(headerList);
    array.forEach((item: any) => mappedData.push(Object.values(item)));

    let csvContent = '';
    mappedData.forEach((row) => {
      csvContent += row.join(',') + '\n';
    });

    return csvContent;
  }

  toStringWithUnitType(data: string) {
    const number: number = parseInt(data.split(' ')[0]);
    const str: string = data.split(' ')[1];
    const resultNumber: string = new Intl.NumberFormat('id-ID', { maximumSignificantDigits: 3 }).format(number);
    return `${resultNumber} ${str}`;
  }

  toNumberFormat(data: number) {
    const resultNumber: string = new Intl.NumberFormat('id-ID', { maximumSignificantDigits: 3 }).format(data);
    return resultNumber;
  }

  SubMenuBreadcrumbTitles(status: string | null, SubMenu: any) {
    let title = null;
    Object.entries(SubMenu).find(([key, value]) => {
      if (status == key) {
        title = value;
        return true;
      }
      return false;
    });

    return title;
  }

  // Privilage
  getStatusDetailPrivilage(status: string | null | undefined, DetailEnum: any) {
    let key_name = null;
    if (status) {
      Object.entries(DetailEnum).find(([key, value]) => {
        if (status == key) {
          key_name = value;
        }
      });
    }
    return key_name;
  }

  privilegeTableColumns(privilege: boolean, TableColumns: TableColumn[]) {
    let tableColumns = TableColumns;
    const _hasActions = !!tableColumns.filter((item) => item.key === 'actions').length;

    if (privilege && !_hasActions) {
      tableColumns.push({ key: 'actions', title: 'Actions' });
    }

    return tableColumns;
  }

  privilegeConfidentialDocuments(privileges: { name: string }[], confidentials: Array<any>) {
    let documents: { label: string; value: string }[] = [];
    if (privileges.length > 0 && confidentials.length > 0) {
      for (let i = 0; i <= confidentials.length - 1; i++) {
        for (let j = 0; j <= privileges.length - 1; j++) {
          if (!confidentials[i]?.name) {
            documents.push(confidentials[i]);
          } else if (confidentials[i]?.name === privileges[j]?.name) {
            documents.push(confidentials[i]);
          }
        }
      }
    }
    return documents;
  }

  mapKeyToString = (enumType: any, key: string) => enumType[key as keyof typeof enumType];

  getEnumKeyByValue = (_enum: any, _enumVal: string) => Object.keys(_enum)[Object.values(_enum).indexOf(_enumVal)];

  removeDuplicateByKey(_arr: any[], _key: string) {
    return [...new Map(_arr.map((_arrItem) => [_arrItem[_key], _arrItem])).values()];
  }

  getBrowserName() {
    const agent = window.navigator.userAgent.toLowerCase();
    switch (true) {
      case agent.indexOf('edge') > -1:
        return 'edge';
      case agent.indexOf('opr') > -1 && !!(<any>window).opr:
        return 'opera';
      case agent.indexOf('chrome') > -1 && !!(<any>window).chrome:
        return 'chrome';
      case agent.indexOf('trident') > -1:
        return 'ie';
      case agent.indexOf('firefox') > -1:
        return 'firefox';
      case agent.indexOf('safari') > -1:
        return 'safari';
      default:
        return 'other';
    }
  }

  renderStringList = (arr: any, key: string = '', show?: number) => {
    try {
      arr = arr.length > (show ? show : arr.length) ? arr.slice(0, show) : arr;
      return this.arrayStringJoin(arr, key, ',', true);
    } catch {
      return;
    }
  };

  reloadPage() {
    window.location.reload();
  }

  calculateDateDiff(date: string) {
    let currentDate = new Date();
    let _date = new Date(date);

    return Math.floor(
      (Date.UTC(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate()) - Date.UTC(_date.getFullYear(), _date.getMonth(), _date.getDate())) /
        (1000 * 60 * 60 * 24)
    );
  }

  generateUpdateValue(data: any, newData: any) {
    let _result: any = [];

    data.map((val: any) => {
      let _objectValue: { key: string; title?: string; label: string; value: string; new?: string } = {
        key: val.key,
        label: val.label,
        value: val.value,
      };

      // if data item has title parameter
      if (val.hasOwnProperty('title')) {
        _objectValue['title'] = val.title;
      }

      if (newData.hasOwnProperty(val.key) && typeof val.key === 'string') {
        if (val.value !== newData[val.key]) _objectValue['new'] = newData[val.key];
      } else if (Array.isArray(val.key)) {
        const _arrayValKey = val.key.filter((_val: string) => {
          return newData.hasOwnProperty(_val);
        });

        if (_arrayValKey.length) {
          let _valueNew: string[] = [];
          let splitKeyArray = _objectValue.value.split(', ');

          const haveNewValue = _arrayValKey.find((_val: string) => {
            if (newData[_val]) {
              return true;
            }
          });

          if (haveNewValue) {
            val.key.map((_key: string, i: number) => {
              _valueNew.push(newData[_key] ?? splitKeyArray[i]);
            });
          }

          const _tempNewObj = _valueNew.join(', ');

          if (val.value !== _tempNewObj) {
            _objectValue['new'] = _valueNew.join(', ');
          }
        }
      }

      _result.push(_objectValue);
    });

    return _result;
  }

  generateUpdateEditData(oldData: ICardDetailBody[], newData: ICardDetailBody[]) {
    let _result: ICardDetailBody[] = [];

    oldData.map((_odItem: ICardDetailBody, index: number) => {
      const { key, label, value } = _odItem;

      let _objectValue: ICardDetailBody = {
        key,
        label,
        value,
      };

      // if data item has title parameter
      if (_odItem.hasOwnProperty('title')) {
        _objectValue['title'] = _odItem.title;
      }

      if (!Array.isArray(value) && newData[index].value !== value) {
        _objectValue.new = newData[index].value;
      }

      _result.push(_objectValue);
    });

    return _result;
  }

  generateSideDocument(_key: string, newData: any, data: any) {
    let _result: { key: string; value: string }[] = [];
    _result.push(data);
    if (newData.hasOwnProperty(_key) && newData[_key] && newData[_key] !== data.value) {
      let sideDocument = { key: 'new', value: newData[_key] };
      _result.push(sideDocument);
    }
    return _result;
  }

  checkInHasNewValue(hasValue: boolean) {
    if (hasValue) {
      return 'border border-primary';
    } else {
      return '';
    }
  }

  isArrayObjIdentical(_arr1: any[], _arr2: any[]) {
    if (_arr1.length !== _arr2.length) {
      return false;
    }

    return _arr1.every((item, index) => {
      return Object.keys(item).every((objItem) => {
        return _arr2[index][objItem] === item[objItem];
      });
    });
  }

  copyToClipboard(data: string) {
    return navigator.clipboard.writeText(data);
  }

  filterStringArray(array: string[], filter: string): string[] {
    return array.filter((item) => item.includes(filter));
  }

  countDays(startDate: Date, endDate: Date): number {
    const timeDifference = endDate.getTime() - startDate.getTime();
    const daysDifference = timeDifference / (1000 * 60 * 60 * 24);
    return Math.floor(daysDifference) + 1; // Including both start and end dates
  }

  setDefaultImageProduct(e: Event) {
    let _target = e.target as HTMLImageElement;
    _target.src = STRING_CONSTANTS.ILLUSTRATIONS.DEFAULT_IMAGE;
    return _target;
  }

  otherToggleSelection(selectedList: string[], e: any): boolean {
    let idx = selectedList.indexOf(e.target.value);
    // Is currently selected
    if (idx > -1) {
      selectedList.splice(idx, 1);
      return e.target.value !== 'OTHER';
    }
    // Is newly selected
    else {
      selectedList.push(e.target.value);
      return e.target.value === 'OTHER';
    }
  }

  dateStringToEpoch(date: string) {
    return Date.parse(date) * 1000;
  }

  formatEpochToDate(epoch: number, _format: string = 'dd-MM-yyyy') {
    if (!epoch) return '';
    const date = new Date(epoch);

    //temp.quick convert using intl date
    if (_format === 'intl') {
      return new Intl.DateTimeFormat('id', {
        day: 'numeric',
        month: 'long',
        year: 'numeric',
        timeZone: 'Asia/Jakarta',
      }).format(date);
    }

    return format(date, _format);
  }

  formatEpochTime(epoch: number, needPlus7 = false) {
    let date = new Date(epoch);
    if (needPlus7) date.setTime(date.getTime() + 7 * 60 * 60 * 1000);
    return new Intl.DateTimeFormat('en', {
      timeZone: 'Asia/jakarta',
      hourCycle: 'h24',
      hour: 'numeric',
      minute: 'numeric',
      second: 'numeric',
    }).format(date);
  }

  stringNumberToPayload = (value: string) => {
    return !!value ? parseInt(value.toString().replaceAll('.', '')) : 0;
  };

  switchDotAndComma(str: string) {
    // input: 12,345.67 |
    // expected output: 12.345,67
    if (!str) return '0';
    const switcher = (match: string) => (match == ',' ? '.' : ',');
    return str.toString().replaceAll(/\.|\,/g, switcher);
  }

  switchDotToComma(str: string) {
    return str.replaceAll('.', ',');
  }

  switchCommaToDot(str: string) {
    return str.replaceAll(',', '.');
  }

  convertStringToNumberWithComma(input: string): number {
    // Replace the comma with a dot
    const normalizedString = this.switchCommaToDot(input);
    // Return the resulting number
    return parseFloat(normalizedString);
  }

  objectMapToArray(obj: Object) {
    if (!Object.keys(obj).length) return [];
    return Object.entries(obj).map(([key, value]) => {
      return {
        [key]: value,
      };
    });
  }

  isObjectEmpty(obj: Record<string, any>) {
    if (!obj || Object.keys(obj).length === 0) return true;
    return Object.values(obj).every((value) => value === null || value === '' || value === undefined || value === false || value === 0 || value === 0.0);
  }

  getTypeDoc(doc: string) {
    if (!doc) return undefined;
    const regex = /\bfile\b/;
    return doc.match(regex) ? 'FILE' : 'IMAGE';
  }
}
