![Logo](https://staging.back-office.maxxiagri.com/assets/media/logos/default.svg)

# MAI BACKOFFICE

> **Atom Backoffice Data** - Dashboard Sistem Manajemen Konten (CMS) yang dirancang untuk menyederhanakan dan memfasilitasi pengelolaan konten bagi para administrator di ekosistem Maxxi Agro.

## 🚀 Quick Start

**For experienced developers:**
```bash
<NAME_EMAIL>:maxxi-agro/atom.git
cd atom
npm install
ng serve
# Open http://localhost:4200
```

**New to the project?** Follow the [complete setup guide](#getting-started) below.

## 📋 Project Overview

**MAI Backoffice** adalah dashboard CMS yang menjadi pusat kontrol untuk pengelolaan konten di ekosistem Maxxi Agro, dengan fokus pada:

### **🎯 Core Features:**
- **📊 Content Management** - Pengelolaan konten terpusat dengan antarmuka yang user-friendly
- **🔗 Cross-Platform Integration** - Integrasi dengan platform web Distributor dan aplikasi Retailer (MM+)
- **👥 Multi-User Management** - Sistem manajemen pengguna dengan role-based access

### **🏗️ Tech Stack:**
- **Frontend**: Angular 15+ dengan TypeScript
- **UI Framework**: Angular Material + Bootstrap
- **State Management**: RxJS + Services
- **Build Tool**: Angular CLI + Webpack
- **Deployment**: Vercel dengan multi-branch strategy

### **🌐 Environment Strategy:**
```
development → development-second → staging → main (production)
     ↓              ↓                ↓         ↓
  Auto-sync    Preview Deploy   Staging Test  Production
```

<!-- START doctoc generated TOC please keep comment here to allow auto update -->
<!-- DON'T EDIT THIS SECTION, INSTEAD RE-RUN doctoc TO UPDATE -->
# Table of Contents

- [Quick Start](#quick-start)
- [Project Overview](#project-overview)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Installation](#installation)
  - [Environment Setup](#environment-setup)
  - [Running the Application](#running-the-application)
- [Development Workflow](#development-workflow)
  - [Branch Strategy](#branch-strategy)
  - [Feature Development](#feature-development)
  - [Code Review Process](#code-review-process)
- [Architecture Overview](#architecture-overview)
  - [Project Structure](#project-structure)
  - [Key Components](#key-components)
  - [Services & State Management](#services--state-management)
- [Testing](#testing)
  - [Running Tests](#running-tests)
  - [Writing Tests](#writing-tests)
  - [Test Coverage](#test-coverage)
- [Deployment](#deployment)
  - [Vercel Integration](#vercel-integration)
  - [Branch Synchronization](#branch-synchronization)
- [Release Management](#release-management)
  - [Release Process](#release-process)
  - [Versioning Strategy](#versioning-strategy)
  - [Changelog Management](#changelog-management)
  - [Hotfix Workflow](#hotfix-workflow)
  - [Rollback Procedures](#rollback-procedures)
- [Contributing Guidelines](#contributing-guidelines)
  - [Coding Styleguide](#coding-styleguide)
  - [Commit Message](#commit-message)
- [Troubleshooting](#troubleshooting)
  - [Common Issues](#common-issues)
  - [Development Problems](#development-problems)
  - [Build & Deployment Issues](#build--deployment-issues)
- [Resources & References](#resources--references)
- [Contributors](#contributors)

<!-- END doctoc generated TOC please keep comment here to allow auto update -->


# Getting Started

Selamat datang di **MAI Backoffice**! 🎉

Panduan ini akan membantu kamu setup development environment dan mulai berkontribusi dalam proyek ini. Ikuti langkah-langkah berikut untuk memulai journey development kamu.

## Prerequisites

Sebelum memulai, pastikan sistem kamu sudah memiliki tools berikut:

### **📋 Required Software:**
- **Node.js**: Version 18.x atau 20.x (recommended: 20.x untuk npm 9+ support)
  - Download dari [nodejs.org](https://nodejs.org)
  - Verify: `node --version` dan `npm --version`
- **Git**: Untuk version control
  - Download dari [git-scm.com](https://git-scm.com)
  - Verify: `git --version`
- **Code Editor**: VS Code (recommended) dengan Angular extensions

### **🔑 Access Requirements:**
- **GitLab Account** dengan akses ke repository `maxxi-agro/atom`
- **SSH Key** configured untuk GitLab (recommended untuk cloning)
- **Vercel Account** (optional, untuk deployment monitoring)

### **⚙️ Recommended VS Code Extensions:**
```
- Angular Language Service
- TypeScript Importer
- Prettier - Code formatter
- ESLint
- GitLens
- Angular Snippets
```

## Installation

### **1. Clone Repository**

```bash
# Using SSH (recommended)
<NAME_EMAIL>:maxxi-agro/atom.git

# Using HTTPS (alternative)
git clone https://gitlab.com/maxxi-agro/atom.git

# Navigate to project directory
cd atom
```

### **2. Install Dependencies**

```bash
# Install all project dependencies
npm install

# Verify installation
npm list --depth=0
```

### **3. Install Angular CLI**

```bash
# Install Angular CLI globally (if not already installed)
npm install -g @angular/cli@15.2.9

# Verify installation
ng version
```

## Environment Setup

### **🔧 Environment Variables**

Create environment configuration files (if not exists):

```bash
# Development environment
cp src/environments/environment.ts.example src/environments/environment.ts

# Production environment
cp src/environments/environment.prod.ts.example src/environments/environment.prod.ts
```

**Note**: Environment files contain sensitive configuration. Never commit actual API keys or secrets to repository.

### **🌐 API Configuration**

Configure API endpoints in environment files:

```typescript
// src/environments/environment.ts
export const environment = {
  production: false,
  apiUrl: 'https://api-dev.maxxiagri.com',
  // Add other configuration as needed
};
```

## Running the Application

### **🚀 Development Server**

```bash
# Start development server
ng serve

# With specific port
ng serve --port 4200

# With host binding (for network access)
ng serve --host 0.0.0.0

# Open browser automatically
ng serve --open
```

**Application will be available at:**
- **Local**: `http://localhost:4200`
- **Network**: `http://[your-ip]:4200` (if using --host 0.0.0.0)

### **🔄 Live Reload**

The application supports **hot reload** - changes to source files will automatically:
- ✅ Recompile the application
- ✅ Refresh the browser
- ✅ Preserve application state (when possible)

### **🏗️ Build Commands**

```bash
# Development build
ng build

# Production build
ng build --configuration production

# Build with source maps
ng build --source-map

# Analyze bundle size
ng build --stats-json
npx webpack-bundle-analyzer dist/atom/stats.json
```

### **✅ Verify Installation**

After running `ng serve`, verify everything works:

1. **✅ Application loads** - No console errors
2. **✅ Navigation works** - Can navigate between pages
3. **✅ API connectivity** - Check network tab for API calls
4. **✅ Hot reload** - Make a small change and see it update

**🎉 Success!** You're ready to start developing!

# Development Workflow

## Branch Strategy

Proyek ini menggunakan **multi-branch strategy** dengan automated synchronization untuk memastikan deployment yang aman dan terkontrol.

### **🌿 Branch Structure:**

```
main (production)
 ↑
staging (pre-production)
 ↑
development (main development)
 ↓
development-second (auto-sync preview)
```

### **🔄 Branch Flow:**

| Branch | Purpose | Deployment | Sync Type |
|--------|---------|------------|-----------|
| `development` | Main development branch | Manual | Source |
| `development-second` | Auto-sync preview | Auto | **Automatic** |
| `staging` | Pre-production testing | Manual | **Manual** |
| `main` | Production | Auto | **Manual** |

### **📋 Branch Rules:**

- **✅ development**: Direct commits allowed, main development happens here
- **⚠️ development-second**: Auto-synced, no direct commits
- **⚠️ staging**: Manual sync only, for testing before production
- **🔒 main**: Manual sync only, production deployment

## Feature Development

### **🚀 Standard Workflow:**

```bash
# 1. Start from development branch
git checkout development
git pull origin development

# 2. Create feature branch
git checkout -b feat/your-feature-name

# 3. Develop your feature
# ... make changes ...
git add .
git commit -m "FEAT: implement your feature"

# 4. Push feature branch
git push origin feat/your-feature-name

# 5. Create Merge Request
# → Go to GitLab and create MR: feat/your-feature-name → development
```

### **🔄 After MR Merged:**

```
1. ✅ MR merged to development
2. 🤖 Auto-sync: development → development-second (immediate)
3. 🌐 Vercel preview deployment available
4. ⚠️ Manual sync to staging (when ready for testing)
5. ⚠️ Manual sync to production (when staging verified)
```

## Code Review Process

### **📋 MR Requirements:**

- **✅ Clear title** - Describe what the MR does
- **✅ Description** - Explain changes and reasoning
- **✅ Link issues** - Reference related tickets (MAI-xxx)
- **✅ Screenshots** - For UI changes
- **✅ Testing notes** - How to test the changes

### **🔍 Review Checklist:**

**For Reviewers:**
- ✅ Code follows [coding styleguide](#coding-styleguide)
- ✅ No console errors or warnings
- ✅ Responsive design works
- ✅ Performance impact considered
- ✅ Security implications reviewed

**For Authors: <AUTHORS>
- ✅ Self-review completed
- ✅ Tests added/updated
- ✅ Documentation updated
- ✅ No merge conflicts
- ✅ CI pipeline passes

# Architecture Overview

## Project Structure

```
src/
├── app/                          # Main application code
│   ├── _metronic/               # Metronic theme framework
│   │   ├── kt/                  # Metronic utilities
│   │   ├── layout/              # Layout components
│   │   └── partials/            # Reusable UI partials
│   ├── config/                  # Application configuration
│   │   ├── api/                 # API endpoints configuration
│   │   ├── constants/           # Application constants
│   │   └── enum/                # Enums and types
│   ├── guards/                  # Route guards (auth, role)
│   ├── models/                  # TypeScript interfaces/models
│   ├── services/                # Application services
│   ├── directives/              # Custom directives
│   ├── pipes/                   # Custom pipes
│   ├── modules/                 # Core modules (auth, errors, i18n)
│   ├── pages/                   # Feature pages (lazy-loaded)
│   │   ├── admin-cms/           # CMS administration
│   │   ├── distributor/         # Distributor management
│   │   ├── products/            # Product management
│   │   ├── purchase-order/      # Purchase order management
│   │   ├── sales-order/         # Sales order management
│   │   ├── retailer/            # Retailer management
│   │   ├── master-data/         # Master data management
│   │   └── report/              # Reporting module
│   ├── shared/                  # Shared components and utilities
│   │   ├── base/                # Base classes and models
│   │   ├── components/          # Reusable UI components
│   │   └── interface/           # Shared interfaces
│   └── app.module.ts           # Root module
├── assets/                      # Static assets
│   ├── fonts/                   # Font files
│   ├── media/                   # Images, icons, logos
│   └── sass/                    # SASS stylesheets
├── environments/                # Environment configurations
└── styles.scss                 # Global styles
```

### **📁 Key Directories Explained:**

| Directory | Purpose | Examples |
|-----------|---------|----------|
| `_metronic/` | UI framework components | Layout, partials, utilities |
| `config/` | App configuration | API endpoints, constants, enums |
| `guards/` | Route protection | Authentication, role-based access |
| `models/` | Data models | User, product, order interfaces |
| `services/` | Business logic | API calls, data processing |
| `pages/` | Feature modules | Distributor, products, orders |
| `shared/` | Reusable code | Components, base classes, interfaces |
| `modules/` | Core modules | Authentication, error handling |

## Key Components

### **🏗️ Architecture Patterns:**

- **📱 Page-Based Structure** - Features organized by business domain
- **🔄 Service-Oriented** - Business logic in injectable services
- **🛣️ Module-Based** - Lazy-loaded feature modules for performance
- **📡 Reactive** - RxJS for async operations and state management
- **🎨 Theme Integration** - Metronic framework for consistent UI

### **🔧 Key Services:**

| Service | Purpose | Location |
|---------|---------|----------|
| `AuthService` | Authentication & authorization | `services/` |
| `BaseService` | Base HTTP service functionality | `services/` |
| `PermissionService` | Role-based access control | `services/` |
| `UtilitiesService` | Common utility functions | `services/` |
| `FileService` | File upload/download operations | `services/` |
| `GoogleMapsService` | Maps integration | `services/` |

## Services & State Management

### **📡 HTTP Communication:**

```typescript
// Example service usage with BaseService
@Injectable()
export class DistributorService extends BaseService {
  constructor(http: HttpClient) {
    super(http);
  }

  getDistributors(): Observable<any> {
    return this.get('/distributors');
  }

  createDistributor(data: any): Observable<any> {
    return this.post('/distributors', data);
  }
}
```

### **🔄 State Management Pattern:**

- **Service-Based State**: Business logic in dedicated services
- **Component State**: Local state management with RxJS
- **Base Classes**: Shared functionality via base components
- **Form State**: Angular Reactive Forms with custom validators
- **Table State**: Pagination and filtering via base table service

### **🛡️ Security Implementation:**

- **JWT Authentication**: Token-based auth with AuthService
- **Route Guards**: AuthGuard and RoleGuard for access control
- **HTTP Interceptors**: Automatic token attachment via AuthInterceptor
- **Permission System**: Role-based access via PermissionService
- **Base Response**: Standardized API response handling

# Testing

### **🚀 Coming Soon:**

Testing guidelines dan comprehensive testing strategy untuk proyek ini sedang dalam pengembangan dan akan ditambahkan di masa mendatang.

# Deployment

## Vercel Integration

Proyek ini menggunakan **Vercel** untuk deployment dengan automated pipeline yang terintegrasi dengan GitLab CI.

### **🌐 Deployment Strategy:**

| Branch | Environment | URL | Deployment |
|--------|-------------|-----|------------|
| `development` | Development | [backoffice-atomdev.vercel.app](https://backoffice-atomdev.vercel.app/) | Auto |
| `development-second` | Preview | [backoffice-atomdev2.vercel.app](https://backoffice-atomdev2.vercel.app/) | Auto |
| `staging` | Staging | [staging.back-office.maxxiagri.com](https://staging.back-office.maxxiagri.com/) | Auto |
| `main` | Production | [back-office.maxxiagri.com](https://back-office.maxxiagri.com/) | Auto |

### **🔄 Deployment Flow:**

```
GitLab CI → Branch Sync → Vercel Webhook → Deployment
```

## Branch Synchronization

### **🔄 Automated Sync:**

Proyek menggunakan GitLab CI untuk automated branch synchronization. Untuk detail lengkap, lihat [Branch Sync Documentation](docs/BRANCH_SYNC_SETUP.md).

**Quick Reference:**
- **✅ Auto**: `development` → `development-second`
- **⚠️ Manual**: `development` → `staging`
- **⚠️ Manual**: `staging` → `main`
- **🚨 Emergency**: `development-second` → `development` (reverse sync)

### **🔄 Reverse Sync (Parallel Development & Emergency)**

For parallel development coordination and critical situations where updates in `development-second` need to be synced back to `development`:

**Primary use cases:**
- **👥 Parallel Development** - Multi-team development coordination
- **🔧 Shared Components** - Enhanced components needed by other teams
- **📦 Feature Integration** - Cross-team feature collaboration
- **🚨 Emergency Fixes** - Critical hotfixes applied to development-second

**Common scenario:**
```
Team A (development): Feature A + needs shared component updates
Team B (development-second): Feature B + enhanced shared components
→ Reverse sync to coordinate features
```

**How to trigger:**
1. Go to GitLab CI Pipeline for development-second branch
2. Click ▶️ on `reverse-sync-dev-second-to-dev` job
3. Monitor execution and coordinate with teams
4. Verify auto-sync propagation

**Safety features:**
- ✅ Cherry-pick approach (selective commit sync)
- ✅ Conflict detection and abort
- ✅ Audit trail of synced commits
- ✅ Team coordination support

# Release Management

## Release Process

Proyek ini menggunakan **release branch strategy** dengan `release-it` tool untuk versioning, changelog generation, dan coordinated deployment ke semua environment branches.

### **🚀 Standard Release Flow:**

```bash
# 1. Create release branch from development
git checkout development
git pull origin development
git checkout -b release/v1.x.x

# 2. Run release process
npm run release
# Follow interactive prompts:
#    - Select version bump (patch/minor/major)
#    - Confirm changelog generation
#    - Confirm git commit and push

# 3. Push release branch
git push origin release/v1.x.x

# 4. Merge release back to development (bottom-up flow):
#    - Create MR: release/v1.x.x → development
#    - After merge, existing branch sync system handles the rest:
#      • development → development-second (automatic)
#      • development → staging (manual trigger in GitLab CI)
#      • staging → main (manual trigger in GitLab CI)

# 5. After release deployed to production, delete release branch
git branch -d release/v1.x.x
git push origin --delete release/v1.x.x
```

### **🔄 Release Flow Benefits:**

**Leveraging Existing Branch Sync System:**
- ✅ **Consistent Direction** - Always bottom-up flow (development → staging → main)
- ✅ **Proven System** - Uses existing branch-sync.yml that's already tested
- ✅ **Single MR** - Only need to merge release branch to development
- ✅ **Automated Propagation** - Existing GitLab CI handles the rest
- ✅ **Manual Control** - Staging and production sync remain manual for safety

**Flow Visualization:**
```
release/v1.x.x → development → development-second (auto)
                      ↓
                   staging (manual) → main (manual)
```

### **⚙️ What Happens During Release:**

1. **📋 Pre-release**: Build scripts and prepare environment
2. **🔢 Version Bump**: Update version in:
   - `package.json`
   - `src/environments/environment.ts`
   - `src/environments/environment.dev.ts`
   - `src/environments/environment.staging.ts`
   - `src/environments/environment.prod.ts`
3. **📝 Changelog Generation**: Auto-generate from conventional commits
4. **📦 Git Operations**: Commit changes with `"CHORE: Release v${version}"`
5. **🚀 Push & Deploy**: Push to repository, trigger deployment
6. **📁 Post-release**: Archive old changelog entries (keep 6 months recent)

## Versioning Strategy

### **📊 Semantic Versioning:**

Menggunakan [Semantic Versioning](https://semver.org/) format: `MAJOR.MINOR.PATCH`

- **PATCH** (1.13.3 → 1.13.4): Bug fixes, small improvements
- **MINOR** (1.13.3 → 1.14.0): New features, backward compatible
- **MAJOR** (1.13.3 → 2.0.0): Breaking changes, major updates

### **🏷️ Version Selection Guide:**

```bash
# Bug fixes, documentation updates
npm run release -- patch

# New features, enhancements
npm run release -- minor

# Breaking changes, major refactoring
npm run release -- major
```

## Changelog Management

### **📝 Automatic Generation:**

Changelog di-generate otomatis dari commit messages menggunakan [Conventional Commits](https://www.conventionalcommits.org/):

**Supported commit types:**
- **FEAT**: New features → "Features:" section
- **FIX**: Bug fixes → "Bug Fixes:" section
- **CHORE**: Maintenance tasks → Not included in changelog
- **DOCS**: Documentation → Not included in changelog

### **📁 Archive System:**

- **Recent entries**: Kept in main `CHANGELOG.md` (last 6 months)
- **Archived entries**: Moved to `docs/CHANGELOG-{YEAR}.md`
- **Auto-archiving**: Runs after each release

## Hotfix Workflow

### **🚨 Emergency Fixes:**

For critical production issues that need immediate deployment:

```bash
# 1. Create hotfix branch from main
git checkout main
git pull origin main
git checkout -b hotfix/critical-fix-name

# 2. Implement fix
# ... make necessary changes ...
git add .
git commit -m "FIX: critical issue description"

# 3. Create MR to main
git push origin hotfix/critical-fix-name
# → Create MR: hotfix/critical-fix-name → main

# 4. After MR merged to main:
#    - Create MRs to sync fix to all other branches:
#      - main → staging
#      - main → development
#      - main → development-second
```

### **⚠️ Hotfix Best Practices:**

- **✅ Test thoroughly** - Even for urgent fixes
- **✅ Minimal changes** - Only fix the critical issue
- **✅ Document impact** - Clear description in MR
- **✅ Sync to all branches** - Ensure consistency across environments
- **✅ Follow-up release** - Create proper release after hotfix

## Rollback Procedures

### **🔄 Rollback Options:**

#### **1. Git-based Rollback (Recommended):**
```bash
# Revert specific commit
git revert <commit-hash>
git push origin main

# Revert release commit
git revert HEAD
git push origin main
```

#### **2. Vercel Rollback (Fastest):**
```bash
# Via Vercel Dashboard:
# 1. Go to Vercel project dashboard
# 2. Navigate to Deployments
# 3. Find previous stable deployment
# 4. Click "Promote to Production"
```

#### **3. Branch Rollback (Last Resort):**
```bash
# Reset to previous stable state
git reset --hard <previous-stable-commit>
git push --force origin main  # ⚠️ Use with extreme caution
```

### **🛡️ Rollback Safety:**

- **✅ Always backup** current state before rollback
- **✅ Communicate** with team before major rollbacks
- **✅ Test rollback** in staging environment first
- **✅ Document** rollback reason and impact

Sebelum kamu mulai berkontribusi, pastikan kita semua menjalankan kode dengan konsisten dan mengikuti panduan yang telah disepakati untuk menjaga kualitas proyek.

## Coding Styleguide

### **📋 Core Principles:**

- **✅ Single Responsibility** - Setiap komponen/service memiliki satu tujuan utama
- **✅ Consistent Naming** - Gunakan naming convention yang konsisten
- **✅ Clean Code** - Kode mudah dibaca dan dipahami
- **✅ Modular Structure** - Pecah kode menjadi bagian-bagian kecil

### **📁 File Structure Guidelines:**

```typescript
// ✅ Good: File size manageable
export class UserService {
  // Max 75 lines per method
  getUserData() { ... }
}

// ❌ Avoid: File too large (>500 lines)
// Consider splitting into multiple files
```

**Rules:**
- **Max 500 lines** per file
- **Max 75 lines** per method/function
- **Refactor** when code becomes too complex

### **🏷️ Naming Conventions:**

| Type | Convention | Example |
|------|------------|---------|
| **Components** | PascalCase + Component | `UserListComponent` |
| **Services** | PascalCase + Service | `AuthService` |
| **Files** | kebab-case + type | `user-list.component.ts` |
| **Variables** | camelCase | `userData`, `isLoading` |
| **Constants** | UPPER_SNAKE_CASE | `API_BASE_URL` |

### **📚 References:**

- **[Angular Style Guide](https://angular.io/guide/styleguide)** - Official Angular coding standards
- **[TypeScript Handbook](https://www.typescriptlang.org/docs/)** - TypeScript best practices

## Commit Message

### **📝 Format:**

```
<type>: <description> [ticket-reference]
```

### **🏷️ Commit Types:**

| Type | Purpose | Example |
|------|---------|---------|
| **FEAT** | New features | `FEAT: add user authentication` |
| **FIX** | Bug fixes | `FIX: resolve login validation error` |
| **CHORE** | Maintenance tasks | `CHORE: update dependencies` |
| **REFACTOR** | Code improvements | `REFACTOR: optimize user service` |
| **STYLE** | UI/styling changes | `STYLE: update button colors` |
| **BUILD** | Build system changes | `BUILD: configure webpack` |

### **✅ Good Examples:**

```bash
FEAT: implement user profile management MAI-123
FIX: resolve memory leak in dashboard component
CHORE: update Angular to version 16
REFACTOR: extract common validation logic
```

### **❌ Avoid:**

```bash
# Too vague
update stuff
fix bug

# Missing type
add new feature for users
```

### **📚 Reference:**

- **[Conventional Commits](https://www.conventionalcommits.org/)** - Official specification

# Troubleshooting

## Common Issues

### **🚫 Installation Problems:**

**Node.js Version Issues:**
```bash
# Check Node version
node --version

# If version < 18, update Node.js
# Download from: https://nodejs.org

# Clear npm cache if needed
npm cache clean --force
```

**Dependency Installation Fails:**
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# If still fails, try with legacy peer deps
npm install --legacy-peer-deps
```

## Development Problems

### **🔧 Build Errors:**

**TypeScript Compilation Errors:**
```bash
# Check TypeScript version compatibility
ng version

# Update Angular CLI if needed
npm update -g @angular/cli

# Clear Angular cache
ng cache clean
```

**Memory Issues:**
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=8192"
ng serve

# Or add to package.json scripts:
"serve": "node --max-old-space-size=8192 ./node_modules/@angular/cli/bin/ng serve"
```

### **🌐 Runtime Issues:**

**API Connection Problems:**
```bash
# Check environment configuration
cat src/environments/environment.ts

# Verify API endpoint accessibility
curl -I https://api.maxxiagri.com/health

# Check browser console for CORS errors
```

**Authentication Issues:**
```bash
# Clear browser storage
localStorage.clear()
sessionStorage.clear()

# Check JWT token validity
# Use browser dev tools → Application → Local Storage
```

## Build & Deployment Issues

### **📦 Build Failures:**

**Production Build Errors:**
```bash
# Build with verbose output
ng build --verbose

# Check for unused imports
ng lint

# Analyze bundle size
ng build --stats-json
npx webpack-bundle-analyzer dist/atom/stats.json
```

**Vercel Deployment Issues:**
```bash
# Check Vercel build logs
# Go to: Vercel Dashboard → Project → Deployments → View Logs

# Common fixes:
# 1. Check environment variables
# 2. Verify build command in vercel.json
# 3. Check Node.js version compatibility
```

### **🔄 Branch Sync Issues:**

For branch synchronization problems, refer to [Branch Sync Troubleshooting](docs/BRANCH_SYNC_SETUP.md#troubleshooting).

**Quick fixes:**
```bash
# Check GitLab CI pipeline status
# Go to: GitLab → CI/CD → Pipelines

# Manual sync if CI fails
./scripts/sync-branches.sh --mode=dev-only

# Verify branch status
git fetch origin
git status
```

# Resources & References

## 📚 Documentation

- **[Angular Documentation](https://angular.io/docs)** - Official Angular guide
- **[Angular Material](https://material.angular.io/)** - UI component library
- **[TypeScript Handbook](https://www.typescriptlang.org/docs/)** - TypeScript reference
- **[RxJS Documentation](https://rxjs.dev/)** - Reactive programming guide

## 🛠️ Development Tools

- **[Angular CLI](https://angular.io/cli)** - Command line interface
- **[Angular DevTools](https://angular.io/guide/devtools)** - Browser extension
- **[VS Code Angular Extensions](https://marketplace.visualstudio.com/search?term=angular&target=VSCode)** - Development extensions

## 🔗 Project-Specific

- **[Branch Sync Setup](docs/BRANCH_SYNC_SETUP.md)** - Branch synchronization guide
- **[Conventional Commits](https://www.conventionalcommits.org/)** - Commit message format
- **[GitLab CI/CD](https://docs.gitlab.com/ee/ci/)** - Pipeline documentation

## 🎯 Best Practices

- **[Angular Style Guide](https://angular.io/guide/styleguide)** - Official coding standards
- **[Angular Security](https://angular.io/guide/security)** - Security best practices
- **[Performance Guide](https://angular.io/guide/performance-checklist)** - Optimization techniques

# Contributors

Terima kasih kepada semua developer yang telah berkontribusi dalam proyek **MAI Backoffice**:

## 👥 Core Team

- **[@dika14](https://gitlab.com/dika14)**
- **[@rivopelu12](https://gitlab.com/rivopelu12)**
- **[@julio70](https://gitlab.com/julio70)**
- **[@legamukad](https://gitlab.com/legamukad)**
- **[@abdullah31](https://gitlab.com/abdullah31)**
- **[@wahyunianti2002](https://gitlab.com/wahyunianti2002)**

## 🤝 How to Contribute

Interested in contributing? We welcome contributions from the team! Here's how to get started:

1. **📖 Read this README** - Understand the project structure and workflow
2. **🔧 Setup development environment** - Follow the [Getting Started](#getting-started) guide
3. **🌿 Create feature branch** - Follow our [branch strategy](#branch-strategy)
4. **💻 Write quality code** - Follow our [coding guidelines](#coding-styleguide)
5. **🔍 Submit for review** - Create merge request with clear description
6. **🚀 Deploy safely** - Use our [deployment workflow](#deployment)

## 📞 Getting Help

- **💬 Team Chat** - Contact any [core team member](#core-team)
- **📋 Issues** - Create GitLab issue for bugs or feature requests
- **📚 Documentation** - Check [resources & references](#resources--references)
- **🔧 Troubleshooting** - See [troubleshooting guide](#troubleshooting)

---

## 🎉 Thank You!

Terima kasih sudah bergabung dengan tim **MAI Backoffice**!

Proyek ini berkembang berkat kontribusi dan dedikasi dari setiap anggota tim. Mari kita terus berinovasi dan membangun solusi terbaik untuk ekosistem Maxxi Agro.

**Happy Coding!** 🚀

---

*Last updated: June 2025 | Version: 1.13.3*
